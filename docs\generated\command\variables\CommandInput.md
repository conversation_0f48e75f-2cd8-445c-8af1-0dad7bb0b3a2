[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [command](../README.md) / CommandInput

# Variable: CommandInput

> `const` **CommandInput**: `ForwardRefExoticComponent`\<`Omit`\<`Omit`\<`Pick`\<`Pick`\<`DetailedHTMLProps`\<`InputHTMLAttributes`\<`HTMLInputElement`\>, `HTMLInputElement`\>, `"key"` \| keyof InputHTMLAttributes\<HTMLInputElement\>\> & `object` & `object`, `"key"` \| `"asChild"` \| keyof InputHTMLAttributes\<HTMLInputElement\>\>, `"onChange"` \| `"value"` \| `"type"`\> & `object` & `RefAttributes`\<`HTMLInputElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLInputElement`\>\>

Defined in: [src/components/ui/command.tsx:52](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/command.tsx#L52)
