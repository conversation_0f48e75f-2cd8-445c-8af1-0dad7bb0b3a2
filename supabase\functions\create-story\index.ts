/// supabase/functions/create-story/index.ts
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_ANON_KEY');
}

interface StoryInput {
  title: string;
  is_public?: boolean;
  max_contributors?: number;
  max_words_per_contribution?: number;
  genre?: string;
  cover_image_url?: string;
  // author_id will be set from authenticated user
  // status defaults to 'in_progress' in DB
  // current_word_count defaults to 0 in DB
}

async function getAuthenticatedUser(req: Request, supabase: SupabaseClient) {
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error || !user) {
    console.error('Auth error or no user:', error);
    throw { message: 'User not authenticated or auth error', status: 401 };
  }
  return user;
}

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  let supabaseClient: SupabaseClient;

  try {
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
     if (!serviceRoleKey) throw new Error('SUPABASE_SERVICE_ROLE_KEY not found.');
    // Initialize Supabase client with the service role key for auth check,
    // then re-initialize with user's auth header for actual DB operation to respect RLS.
    // Or, pass user's auth token to a client initialized with anon key.
    const tempSupabase = createClient(supabaseUrl!, supabaseAnonKey!, {
        global: { headers: { Authorization: req.headers.get('Authorization')! } },
        auth: { persistSession: false }
    });
    const user = await getAuthenticatedUser(req, tempSupabase);


    // Re-initialize with the user's token to ensure RLS policies are applied for the insert
    // This is crucial. The previous client was just for auth check.
    supabaseClient = createClient(supabaseUrl!, supabaseAnonKey!, {
        global: { headers: { Authorization: req.headers.get('Authorization')! } },
        auth: { persistSession: false } // Recommended for server-side
    });


    const body: StoryInput = await req.json();

    if (!body.title || typeof body.title !== 'string' || body.title.trim() === '') {
      return new Response(JSON.stringify({ error: 'Title is required and must be a non-empty string.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400, // Bad Request
      });
    }

    const newStoryData = {
      title: body.title.trim(),
      author_id: user.id, // Set author_id from authenticated user
      is_public: typeof body.is_public === 'boolean' ? body.is_public : true, // Default to true
      max_contributors: typeof body.max_contributors === 'number' ? body.max_contributors : 10, // Default
      max_words_per_contribution: typeof body.max_words_per_contribution === 'number' ? body.max_words_per_contribution : 50, // Default
      genre: body.genre || null,
      cover_image_url: body.cover_image_url || null,
      // status and current_word_count will use DB defaults
    };

    const { data, error } = await supabaseClient
      .from('stories')
      .insert(newStoryData)
      .select()
      .single(); // Assuming you want to return the created record

    if (error) {
      console.error('Supabase insert error:', error);
      // Check for specific error types, e.g., RLS violation, unique constraint
      let statusCode = 500;
      if (error.code === '23505') statusCode = 409; // Conflict (e.g. unique constraint)
      if (error.code === '42501') statusCode = 403; // RLS permission denied
      
      return new Response(JSON.stringify({ error: error.message, details: error.details, hint: error.hint }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: statusCode,
      });
    }

    return new Response(JSON.stringify(data), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 201, // Created
    });

  } catch (err) {
    console.error('Error processing request:', err);
    const status = typeof err.status === 'number' ? err.status : 500;
    return new Response(JSON.stringify({ error: err.message || 'An unexpected error occurred.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status,
    });
  }
}); 