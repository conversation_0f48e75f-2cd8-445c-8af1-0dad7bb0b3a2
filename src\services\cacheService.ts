import Redis from 'ioredis';
import NodeCache from 'node-cache';

// Cache configuration interface
interface CacheConfig {
  redis: {
    host: string;
    port: number;
    password?: string;
    db: number;
    connectTimeout: number;
    lazyConnect: boolean;
    enableReadyCheck: boolean;
    enableOfflineQueue: boolean;
    cluster?: {
      nodes: Array<{ host: string; port: number }>;
      enableReadyCheck: boolean;
      redisOptions: {
        password?: string;
      };
    };
  };
  fallback: {
    stdTTL: number; // Standard TTL in seconds
    checkperiod: number; // Period for delete expired keys
    useClones: boolean;
  };
  strategies: {
    defaultTTL: number; // 1 hour
    storyTTL: number; // 30 minutes  
    contributionTTL: number; // 15 minutes
    userSessionTTL: number; // 5 minutes
    apiResponseTTL: number; // 10 minutes
    validationTTL: number; // 5 minutes
  };
}

// Cache key patterns for organization
export const CacheKeys = {
  STORY: (id: string) => `story:${id}`,
  STORY_WITH_CONTRIBUTIONS: (id: string) => `story_contributions:${id}`,
  CONTRIBUTIONS: (storyId: string) => `contributions:${storyId}`,
  USER_SESSION: (storyId: string, userId: string) => `session:${storyId}:${userId}`,
  ACTIVE_SESSIONS: (storyId: string) => `active_sessions:${storyId}`,
  API_RESPONSE: (endpoint: string, params: string) => `api:${endpoint}:${params}`,
  VALIDATION: (content: string, type: string) => `validation:${type}:${content.slice(0, 50)}`,
  POPULAR_STORIES: () => 'popular_stories',
  USER_PRESENCE: (userId: string) => `presence:${userId}`,
  STORY_STATS: (storyId: string) => `stats:${storyId}`,
  HOT_CONTENT: () => 'hot_content',
} as const;

// Cache invalidation patterns
export const InvalidationPatterns = {
  STORY_UPDATED: (storyId: string) => [
    CacheKeys.STORY(storyId),
    CacheKeys.STORY_WITH_CONTRIBUTIONS(storyId),
    CacheKeys.STORY_STATS(storyId),
  ],
  CONTRIBUTION_ADDED: (storyId: string) => [
    CacheKeys.STORY_WITH_CONTRIBUTIONS(storyId),
    CacheKeys.CONTRIBUTIONS(storyId),
    CacheKeys.STORY_STATS(storyId),
    CacheKeys.POPULAR_STORIES(),
    CacheKeys.HOT_CONTENT(),
  ],
  USER_SESSION_UPDATED: (storyId: string, userId: string) => [
    CacheKeys.USER_SESSION(storyId, userId),
    CacheKeys.ACTIVE_SESSIONS(storyId),
    CacheKeys.USER_PRESENCE(userId),
  ],
} as const;

// Cache metrics for monitoring
interface CacheMetrics {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  errors: number;
  lastReset: Date;
}

class CacheService {
  private redis: Redis | null = null;
  private fallbackCache: NodeCache;
  private config: CacheConfig;
  private metrics: CacheMetrics;
  private isRedisConnected = false;

  constructor(config?: Partial<CacheConfig>) {
    // Default configuration
    this.config = {
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB || '0'),
        connectTimeout: 10000,
        lazyConnect: true,
        enableReadyCheck: true,
        enableOfflineQueue: false,
        ...config?.redis,
      },
      fallback: {
        stdTTL: 3600, // 1 hour
        checkperiod: 600, // 10 minutes
        useClones: false,
        ...config?.fallback,
      },
      strategies: {
        defaultTTL: 3600, // 1 hour
        storyTTL: 1800, // 30 minutes
        contributionTTL: 900, // 15 minutes
        userSessionTTL: 300, // 5 minutes
        apiResponseTTL: 600, // 10 minutes
        validationTTL: 300, // 5 minutes
        ...config?.strategies,
      },
    };

    // Initialize fallback cache
    this.fallbackCache = new NodeCache(this.config.fallback);

    // Initialize metrics
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
      lastReset: new Date(),
    };

    this.initializeRedis();
  }

  private async initializeRedis(): Promise<void> {
    try {
      // Check if clustering is enabled
      if (this.config.redis.cluster) {
        this.redis = new Redis.Cluster(
          this.config.redis.cluster.nodes,
          {
            enableReadyCheck: this.config.redis.cluster.enableReadyCheck,
            redisOptions: this.config.redis.cluster.redisOptions,
          }
        ) as any; // Type assertion for cluster compatibility
      } else {
        this.redis = new Redis({
          host: this.config.redis.host,
          port: this.config.redis.port,
          password: this.config.redis.password,
          db: this.config.redis.db,
          connectTimeout: this.config.redis.connectTimeout,
          lazyConnect: this.config.redis.lazyConnect,
          enableReadyCheck: this.config.redis.enableReadyCheck,
          enableOfflineQueue: this.config.redis.enableOfflineQueue,
        });
      }

      // Redis event handlers
      this.redis.on('connect', () => {
        console.log('✅ Redis connected successfully');
        this.isRedisConnected = true;
      });

      this.redis.on('ready', () => {
        console.log('🚀 Redis ready for operations');
      });

      this.redis.on('error', (err) => {
        console.error('❌ Redis connection error:', err);
        this.isRedisConnected = false;
        this.metrics.errors++;
      });

      this.redis.on('close', () => {
        console.log('🔌 Redis connection closed');
        this.isRedisConnected = false;
      });

      this.redis.on('reconnecting', () => {
        console.log('🔄 Redis reconnecting...');
      });

    } catch (error) {
      console.error('❌ Failed to initialize Redis:', error);
      this.metrics.errors++;
    }
  }

  // Get value from cache with fallback
  async get<T>(key: string): Promise<T | null> {
    try {
      // Try Redis first
      if (this.isRedisConnected && this.redis) {
        const value = await this.redis.get(key);
        if (value !== null) {
          this.metrics.hits++;
          return JSON.parse(value);
        }
      }

      // Fallback to in-memory cache
      const fallbackValue = this.fallbackCache.get<T>(key);
      if (fallbackValue !== undefined) {
        this.metrics.hits++;
        return fallbackValue;
      }

      this.metrics.misses++;
      return null;
    } catch (error) {
      console.error(`❌ Cache GET error for key ${key}:`, error);
      this.metrics.errors++;
      
      // Try fallback cache on Redis error
      const fallbackValue = this.fallbackCache.get<T>(key);
      if (fallbackValue !== undefined) {
        this.metrics.hits++;
        return fallbackValue;
      }
      
      this.metrics.misses++;
      return null;
    }
  }

  // Set value in cache with TTL
  async set(key: string, value: any, ttl?: number): Promise<boolean> {
    const finalTTL = ttl || this.config.strategies.defaultTTL;
    
    try {
      // Store in Redis
      if (this.isRedisConnected && this.redis) {
        await this.redis.setex(key, finalTTL, JSON.stringify(value));
      }

      // Store in fallback cache
      this.fallbackCache.set(key, value, finalTTL);

      this.metrics.sets++;
      return true;
    } catch (error) {
      console.error(`❌ Cache SET error for key ${key}:`, error);
      this.metrics.errors++;
      
      // Try fallback cache on Redis error
      try {
        this.fallbackCache.set(key, value, finalTTL);
        this.metrics.sets++;
        return true;
      } catch (fallbackError) {
        console.error(`❌ Fallback cache SET error for key ${key}:`, fallbackError);
        return false;
      }
    }
  }

  // Delete specific key
  async delete(key: string): Promise<boolean> {
    try {
      // Delete from Redis
      if (this.isRedisConnected && this.redis) {
        await this.redis.del(key);
      }

      // Delete from fallback cache
      this.fallbackCache.del(key);

      this.metrics.deletes++;
      return true;
    } catch (error) {
      console.error(`❌ Cache DELETE error for key ${key}:`, error);
      this.metrics.errors++;
      return false;
    }
  }

  // Delete by pattern (Redis only, fallback clears all)
  async deletePattern(pattern: string): Promise<number> {
    let deletedCount = 0;
    
    try {
      if (this.isRedisConnected && this.redis) {
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          deletedCount = await this.redis.del(...keys);
        }
      }

      // For fallback cache, we'll clear all (pattern matching not supported)
      if (pattern.includes('*')) {
        this.fallbackCache.flushAll();
        deletedCount += this.fallbackCache.getStats().keys || 0;
      }

      this.metrics.deletes += deletedCount;
      return deletedCount;
    } catch (error) {
      console.error(`❌ Cache DELETE PATTERN error for pattern ${pattern}:`, error);
      this.metrics.errors++;
      return 0;
    }
  }

  // Cache invalidation by patterns
  async invalidate(keys: string[]): Promise<void> {
    try {
      const deletePromises = keys.map(key => this.delete(key));
      await Promise.all(deletePromises);
      console.log(`🗑️ Invalidated ${keys.length} cache entries`);
    } catch (error) {
      console.error('❌ Cache invalidation error:', error);
      this.metrics.errors++;
    }
  }

  // Story-specific caching methods
  async getStory(storyId: string) {
    return this.get(CacheKeys.STORY(storyId));
  }

  async setStory(storyId: string, story: any) {
    return this.set(CacheKeys.STORY(storyId), story, this.config.strategies.storyTTL);
  }

  async getStoryWithContributions(storyId: string) {
    return this.get(CacheKeys.STORY_WITH_CONTRIBUTIONS(storyId));
  }

  async setStoryWithContributions(storyId: string, data: any) {
    return this.set(
      CacheKeys.STORY_WITH_CONTRIBUTIONS(storyId), 
      data, 
      this.config.strategies.storyTTL
    );
  }

  async getUserSession(storyId: string, userId: string) {
    return this.get(CacheKeys.USER_SESSION(storyId, userId));
  }

  async setUserSession(storyId: string, userId: string, session: any) {
    return this.set(
      CacheKeys.USER_SESSION(storyId, userId), 
      session, 
      this.config.strategies.userSessionTTL
    );
  }

  async invalidateStoryCache(storyId: string) {
    await this.invalidate(InvalidationPatterns.STORY_UPDATED(storyId));
  }

  async invalidateContributionCache(storyId: string) {
    await this.invalidate(InvalidationPatterns.CONTRIBUTION_ADDED(storyId));
  }

  async invalidateUserSessionCache(storyId: string, userId: string) {
    await this.invalidate(InvalidationPatterns.USER_SESSION_UPDATED(storyId, userId));
  }

  // Cache warming - preload popular content
  async warmCache(): Promise<void> {
    console.log('🔥 Starting cache warming...');
    
    try {
      // This would be called with actual data from your DatabaseAdapter
      // For now, this is the structure that would be implemented
      console.log('🔥 Cache warming completed');
    } catch (error) {
      console.error('❌ Cache warming error:', error);
      this.metrics.errors++;
    }
  }

  // Get cache statistics
  getStats() {
    const fallbackStats = this.fallbackCache.getStats();
    
    return {
      redis: {
        connected: this.isRedisConnected,
        config: this.config.redis,
      },
      fallback: fallbackStats,
      metrics: {
        ...this.metrics,
        hitRate: this.metrics.hits / (this.metrics.hits + this.metrics.misses) || 0,
        errorRate: this.metrics.errors / (this.metrics.hits + this.metrics.misses + this.metrics.sets) || 0,
      },
    };
  }

  // Reset metrics
  resetMetrics(): void {
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
      lastReset: new Date(),
    };
  }

  // Health check
  async healthCheck(): Promise<{ redis: boolean; fallback: boolean }> {
    const redisHealth = this.isRedisConnected && this.redis !== null;
    const fallbackHealth = this.fallbackCache !== null;
    
    // Test Redis with a simple ping
    if (redisHealth && this.redis) {
      try {
        await this.redis.ping();
      } catch {
        return { redis: false, fallback: fallbackHealth };
      }
    }
    
    return { redis: redisHealth, fallback: fallbackHealth };
  }

  // Cleanup and close connections
  async close(): Promise<void> {
    try {
      if (this.redis) {
        await this.redis.quit();
        this.redis = null;
      }
      
      this.fallbackCache.close();
      console.log('🔌 Cache service closed successfully');
    } catch (error) {
      console.error('❌ Error closing cache service:', error);
    }
  }
}

// Singleton instance
export const cacheService = new CacheService();

// Export for testing or custom configurations
export { CacheService };
export type { CacheConfig }; 