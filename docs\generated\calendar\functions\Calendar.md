[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [calendar](../README.md) / Calendar

# Function: Calendar()

> **Calendar**(`__namedParameters`): `Element`

Defined in: [src/components/ui/calendar.tsx:10](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/calendar.tsx#L10)

## Parameters

### \_\_namedParameters

`DayPickerDefaultProps` | `DayPickerSingleProps` | `DayPickerMultipleProps` | `DayPickerRangeProps`

## Returns

`Element`
