[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [toggle](../README.md) / Toggle

# Variable: Toggle

> `const` **Toggle**: `ForwardRefExoticComponent`\<`Omit`\<`ToggleProps` & `RefAttributes`\<`HTMLButtonElement`\>, `"ref"`\> & `VariantProps`\<(`props?`) => `string`\> & `RefAttributes`\<`HTMLButtonElement`\>\>

Defined in: [src/components/ui/toggle.tsx:29](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/toggle.tsx#L29)
