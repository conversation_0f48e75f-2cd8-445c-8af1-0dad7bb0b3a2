[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [resizable](../README.md) / ResizablePanel

# Variable: ResizablePanel

> `const` **ResizablePanel**: `ForwardRefExoticComponent`\<`Omit`\<`HTMLAttributes`\<`HTMLDivElement` \| `HTMLElement` \| `HTMLButtonElement` \| `HTMLObjectElement` \| `HTMLLinkElement` \| `HTMLAnchorElement` \| `HTMLFormElement` \| `HTMLHeadingElement` \| `HTMLImageElement` \| `HTMLInputElement` \| `HTMLLabelElement` \| `HTMLLIElement` \| `HTMLOListElement` \| `HTMLParagraphElement` \| `HTMLSelectElement` \| `HTMLSpanElement` \| `HTMLUListElement` \| `HTMLAreaElement` \| `HTMLAudioElement` \| `HTMLBaseElement` \| `HTMLQuoteElement` \| `HTMLBodyElement` \| `HTMLBRElement` \| `HTMLCanvasElement` \| `HTMLTableColElement` \| `HTMLDataElement` \| `HTMLDataListElement` \| `HTMLModElement` \| `HTMLDetailsElement` \| `HTMLDialogElement` \| `HTMLDListElement` \| `HTMLEmbedElement` \| `HTMLFieldSetElement` \| `HTMLHeadElement` \| `HTMLHRElement` \| `HTMLHtmlElement` \| `HTMLIFrameElement` \| `HTMLLegendElement` \| `HTMLMapElement` \| `HTMLMetaElement` \| `HTMLMeterElement` \| `HTMLOptGroupElement` \| `HTMLOptionElement` \| `HTMLOutputElement` \| `HTMLPreElement` \| `HTMLProgressElement` \| `HTMLSlotElement` \| `HTMLScriptElement` \| `HTMLSourceElement` \| `HTMLStyleElement` \| `HTMLTableElement` \| `HTMLTemplateElement` \| `HTMLTableSectionElement` \| `HTMLTableCellElement` \| `HTMLTextAreaElement` \| `HTMLTimeElement` \| `HTMLTitleElement` \| `HTMLTableRowElement` \| `HTMLTrackElement` \| `HTMLVideoElement` \| `HTMLTableCaptionElement` \| `HTMLMenuElement` \| `HTMLPictureElement`\>, `"id"` \| `"onResize"`\> & `object` & `object` & `RefAttributes`\<`ImperativePanelHandle`\>\> = `ResizablePrimitive.Panel`

Defined in: [src/components/ui/resizable.tsx:19](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/resizable.tsx#L19)
