import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON>alogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useContentModeration } from "@/hooks/use-content-moderation";
import { Loader2, Shield } from "lucide-react";

interface ReportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  contentId: string;
  contentType: "story" | "contribution" | "comment";
  contentPreview: string;
}

const ReportDialog: React.FC<ReportDialogProps> = ({
  isOpen,
  onClose,
  contentId,
  contentType,
  contentPreview,
}) => {
  const [reason, setReason] = useState<string>("");
  const [details, setDetails] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { reportInappropriateContent } = useContentModeration();

  const handleSubmit = async () => {
    if (!reason) return;

    setIsSubmitting(true);

    try {
      const fullReason = details ? `${reason}: ${details}` : reason;
      const success = await reportInappropriateContent(
        contentId,
        contentType,
        fullReason,
      );

      if (success) {
        onClose();
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-orange-500" />
            Report Inappropriate Content
          </DialogTitle>
          <DialogDescription>
            Help us maintain a positive community by reporting content that
            violates our guidelines.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          <div className="bg-muted p-3 rounded-md text-sm max-h-20 overflow-y-auto">
            <p className="italic text-muted-foreground">"{contentPreview}"</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="report-reason">Reason for reporting</Label>
            <Select value={reason} onValueChange={setReason}>
              <SelectTrigger id="report-reason">
                <SelectValue placeholder="Select a reason" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="inappropriate">
                  Inappropriate content
                </SelectItem>
                <SelectItem value="harassment">
                  Harassment or bullying
                </SelectItem>
                <SelectItem value="spam">Spam or advertising</SelectItem>
                <SelectItem value="hateSpeech">Hate speech</SelectItem>
                <SelectItem value="misinformation">Misinformation</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="report-details">
              Additional details (optional)
            </Label>
            <Textarea
              id="report-details"
              placeholder="Please provide more information about why you're reporting this content"
              value={details}
              onChange={(e) => setDetails(e.target.value)}
              className="min-h-[100px]"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={!reason || isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Submitting
              </>
            ) : (
              "Submit Report"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ReportDialog;
