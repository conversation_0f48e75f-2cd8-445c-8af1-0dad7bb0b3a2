import { supabase } from '@/lib/supabase';

export interface TokenBalance {
  balance: number;
  totalEarned: number;
  totalSpent: number;
}

export interface TokenTransaction {
  id: string;
  userId: string;
  storyId?: string;
  contributionId?: string;
  transactionType: 'earned' | 'spent' | 'refund' | 'admin_adjustment' | 'transfer_sent' | 'transfer_received';
  amount: number;
  balanceAfter: number;
  description?: string;
  createdAt: string;
  transferId?: string;
}

export interface TokenTransferUser {
  userId: string;
  username: string;
  displayName?: string;
  avatarUrl?: string;
  isActive: boolean;
}

export interface TokenTransfer {
  id: string;
  senderId: string;
  recipientId: string;
  amount: number;
  senderBalanceBefore: number;
  senderBalanceAfter: number;
  recipientBalanceBefore: number;
  recipientBalanceAfter: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  transferMessage?: string;
  errorMessage?: string;
  createdAt: string;
  completedAt?: string;
}

export interface TokenTransferHistory {
  transferId: string;
  isSender: boolean;
  otherUserId: string;
  otherUsername: string;
  otherDisplayName?: string;
  otherAvatarUrl?: string;
  amount: number;
  transferMessage?: string;
  status: string;
  createdAt: string;
  completedAt?: string;
}

export interface SpecialActionCost {
  gotcha: number;
  reverse: number;
  golden: number;
}

// Standard costs for special actions
export const SPECIAL_ACTION_COSTS: SpecialActionCost = {
  gotcha: 2,
  reverse: 5,
  golden: 3,
};

/**
 * Service for managing user tokens and special action costs
 */
export class TokenService {
  private static instance: TokenService;

  private constructor() {}

  public static getInstance(): TokenService {
    if (!TokenService.instance) {
      TokenService.instance = new TokenService();
    }
    return TokenService.instance;
  }

  /**
   * Get user's current token balance
   */
  async getUserTokenBalance(userId: string): Promise<TokenBalance | null> {
    try {
      const { data, error } = await supabase
        .from('user_tokens')
        .select('token_balance, total_tokens_earned, total_tokens_spent')
        .eq('user_id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // User not found, initialize them
          await this.initializeUserTokens(userId);
          return { balance: 10, totalEarned: 10, totalSpent: 0 };
        }
        throw error;
      }

      return {
        balance: data.token_balance,
        totalEarned: data.total_tokens_earned,
        totalSpent: data.total_tokens_spent,
      };
    } catch (error) {
      console.error('Error fetching user token balance:', error);
      return null;
    }
  }

  /**
   * Initialize tokens for a new user
   */
  async initializeUserTokens(userId: string, initialTokens: number = 10): Promise<boolean> {
    try {
      const { error } = await supabase.rpc('add_user_tokens', {
        p_user_id: userId,
        p_amount: initialTokens,
        p_description: 'Welcome bonus - free tokens for new users'
      });

      if (error) {
        console.error('Error initializing user tokens:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error initializing user tokens:', error);
      return false;
    }
  }

  /**
   * Spend tokens for a special action
   */
  async spendTokens(
    userId: string,
    amount: number,
    storyId?: string,
    contributionId?: string,
    description?: string
  ): Promise<{ success: boolean; newBalance?: number; error?: string }> {
    try {
      const { data, error } = await supabase.rpc('spend_user_tokens', {
        p_user_id: userId,
        p_amount: amount,
        p_story_id: storyId,
        p_contribution_id: contributionId,
        p_description: description
      });

      if (error) {
        console.error('Error spending tokens:', error);
        return { success: false, error: error.message };
      }

      if (data && data.length > 0) {
        const result = data[0];
        return {
          success: result.success,
          newBalance: result.new_balance,
          error: result.error_message
        };
      }

      return { success: false, error: 'Unexpected response from token service' };
    } catch (error) {
      console.error('Error spending tokens:', error);
      return { success: false, error: 'Failed to spend tokens' };
    }
  }

  /**
   * Add tokens to user's balance
   */
  async addTokens(
    userId: string,
    amount: number,
    description?: string
  ): Promise<{ success: boolean; newBalance?: number; error?: string }> {
    try {
      const { data, error } = await supabase.rpc('add_user_tokens', {
        p_user_id: userId,
        p_amount: amount,
        p_description: description
      });

      if (error) {
        console.error('Error adding tokens:', error);
        return { success: false, error: error.message };
      }

      if (data && data.length > 0) {
        const result = data[0];
        return {
          success: result.success,
          newBalance: result.new_balance,
          error: result.error_message
        };
      }

      return { success: false, error: 'Unexpected response from token service' };
    } catch (error) {
      console.error('Error adding tokens:', error);
      return { success: false, error: 'Failed to add tokens' };
    }
  }

  /**
   * Get token transaction history for a user
   */
  async getTokenTransactions(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<TokenTransaction[]> {
    try {
      const { data, error } = await supabase
        .from('token_transactions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('Error fetching token transactions:', error);
        return [];
      }

      return (data || []).map(tx => ({
        id: tx.id,
        userId: tx.user_id,
        storyId: tx.story_id,
        contributionId: tx.contribution_id,
        transactionType: tx.transaction_type,
        amount: tx.amount,
        balanceAfter: tx.balance_after,
        description: tx.description,
        createdAt: tx.created_at,
        transferId: tx.transfer_id,
      }));
    } catch (error) {
      console.error('Error fetching token transactions:', error);
      return [];
    }
  }

  /**
   * Validate if user can afford a special action
   */
  async canAffordSpecialAction(
    userId: string,
    specialType: 'gotcha' | 'reverse' | 'golden'
  ): Promise<{ canAfford: boolean; cost: number; balance: number }> {
    const cost = SPECIAL_ACTION_COSTS[specialType];
    const tokenBalance = await this.getUserTokenBalance(userId);
    
    if (!tokenBalance) {
      return { canAfford: false, cost, balance: 0 };
    }

    return {
      canAfford: tokenBalance.balance >= cost,
      cost,
      balance: tokenBalance.balance
    };
  }

  /**
   * Get the cost for a special action type
   */
  getSpecialActionCost(specialType: 'gotcha' | 'reverse' | 'golden'): number {
    return SPECIAL_ACTION_COSTS[specialType];
  }

  /**
   * Process a special action with token validation and deduction
   */
  async processSpecialAction(
    userId: string,
    specialType: 'gotcha' | 'reverse' | 'golden',
    storyId: string,
    contributionId?: string
  ): Promise<{ success: boolean; newBalance?: number; error?: string }> {
    const cost = this.getSpecialActionCost(specialType);
    const description = `${specialType} special action`;

    // Check if user can afford the action
    const affordability = await this.canAffordSpecialAction(userId, specialType);
    
    if (!affordability.canAfford) {
      return {
        success: false,
        error: `Insufficient tokens. Need ${cost} tokens, but you only have ${affordability.balance}.`
      };
    }

    // Spend the tokens
    return await this.spendTokens(userId, cost, storyId, contributionId, description);
  }

  /**
   * Find a user by username for transfer validation
   */
  async findUserByUsername(username: string): Promise<TokenTransferUser | null> {
    try {
      const { data, error } = await supabase.rpc('find_user_by_username', {
        p_username: username
      });

      if (error) {
        console.error('Error finding user by username:', error);
        return null;
      }

      if (!data || data.length === 0) {
        return null;
      }

      const user = data[0];
      return {
        userId: user.user_id,
        username: user.username,
        displayName: user.display_name,
        avatarUrl: user.avatar_url,
        isActive: user.is_active
      };
    } catch (error) {
      console.error('Error finding user by username:', error);
      return null;
    }
  }

  /**
   * Transfer tokens from one user to another
   */
  async transferTokens(
    senderId: string,
    recipientUsername: string,
    amount: number,
    transferMessage?: string
  ): Promise<{
    success: boolean;
    transferId?: string;
    senderNewBalance?: number;
    recipientNewBalance?: number;
    error?: string;
  }> {
    try {
      // Validate inputs
      if (amount <= 0) {
        return { success: false, error: 'Transfer amount must be positive' };
      }

      if (!recipientUsername || recipientUsername.trim().length === 0) {
        return { success: false, error: 'Recipient username is required' };
      }

      const { data, error } = await supabase.rpc('transfer_tokens_between_users', {
        p_sender_id: senderId,
        p_recipient_username: recipientUsername.trim(),
        p_amount: amount,
        p_transfer_message: transferMessage
      });

      if (error) {
        console.error('Error transferring tokens:', error);
        return { success: false, error: error.message };
      }

      if (data && data.length > 0) {
        const result = data[0];
        return {
          success: result.success,
          transferId: result.transfer_id,
          senderNewBalance: result.sender_new_balance,
          recipientNewBalance: result.recipient_new_balance,
          error: result.error_message
        };
      }

      return { success: false, error: 'Unexpected response from transfer service' };
    } catch (error) {
      console.error('Error transferring tokens:', error);
      return { success: false, error: 'Failed to transfer tokens' };
    }
  }

  /**
   * Get transfer history for a user
   */
  async getTransferHistory(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<TokenTransferHistory[]> {
    try {
      const { data, error } = await supabase.rpc('get_user_transfer_history', {
        p_user_id: userId,
        p_limit: limit,
        p_offset: offset
      });

      if (error) {
        console.error('Error fetching transfer history:', error);
        return [];
      }

      return (data || []).map(transfer => ({
        transferId: transfer.transfer_id,
        isSender: transfer.is_sender,
        otherUserId: transfer.other_user_id,
        otherUsername: transfer.other_username,
        otherDisplayName: transfer.other_display_name,
        otherAvatarUrl: transfer.other_avatar_url,
        amount: transfer.amount,
        transferMessage: transfer.transfer_message,
        status: transfer.status,
        createdAt: transfer.created_at,
        completedAt: transfer.completed_at
      }));
    } catch (error) {
      console.error('Error fetching transfer history:', error);
      return [];
    }
  }

  /**
   * Validate if user can afford a transfer
   */
  async canAffordTransfer(
    userId: string,
    amount: number
  ): Promise<{ canAfford: boolean; balance: number; error?: string }> {
    if (amount <= 0) {
      return { canAfford: false, balance: 0, error: 'Transfer amount must be positive' };
    }

    const tokenBalance = await this.getUserTokenBalance(userId);
    
    if (!tokenBalance) {
      return { canAfford: false, balance: 0, error: 'Unable to fetch token balance' };
    }

    return {
      canAfford: tokenBalance.balance >= amount,
      balance: tokenBalance.balance
    };
  }
}

// Export singleton instance
export const tokenService = TokenService.getInstance(); 