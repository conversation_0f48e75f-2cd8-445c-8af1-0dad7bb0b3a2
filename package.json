{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "node scripts/platform-deps.js auto && concurrently \"vite\" \"tsx watch server.mjs\"", "dev:frontend": "node scripts/platform-deps.js auto && vite", "build": "node scripts/platform-deps.js auto && node build.js", "build:dev": "node scripts/platform-deps.js auto && vite build --mode development", "lint": "eslint .", "preview": "vite preview", "vercel-build": "node scripts/platform-deps.js remove && node vercel-build.js", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate deploy", "deploy:production": "node deploy-production.js", "deploy:preview": "node deploy-preview.js", "deploy:development": "node deploy-development.js", "setup:vercel-env": "node setup-vercel-env.js", "test": "vitest run", "test:watch": "vitest", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:e2e:dev": "concurrently \"npm run dev\" \"cypress open\"", "test:component": "cypress run --component", "perf:lighthouse": "node scripts/performance/lighthouse-audit.js", "perf:bundle": "node scripts/performance/bundle-analyzer.js", "perf:vitals": "node scripts/performance/web-vitals-monitor.js", "perf:build": "vite build --config vite.performance.config.ts", "perf:analyze": "npm run perf:build && npm run perf:bundle && npm run perf:lighthouse", "start:development": "npx vite", "start:local": "node build-local.cjs", "serve": "node server.js", "simple-dev": "node simple-dev.js", "start:preview-local": "npx vite build --mode development && npx vite preview", "start:production-local": "npx vite build && npx vite preview", "express-server": "node express-server.mjs", "dev:patched": "node vite-patched.mjs", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "docs:generate": "typedoc", "docs:serve": "npx http-server docs/generated -p 8080 -o", "docs:build": "npm run docs:generate && npm run build-storybook", "docs:dev": "concurrently \"npm run storybook\" \"npm run docs:serve\""}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@stripe/stripe-js": "^7.3.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.56.2", "@types/redis": "^4.0.10", "@types/stripe": "^8.0.416", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "firebase": "^11.8.1", "helmet": "^8.1.0", "input-otp": "^1.2.4", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "node-cache": "^5.1.2", "posthog-js": "^1.246.0", "rate-limiter-flexible": "^7.1.1", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "redis": "^5.1.0", "sonner": "^1.5.0", "stripe": "^18.1.1", "supabase": "^2.23.4", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "task-master-ai": "^0.14.0", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@cypress/code-coverage": "^3.14.3", "@eslint/js": "^9.9.0", "@storybook/addon-a11y": "^8.6.14", "@storybook/addon-actions": "^8.6.14", "@storybook/addon-backgrounds": "^8.6.14", "@storybook/addon-controls": "^8.6.14", "@storybook/addon-docs": "^8.6.14", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/addon-viewport": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/react-vite": "^8.6.14", "@tailwindcss/typography": "^0.5.15", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/next": "^8.0.7", "@types/node": "^22.15.21", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.4.1", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/ui": "^3.1.4", "autoprefixer": "^10.4.20", "axe-core": "^4.10.3", "chrome-launcher": "^1.2.0", "concurrently": "^9.1.2", "cypress": "^14.4.0", "cypress-axe": "^1.6.0", "cypress-real-events": "^1.14.0", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "http-server": "^14.1.1", "jest": "^29.7.0", "jsdom": "^26.1.0", "lighthouse": "^12.6.0", "node-mocks-http": "^1.17.2", "postcss": "^8.4.47", "prettier": "^3.5.3", "prisma": "^6.8.2", "rollup-plugin-visualizer": "^5.14.0", "storybook": "^8.6.14", "tailwindcss": "^3.4.11", "ts-jest": "^29.3.4", "tsx": "^4.19.4", "typedoc": "^0.28.4", "typedoc-plugin-markdown": "^4.6.3", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vite-bundle-analyzer": "^0.21.0", "vitest": "^3.1.4"}, "optionalDependencies": {"@esbuild/win32-x64": "^4.13.0", "@rollup/rollup-win32-x64-msvc": "^4.41.1"}}