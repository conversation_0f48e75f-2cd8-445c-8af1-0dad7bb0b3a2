import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Shield, Phone, Smartphone } from "lucide-react";
import { useAuth } from "@/contexts/auth";
import { supabase } from "@/lib/supabase";

interface MFAVerificationProps {
  onVerificationComplete?: () => void;
  onCancel?: () => void;
}

interface MFAFactor {
  id: string;
  friendly_name?: string;
  factor_type: 'phone' | 'totp' | (string & {});
  status: 'unverified' | 'verified';
  phone?: string;
}

export const MFAVerification: React.FC<MFAVerificationProps> = ({
  onVerificationComplete,
  onCancel,
}) => {
  const { user } = useAuth();
  const [verificationCode, setVerificationCode] = useState("");
  const [selectedFactor, setSelectedFactor] = useState<MFAFactor | null>(null);
  const [challengeId, setChallengeId] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [factors, setFactors] = useState<MFAFactor[]>([]);
  const [step, setStep] = useState<'select' | 'verify'>('select');

  // Load available MFA factors on component mount
  useEffect(() => {
    const loadFactors = async () => {
      try {
        const { data, error } = await supabase.auth.mfa.listFactors();
        if (error) throw error;
        
        const allFactors = [...(data.totp || []), ...(data.phone || [])];
        const verifiedFactors = allFactors.filter(factor => factor.status === 'verified');
        setFactors(verifiedFactors);
        
        // If only one factor, auto-select it
        if (verifiedFactors.length === 1) {
          setSelectedFactor(verifiedFactors[0]);
          await startChallenge(verifiedFactors[0]);
        }
      } catch (err) {
        console.error('Error loading MFA factors:', err);
        setError("Failed to load authentication methods");
      }
    };

    if (user) {
      loadFactors();
    }
  }, [user]);

  const startChallenge = async (factor: MFAFactor) => {
    setLoading(true);
    setError("");

    try {
      const { data: challenge, error } = await supabase.auth.mfa.challenge({
        factorId: factor.id,
      });

      if (error) throw error;

      setChallengeId(challenge.id);
      setSelectedFactor(factor);
      setStep('verify');
    } catch (err: any) {
      setError(err.message || "Failed to start verification challenge");
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyCode = async () => {
    if (!verificationCode.trim()) {
      setError("Please enter the verification code");
      return;
    }

    if (!selectedFactor || !challengeId) {
      setError("Verification session expired. Please try again.");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const { error: verifyError } = await supabase.auth.mfa.verify({
        factorId: selectedFactor.id,
        challengeId,
        code: verificationCode,
      });

      if (verifyError) throw verifyError;

      // Verification successful
      onVerificationComplete?.();
    } catch (err: any) {
      setError(err.message || "Invalid verification code");
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (!selectedFactor) return;
    await startChallenge(selectedFactor);
  };

  const handleSelectFactor = async (factor: MFAFactor) => {
    await startChallenge(factor);
  };

  const getFactorIcon = (factorType: string) => {
    switch (factorType) {
      case 'phone':
        return <Phone className="w-5 h-5" />;
      case 'totp':
        return <Smartphone className="w-5 h-5" />;
      default:
        return <Shield className="w-5 h-5" />;
    }
  };

  const getFactorLabel = (factor: MFAFactor) => {
    switch (factor.factor_type) {
      case 'phone':
        return `SMS to ${factor.phone || 'your phone'}`;
      case 'totp':
        return 'Authenticator App';
      default:
        return 'Authentication Factor';
    }
  };

  // If no factors are available
  if (factors.length === 0 && !loading) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
            <Shield className="w-6 h-6 text-yellow-600" />
          </div>
          <CardTitle>No MFA Methods Found</CardTitle>
          <CardDescription>
            You don't have any multi-factor authentication methods set up
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="space-y-2">
            <Button onClick={onCancel} className="w-full">
              Continue Without MFA
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Factor selection step
  if (step === 'select' && factors.length > 1) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <Shield className="w-6 h-6 text-blue-600" />
          </div>
          <CardTitle>Choose Verification Method</CardTitle>
          <CardDescription>
            Select how you'd like to verify your identity
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            {factors.map((factor) => (
              <Button
                key={factor.id}
                variant="outline"
                onClick={() => handleSelectFactor(factor)}
                disabled={loading}
                className="w-full justify-start h-auto p-4"
              >
                <div className="flex items-center space-x-3">
                  {getFactorIcon(factor.factor_type)}
                  <div className="text-left">
                    <div className="font-medium">{getFactorLabel(factor)}</div>
                    {factor.factor_type === 'phone' && factor.phone && (
                      <div className="text-sm text-gray-500">{factor.phone}</div>
                    )}
                  </div>
                </div>
              </Button>
            ))}
          </div>

          {onCancel && (
            <Button variant="ghost" onClick={onCancel} className="w-full">
              Cancel
            </Button>
          )}
        </CardContent>
      </Card>
    );
  }

  // Verification step
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
          {selectedFactor && getFactorIcon(selectedFactor.factor_type)}
        </div>
        <CardTitle>Verify Your Identity</CardTitle>
        <CardDescription>
          {selectedFactor && getFactorLabel(selectedFactor)}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Input
            type="text"
            placeholder={selectedFactor?.factor_type === 'phone' ? "Enter 6-digit SMS code" : "Enter verification code"}
            value={verificationCode}
            onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
            maxLength={6}
            className="text-center text-lg tracking-widest"
          />
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <Button
            onClick={handleVerifyCode}
            disabled={loading || verificationCode.length !== 6}
            className="w-full"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Verifying...
              </>
            ) : (
              "Verify Code"
            )}
          </Button>

          {selectedFactor?.factor_type === 'phone' && (
            <Button
              variant="ghost"
              onClick={handleResendCode}
              disabled={loading}
              className="w-full"
            >
              Resend SMS Code
            </Button>
          )}

          <Button
            variant="outline"
            onClick={() => {
              if (factors.length > 1) {
                setStep('select');
                setSelectedFactor(null);
                setVerificationCode("");
                setChallengeId("");
              } else {
                onCancel?.();
              }
            }}
            className="w-full"
          >
            {factors.length > 1 ? 'Choose Different Method' : 'Cancel'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
