-- Migration: Add user tokens/credits system
-- File: supabase/migrations/0011_add_user_tokens_table.sql

-- Create user_tokens table to track token balances
CREATE TABLE IF NOT EXISTS public.user_tokens (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    token_balance INTEGER NOT NULL DEFAULT 0 CHECK (token_balance >= 0),
    total_tokens_earned INTEGER NOT NULL DEFAULT 0,
    total_tokens_spent INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    CONSTRAINT unique_user_tokens UNIQUE (user_id)
);

-- Create token_transactions table to track token usage history
CREATE TABLE IF NOT EXISTS public.token_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    story_id UUID REFERENCES public.stories(id) ON DELETE CASCADE,
    contribution_id UUID REFERENCES public.contributions(id) ON DELETE CASCADE,
    transaction_type TEXT NOT NULL CHECK (transaction_type IN ('earned', 'spent', 'refund', 'admin_adjustment')),
    amount INTEGER NOT NULL,
    balance_after INTEGER NOT NULL,
    description TEXT,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT now()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_tokens_user_id ON public.user_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_token_transactions_user_id ON public.token_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_token_transactions_story_id ON public.token_transactions(story_id);
CREATE INDEX IF NOT EXISTS idx_token_transactions_created_at ON public.token_transactions(created_at DESC);

-- Enable RLS
ALTER TABLE public.user_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.token_transactions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_tokens
CREATE POLICY "Users can read their own token balance"
ON public.user_tokens FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own token balance"
ON public.user_tokens FOR UPDATE USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- RLS Policies for token_transactions
CREATE POLICY "Users can read their own token transactions"
ON public.token_transactions FOR SELECT USING (auth.uid() = user_id);

-- Function to initialize user tokens when they first sign up
CREATE OR REPLACE FUNCTION public.initialize_user_tokens()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Give new users some starting tokens
    INSERT INTO public.user_tokens (user_id, token_balance, total_tokens_earned)
    VALUES (NEW.id, 10, 10); -- Start with 10 free tokens
    
    -- Record the initial token grant
    INSERT INTO public.token_transactions (
        user_id, 
        transaction_type, 
        amount, 
        balance_after, 
        description
    ) VALUES (
        NEW.id, 
        'earned', 
        10, 
        10, 
        'Welcome bonus - free tokens for new users'
    );
    
    RETURN NEW;
END;
$$;

-- Trigger to initialize tokens for new users
CREATE TRIGGER trigger_initialize_user_tokens
AFTER INSERT ON auth.users
FOR EACH ROW EXECUTE FUNCTION public.initialize_user_tokens();

-- Function to spend tokens (with validation)
CREATE OR REPLACE FUNCTION public.spend_user_tokens(
    p_user_id UUID,
    p_amount INTEGER,
    p_story_id UUID DEFAULT NULL,
    p_contribution_id UUID DEFAULT NULL,
    p_description TEXT DEFAULT NULL
)
RETURNS TABLE (
    success BOOLEAN,
    new_balance INTEGER,
    error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_current_balance INTEGER;
    v_new_balance INTEGER;
BEGIN
    -- Lock the user's token record
    SELECT token_balance INTO v_current_balance
    FROM public.user_tokens
    WHERE user_id = p_user_id
    FOR UPDATE;
    
    -- Check if user exists
    IF NOT FOUND THEN
        RETURN QUERY SELECT FALSE, 0, 'User not found or tokens not initialized';
        RETURN;
    END IF;
    
    -- Check if user has enough tokens
    IF v_current_balance < p_amount THEN
        RETURN QUERY SELECT FALSE, v_current_balance, 'Insufficient tokens';
        RETURN;
    END IF;
    
    -- Calculate new balance
    v_new_balance := v_current_balance - p_amount;
    
    -- Update user's token balance
    UPDATE public.user_tokens
    SET 
        token_balance = v_new_balance,
        total_tokens_spent = total_tokens_spent + p_amount,
        updated_at = now()
    WHERE user_id = p_user_id;
    
    -- Record the transaction
    INSERT INTO public.token_transactions (
        user_id,
        story_id,
        contribution_id,
        transaction_type,
        amount,
        balance_after,
        description
    ) VALUES (
        p_user_id,
        p_story_id,
        p_contribution_id,
        'spent',
        -p_amount, -- Negative for spending
        v_new_balance,
        COALESCE(p_description, 'Token spent')
    );
    
    RETURN QUERY SELECT TRUE, v_new_balance, NULL::TEXT;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT FALSE, v_current_balance, SQLERRM;
END;
$$;

-- Function to add tokens (for purchases, rewards, etc.)
CREATE OR REPLACE FUNCTION public.add_user_tokens(
    p_user_id UUID,
    p_amount INTEGER,
    p_description TEXT DEFAULT NULL
)
RETURNS TABLE (
    success BOOLEAN,
    new_balance INTEGER,
    error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_current_balance INTEGER;
    v_new_balance INTEGER;
BEGIN
    -- Lock the user's token record
    SELECT token_balance INTO v_current_balance
    FROM public.user_tokens
    WHERE user_id = p_user_id
    FOR UPDATE;
    
    -- Check if user exists, if not initialize them
    IF NOT FOUND THEN
        INSERT INTO public.user_tokens (user_id, token_balance, total_tokens_earned)
        VALUES (p_user_id, p_amount, p_amount);
        v_new_balance := p_amount;
    ELSE
        -- Calculate new balance
        v_new_balance := v_current_balance + p_amount;
        
        -- Update user's token balance
        UPDATE public.user_tokens
        SET 
            token_balance = v_new_balance,
            total_tokens_earned = total_tokens_earned + p_amount,
            updated_at = now()
        WHERE user_id = p_user_id;
    END IF;
    
    -- Record the transaction
    INSERT INTO public.token_transactions (
        user_id,
        transaction_type,
        amount,
        balance_after,
        description
    ) VALUES (
        p_user_id,
        'earned',
        p_amount,
        v_new_balance,
        COALESCE(p_description, 'Tokens added')
    );
    
    RETURN QUERY SELECT TRUE, v_new_balance, NULL::TEXT;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN QUERY SELECT FALSE, COALESCE(v_current_balance, 0), SQLERRM;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.spend_user_tokens(UUID, INTEGER, UUID, UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.add_user_tokens(UUID, INTEGER, TEXT) TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE public.user_tokens IS 'Tracks token balances for each user';
COMMENT ON TABLE public.token_transactions IS 'Records all token transactions for audit trail';
COMMENT ON COLUMN public.user_tokens.token_balance IS 'Current available token balance';
COMMENT ON COLUMN public.user_tokens.total_tokens_earned IS 'Total tokens earned throughout lifetime';
COMMENT ON COLUMN public.user_tokens.total_tokens_spent IS 'Total tokens spent throughout lifetime'; 