<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 192 192" width="192" height="192">
  <defs>
    <style>
      .book-cover { fill: #7D1D3F; }
      .book-spine { fill: #5A142E; }
      .book-pages { fill: #F8F0E3; }
      .page-lines { fill: #E8D8CB; }
      .shadow { fill: rgba(0,0,0,0.15); }
      .highlight { fill: rgba(255,255,255,0.1); }
    </style>
  </defs>
  
  <!-- Drop shadow -->
  <ellipse cx="96" cy="180" rx="75" ry="8" class="shadow"/>
  
  <!-- Book cover -->
  <rect x="36" y="36" width="120" height="120" rx="8" class="book-cover"/>
  
  <!-- Book spine shadow -->
  <rect x="36" y="36" width="18" height="120" rx="8" class="book-spine"/>
  
  <!-- Cover highlight -->
  <rect x="39" y="39" width="114" height="6" rx="3" class="highlight"/>
  
  <!-- Pages -->
  <rect x="57" y="48" width="90" height="96" class="book-pages"/>
  
  <!-- Page lines for text detail -->
  <line x1="66" y1="66" x2="138" y2="66" stroke="#E8D8CB" stroke-width="2"/>
  <line x1="66" y1="78" x2="129" y2="78" stroke="#E8D8CB" stroke-width="2"/>
  <line x1="66" y1="90" x2="135" y2="90" stroke="#E8D8CB" stroke-width="2"/>
  <line x1="66" y1="102" x2="123" y2="102" stroke="#E8D8CB" stroke-width="2"/>
  <line x1="66" y1="114" x2="138" y2="114" stroke="#E8D8CB" stroke-width="2"/>
  <line x1="66" y1="126" x2="132" y2="126" stroke="#E8D8CB" stroke-width="2"/>
  
  <!-- Spine detail line -->
  <line x1="48" y1="48" x2="48" y2="144" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
  
  <!-- Book binding -->
  <circle cx="63" cy="72" r="1.5" fill="#B87333"/>
  <circle cx="63" cy="96" r="1.5" fill="#B87333"/>
  <circle cx="63" cy="120" r="1.5" fill="#B87333"/>
</svg> 