import React, { useState } from 'react';
import { MessagingInterface } from '@/components/messaging';
import type { Story, Contribution, Participant } from '@/components/messaging';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Smartphone, Tablet, Monitor } from 'lucide-react';
import { Link } from 'react-router-dom';

// Mock data for demo
const demoStory: Story = {
  id: 'demo-story',
  title: 'The Digital Storytellers',
  description: 'A modern tale of collaboration in the digital age',
  contribution_mode: 'word',
  words_per_contribution: 1,
  max_contributions: 50,
  current_contribution_count: 12,
  creator_id: 'demo-user-1',
  status: 'ACTIVE',
};

const initialContributions: Contribution[] = [
  {
    id: 'demo-1',
    content: 'In',
    author: { id: 'demo-user-1', username: 'DigitalDreamer', avatar_url: 'https://i.pravatar.cc/150?u=demo1' },
    created_at: new Date(Date.now() - 60000 * 11).toISOString(),
    position: 1,
  },
  {
    id: 'demo-2',
    content: 'the',
    author: { id: 'demo-user-2', username: 'CodePoet', avatar_url: 'https://i.pravatar.cc/150?u=demo2' },
    created_at: new Date(Date.now() - 60000 * 10).toISOString(),
    position: 2,
  },
  {
    id: 'demo-3',
    content: 'age',
    author: { id: 'demo-user-3', username: 'StoryWeaver', avatar_url: 'https://i.pravatar.cc/150?u=demo3' },
    created_at: new Date(Date.now() - 60000 * 9).toISOString(),
    position: 3,
  },
  {
    id: 'demo-4',
    content: 'of',
    author: { id: 'demo-user-1', username: 'DigitalDreamer', avatar_url: 'https://i.pravatar.cc/150?u=demo1' },
    created_at: new Date(Date.now() - 60000 * 8).toISOString(),
    position: 4,
  },
  {
    id: 'demo-5',
    content: 'connected',
    author: { id: 'demo-user-4', username: 'TechScribe', avatar_url: 'https://i.pravatar.cc/150?u=demo4' },
    created_at: new Date(Date.now() - 60000 * 7).toISOString(),
    position: 5,
    special_type: 'gotcha',
    token_cost: 2,
  },
  {
    id: 'demo-6',
    content: 'minds',
    author: { id: 'demo-user-2', username: 'CodePoet', avatar_url: 'https://i.pravatar.cc/150?u=demo2' },
    created_at: new Date(Date.now() - 60000 * 6).toISOString(),
    position: 6,
  },
  {
    id: 'demo-7',
    content: 'and',
    author: { id: 'demo-user-5', username: 'CreativeAI', avatar_url: 'https://i.pravatar.cc/150?u=demo5' },
    created_at: new Date(Date.now() - 60000 * 5).toISOString(),
    position: 7,
  },
  {
    id: 'demo-8',
    content: 'infinite',
    author: { id: 'demo-user-3', username: 'StoryWeaver', avatar_url: 'https://i.pravatar.cc/150?u=demo3' },
    created_at: new Date(Date.now() - 60000 * 4).toISOString(),
    position: 8,
    special_type: 'golden',
    token_cost: 3,
  },
  {
    id: 'demo-9',
    content: 'possibilities',
    author: { id: 'demo-user-6', username: 'WordMaster', avatar_url: 'https://i.pravatar.cc/150?u=demo6' },
    created_at: new Date(Date.now() - 60000 * 3).toISOString(),
    position: 9,
  },
  {
    id: 'demo-10',
    content: 'writers',
    author: { id: 'demo-user-1', username: 'DigitalDreamer', avatar_url: 'https://i.pravatar.cc/150?u=demo1' },
    created_at: new Date(Date.now() - 60000 * 2).toISOString(),
    position: 10,
  },
  {
    id: 'demo-11',
    content: 'discovered',
    author: { id: 'demo-user-4', username: 'TechScribe', avatar_url: 'https://i.pravatar.cc/150?u=demo4' },
    created_at: new Date(Date.now() - 60000 * 1).toISOString(),
    position: 11,
  },
  {
    id: 'demo-12',
    content: 'collaboration',
    author: { id: 'demo-user-2', username: 'CodePoet', avatar_url: 'https://i.pravatar.cc/150?u=demo2' },
    created_at: new Date().toISOString(),
    position: 12,
  },
];

const demoParticipants: Participant[] = [
  {
    id: 'demo-user-1',
    username: 'DigitalDreamer',
    avatar_url: 'https://i.pravatar.cc/150?u=demo1',
    role: 'creator',
    isOnline: true,
    contributionCount: 3,
    joinedAt: new Date(Date.now() - 3600000),
  },
  {
    id: 'demo-user-2',
    username: 'CodePoet',
    avatar_url: 'https://i.pravatar.cc/150?u=demo2',
    role: 'contributor',
    isOnline: true,
    contributionCount: 3,
    joinedAt: new Date(Date.now() - 3000000),
    isTyping: true,
  },
  {
    id: 'demo-user-3',
    username: 'StoryWeaver',
    avatar_url: 'https://i.pravatar.cc/150?u=demo3',
    role: 'contributor',
    isOnline: true,
    contributionCount: 2,
    joinedAt: new Date(Date.now() - 2400000),
  },
  {
    id: 'demo-user-4',
    username: 'TechScribe',
    avatar_url: 'https://i.pravatar.cc/150?u=demo4',
    role: 'contributor',
    isOnline: false,
    lastSeen: new Date(Date.now() - 300000),
    contributionCount: 2,
    joinedAt: new Date(Date.now() - 1800000),
  },
  {
    id: 'demo-user-5',
    username: 'CreativeAI',
    avatar_url: 'https://i.pravatar.cc/150?u=demo5',
    role: 'contributor',
    isOnline: true,
    contributionCount: 1,
    joinedAt: new Date(Date.now() - 1200000),
  },
  {
    id: 'demo-user-6',
    username: 'WordMaster',
    avatar_url: 'https://i.pravatar.cc/150?u=demo6',
    role: 'viewer',
    isOnline: false,
    lastSeen: new Date(Date.now() - 600000),
    contributionCount: 1,
    joinedAt: new Date(Date.now() - 600000),
  },
];

const MessagingDemo: React.FC = () => {
  const [contributions, setContributions] = useState<Contribution[]>(initialContributions);
  const [participants, setParticipants] = useState<Participant[]>(demoParticipants);
  const [userTokens, setUserTokens] = useState(12);
  const [selectedDevice, setSelectedDevice] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');

  const handleSendContribution = async (content: string, specialType?: 'gotcha' | 'reverse' | 'golden') => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));

    // Calculate token cost
    const tokenCost = specialType === 'gotcha' ? 2 : specialType === 'reverse' ? 5 : specialType === 'golden' ? 3 : 0;
    
    if (tokenCost > userTokens) {
      throw new Error('Insufficient tokens');
    }

    const newContribution: Contribution = {
      id: `demo-${Date.now()}`,
      content,
      author: {
        id: 'demo-user-1',
        username: 'DigitalDreamer',
        avatar_url: 'https://i.pravatar.cc/150?u=demo1',
      },
      created_at: new Date().toISOString(),
      position: contributions.length + 1,
      special_type: specialType || undefined,
      token_cost: tokenCost || undefined,
    };

    setContributions(prev => [...prev, newContribution]);
    setUserTokens(prev => prev - tokenCost);

    // Update story contribution count
    demoStory.current_contribution_count = contributions.length + 1;
  };

  const handleInviteUser = () => {
    console.log('Invite user functionality would open here');
  };

  const handleKickUser = (userId: string) => {
    setParticipants(prev => prev.filter(p => p.id !== userId));
  };

  const handlePromoteUser = (userId: string) => {
    setParticipants(prev => 
      prev.map(p => p.id === userId ? { ...p, role: 'contributor' as const } : p)
    );
  };

  const handleMuteUser = (userId: string) => {
    console.log('Mute user:', userId);
  };

  const handleShareStory = () => {
    const storyText = contributions.map(c => c.content).join(' ');
    navigator.clipboard.writeText(storyText);
    console.log('Story text copied to clipboard');
  };

  const handleExportStory = (format: 'txt' | 'pdf' | 'markdown') => {
    console.log('Export story as:', format);
    // Here you would implement actual export functionality
  };

  const deviceStyles = {
    mobile: 'max-w-sm mx-auto',
    tablet: 'max-w-2xl mx-auto',
    desktop: 'w-full',
  };

  const deviceHeights = {
    mobile: 'h-[667px]', // iPhone height
    tablet: 'h-[768px]',  // iPad height
    desktop: 'h-screen',
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Link to="/" className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground">
                <ArrowLeft className="w-4 h-4" />
                Back to App
              </Link>
              <div>
                <h1 className="text-xl font-semibold">Messaging Interface Demo</h1>
                <p className="text-sm text-muted-foreground">
                  Experience the new chat-style collaborative writing
                </p>
              </div>
            </div>

            {/* Device Selector */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground mr-2">View:</span>
              <div className="flex rounded-lg border">
                <Button
                  variant={selectedDevice === 'mobile' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setSelectedDevice('mobile')}
                  className="rounded-r-none"
                >
                  <Smartphone className="w-4 h-4" />
                </Button>
                <Button
                  variant={selectedDevice === 'tablet' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setSelectedDevice('tablet')}
                  className="rounded-none border-x"
                >
                  <Tablet className="w-4 h-4" />
                </Button>
                <Button
                  variant={selectedDevice === 'desktop' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setSelectedDevice('desktop')}
                  className="rounded-l-none"
                >
                  <Monitor className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Demo Stats */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{contributions.length}</div>
              <p className="text-sm text-muted-foreground">Contributions</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{participants.filter(p => p.isOnline).length}</div>
              <p className="text-sm text-muted-foreground">Online Users</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-600">{userTokens}</div>
              <p className="text-sm text-muted-foreground">Your Tokens</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-orange-600">
                {Math.round((contributions.length / demoStory.max_contributions) * 100)}%
              </div>
              <p className="text-sm text-muted-foreground">Story Progress</p>
            </CardContent>
          </Card>
        </div>

        {/* Features */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Features Demonstrated</h3>
          <div className="flex flex-wrap gap-2">
            <Badge variant="secondary">Real-time messaging UI</Badge>
            <Badge variant="secondary">Mobile-first responsive design</Badge>
            <Badge variant="secondary">Special token actions</Badge>
            <Badge variant="secondary">Typing indicators</Badge>
            <Badge variant="secondary">Participant management</Badge>
            <Badge variant="secondary">Story compilation view</Badge>
            <Badge variant="secondary">Message bubbles</Badge>
            <Badge variant="secondary">Avatar system</Badge>
          </div>
        </div>
      </div>

      {/* Messaging Interface */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <div className={`${deviceStyles[selectedDevice]} ${deviceHeights[selectedDevice]} border rounded-lg shadow-xl bg-white dark:bg-gray-800 overflow-hidden`}>
          <MessagingInterface
            story={demoStory}
            contributions={contributions}
            participants={participants}
            userTokens={userTokens}
            onSendContribution={handleSendContribution}
            onInviteUser={handleInviteUser}
            onKickUser={handleKickUser}
            onPromoteUser={handlePromoteUser}
            onMuteUser={handleMuteUser}
            onShareStory={handleShareStory}
            onExportStory={handleExportStory}
          />
        </div>
      </div>

      {/* Instructions */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
        <Card>
          <CardHeader>
            <CardTitle>Try It Out!</CardTitle>
            <CardDescription>
              This is a fully interactive demo of the new messaging-style interface for collaborative writing.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">How to Use:</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Type a single word in the input field and press send</li>
                  <li>• Try the special actions (lightning bolt icon) to use tokens</li>
                  <li>• Click the users icon to see participants</li>
                  <li>• Click the book icon to view the compiled story</li>
                  <li>• Test different device views using the buttons above</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">Special Actions:</h4>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="outline">Gotcha Word (2 tokens) - Add a plot twist</Badge>
                  <Badge variant="outline">Reverse Move (5 tokens) - Undo and replace</Badge>
                  <Badge variant="outline">Golden Contribution (3 tokens) - Highlight important text</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default MessagingDemo;