import React, { useEffect, useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { gravatarService, AvatarPreference } from '@/services/gravatarService';
import { cn } from '@/lib/utils';

interface UserAvatarProps {
  user?: {
    id?: string;
    email?: string;
    username?: string;
    avatar_url?: string;
    avatarUrl?: string; // Alternative naming
    profilePicture?: string; // Alternative naming
  };
  email?: string; // Direct email prop
  username?: string; // Direct username prop
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  preferences?: AvatarPreference; // Override preferences
  showGravatarIfAvailable?: boolean; // Simple Gravatar check without preferences
}

const sizeClasses = {
  sm: 'w-6 h-6',
  md: 'w-8 h-8',
  lg: 'w-12 h-12',
  xl: 'w-16 h-16',
};

export const UserAvatar: React.FC<UserAvatarProps> = ({
  user,
  email: directEmail,
  username: directUsername,
  size = 'md',
  className,
  preferences,
  showGravatarIfAvailable = true,
}) => {
  const [avatarUrl, setAvatarUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  // Extract user info from various prop formats
  const userEmail = directEmail || user?.email;
  const userName = directUsername || user?.username;
  const existingAvatarUrl = user?.avatar_url || user?.avatarUrl || user?.profilePicture;

  // Get initials for fallback
  const getInitials = () => {
    if (userName) {
      const words = userName.trim().split(/\s+/);
      if (words.length === 1) {
        return words[0].substring(0, 2).toUpperCase();
      }
      return words
        .slice(0, 2)
        .map(word => word.charAt(0).toUpperCase())
        .join('');
    }
    
    if (userEmail) {
      return userEmail.charAt(0).toUpperCase();
    }
    
    return 'U';
  };

  useEffect(() => {
    const loadAvatar = async () => {
      // If we have existing avatar URL and not using Gravatar override, use it
      if (existingAvatarUrl && !showGravatarIfAvailable) {
        setAvatarUrl(existingAvatarUrl);
        return;
      }

      // If no email available, use existing avatar or generate initials
      if (!userEmail) {
        if (existingAvatarUrl) {
          setAvatarUrl(existingAvatarUrl);
        } else if (userName || userEmail) {
          setAvatarUrl(gravatarService.generateInitialsAvatar(userName || userEmail || 'User'));
        } else {
          setAvatarUrl(gravatarService.getDefaultAvatarUrl());
        }
        return;
      }

      setIsLoading(true);
      try {
        let finalAvatarUrl = '';

        if (preferences) {
          // Use provided preferences
          finalAvatarUrl = await gravatarService.getBestAvatarUrl(userEmail, preferences);
        } else if (showGravatarIfAvailable) {
          // Simple Gravatar check - try Gravatar first, fallback to existing or initials
          const gravatarUrl = gravatarService.generateGravatarUrl(userEmail);
          const gravatarExists = await gravatarService.checkGravatarExists(userEmail);
          
          if (gravatarExists) {
            finalAvatarUrl = gravatarUrl;
          } else if (existingAvatarUrl) {
            finalAvatarUrl = existingAvatarUrl;
          } else {
            finalAvatarUrl = gravatarService.generateInitialsAvatar(userName || userEmail);
          }
        } else {
          // Use existing avatar or generate initials
          finalAvatarUrl = existingAvatarUrl || gravatarService.generateInitialsAvatar(userName || userEmail);
        }

        setAvatarUrl(finalAvatarUrl);
      } catch (error) {
        console.error('Error loading avatar:', error);
        // Fallback to existing avatar or initials
        if (existingAvatarUrl) {
          setAvatarUrl(existingAvatarUrl);
        } else if (userName || userEmail) {
          setAvatarUrl(gravatarService.generateInitialsAvatar(userName || userEmail));
        } else {
          setAvatarUrl(gravatarService.getDefaultAvatarUrl());
        }
      } finally {
        setIsLoading(false);
      }
    };

    loadAvatar();
  }, [userEmail, userName, existingAvatarUrl, preferences, showGravatarIfAvailable]);

  return (
    <Avatar className={cn(sizeClasses[size], className)}>
      <AvatarImage 
        src={avatarUrl} 
        alt={userName || userEmail || 'User'} 
      />
      <AvatarFallback>
        {isLoading ? (
          <div className="animate-pulse bg-muted rounded-full w-full h-full" />
        ) : (
          getInitials()
        )}
      </AvatarFallback>
    </Avatar>
  );
}; 