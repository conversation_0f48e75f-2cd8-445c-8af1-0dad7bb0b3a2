[**Word by Word Story - UI Components v0.0.0**](../README.md)

***

[Word by Word Story - UI Components](../modules.md) / sidebar

# sidebar

## Variables

- [Sidebar](variables/Sidebar.md)
- [SidebarContent](variables/SidebarContent.md)
- [SidebarFooter](variables/SidebarFooter.md)
- [SidebarGroup](variables/SidebarGroup.md)
- [SidebarGroupAction](variables/SidebarGroupAction.md)
- [SidebarGroupContent](variables/SidebarGroupContent.md)
- [SidebarGroupLabel](variables/SidebarGroupLabel.md)
- [SidebarHeader](variables/SidebarHeader.md)
- [SidebarInput](variables/SidebarInput.md)
- [SidebarInset](variables/SidebarInset.md)
- [SidebarMenu](variables/SidebarMenu.md)
- [SidebarMenuAction](variables/SidebarMenuAction.md)
- [SidebarMenuBadge](variables/SidebarMenuBadge.md)
- [SidebarMenuButton](variables/SidebarMenuButton.md)
- [SidebarMenuItem](variables/SidebarMenuItem.md)
- [SidebarMenuSkeleton](variables/SidebarMenuSkeleton.md)
- [SidebarMenuSub](variables/SidebarMenuSub.md)
- [SidebarMenuSubButton](variables/SidebarMenuSubButton.md)
- [SidebarMenuSubItem](variables/SidebarMenuSubItem.md)
- [SidebarProvider](variables/SidebarProvider.md)
- [SidebarRail](variables/SidebarRail.md)
- [SidebarSeparator](variables/SidebarSeparator.md)
- [SidebarTrigger](variables/SidebarTrigger.md)

## Functions

- [useSidebar](functions/useSidebar.md)
