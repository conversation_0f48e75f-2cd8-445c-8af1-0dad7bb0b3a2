[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [chart](../README.md) / ChartTooltipContent

# Variable: ChartTooltipContent

> `const` **ChartTooltipContent**: `ForwardRefExoticComponent`\<`Omit`\<`Props`\<`ValueType`, `NameType`\> & `object` & `ClassAttributes`\<`HTMLDivElement`\> & `HTMLAttributes`\<`HTMLDivElement`\> & `object`, `"ref"`\> & `RefAttributes`\<`HTMLDivElement`\>\>

Defined in: [src/components/ui/chart.tsx:103](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/chart.tsx#L103)
