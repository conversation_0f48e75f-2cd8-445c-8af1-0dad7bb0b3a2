import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Loader2, 
  History, 
  ArrowUpRight, 
  ArrowDownLeft, 
  ShoppingCart, 
  Send, 
  Coins,
  Calendar,
  Filter,
  Download
} from 'lucide-react';
import { useAuth } from '@/contexts/auth';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';

interface Transaction {
  id: string;
  type: 'purchase' | 'transfer_sent' | 'transfer_received' | 'special_action' | 'refund';
  amount: number;
  description: string;
  timestamp: string;
  status: 'completed' | 'pending' | 'failed';
  metadata?: {
    // For purchases
    packageId?: string;
    stripeSessionId?: string;
    // For transfers
    fromUserId?: string;
    fromUsername?: string;
    toUserId?: string;
    toUsername?: string;
    message?: string;
    // For special actions
    actionType?: 'gotcha' | 'reverse' | 'golden';
    storyId?: string;
    storyTitle?: string;
  };
}

interface TransactionHistoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const TRANSACTION_TYPES = [
  { value: 'all', label: 'All Transactions' },
  { value: 'purchase', label: 'Purchases' },
  { value: 'transfer_sent', label: 'Transfers Sent' },
  { value: 'transfer_received', label: 'Transfers Received' },
  { value: 'special_action', label: 'Special Actions' },
  { value: 'refund', label: 'Refunds' },
];

const TIME_PERIODS = [
  { value: 'all', label: 'All Time' },
  { value: '7d', label: 'Last 7 Days' },
  { value: '30d', label: 'Last 30 Days' },
  { value: '90d', label: 'Last 90 Days' },
  { value: '1y', label: 'Last Year' },
];

export const TransactionHistoryDialog: React.FC<TransactionHistoryDialogProps> = ({
  isOpen,
  onClose,
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [typeFilter, setTypeFilter] = useState('all');
  const [timeFilter, setTimeFilter] = useState('all');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // Load transactions
  useEffect(() => {
    const loadTransactions = async () => {
      if (!user || !isOpen) return;

      setLoading(true);
      try {
        const params = new URLSearchParams({
          userId: user.id,
          page: page.toString(),
          limit: '20',
        });

        if (typeFilter !== 'all') {
          params.append('type', typeFilter);
        }
        if (timeFilter !== 'all') {
          params.append('period', timeFilter);
        }

        const response = await fetch(`/api/v1/tokens/transactions?${params}`);
        
        if (response.ok) {
          const data = await response.json();
          
          if (page === 1) {
            setTransactions(data.transactions);
          } else {
            setTransactions(prev => [...prev, ...data.transactions]);
          }
          
          setHasMore(data.hasMore);
        } else {
          throw new Error('Failed to load transactions');
        }
      } catch (error) {
        console.error('Error loading transactions:', error);
        toast({
          title: 'Error Loading Transactions',
          description: 'Unable to load your transaction history. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    loadTransactions();
  }, [user, isOpen, typeFilter, timeFilter, page, toast]);

  // Reset page when filters change
  useEffect(() => {
    setPage(1);
    setTransactions([]);
  }, [typeFilter, timeFilter]);

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'purchase':
        return <ShoppingCart className="w-4 h-4 text-green-600" />;
      case 'transfer_sent':
        return <ArrowUpRight className="w-4 h-4 text-red-600" />;
      case 'transfer_received':
        return <ArrowDownLeft className="w-4 h-4 text-green-600" />;
      case 'special_action':
        return <Coins className="w-4 h-4 text-purple-600" />;
      case 'refund':
        return <ArrowDownLeft className="w-4 h-4 text-blue-600" />;
      default:
        return <History className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'purchase':
      case 'transfer_received':
      case 'refund':
        return 'text-green-600';
      case 'transfer_sent':
      case 'special_action':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getAmountDisplay = (transaction: Transaction) => {
    const isPositive = ['purchase', 'transfer_received', 'refund'].includes(transaction.type);
    const sign = isPositive ? '+' : '-';
    return `${sign}${transaction.amount}`;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Completed</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'failed':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Failed</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getUserInitials = (username: string) => {
    return username.slice(0, 2).toUpperCase();
  };

  const loadMore = () => {
    if (!loading && hasMore) {
      setPage(prev => prev + 1);
    }
  };

  const exportTransactions = async () => {
    if (!user) return;

    try {
      const params = new URLSearchParams({
        userId: user.id,
        format: 'csv',
      });

      if (typeFilter !== 'all') {
        params.append('type', typeFilter);
      }
      if (timeFilter !== 'all') {
        params.append('period', timeFilter);
      }

      const response = await fetch(`/api/v1/tokens/transactions/export?${params}`);
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `token-transactions-${format(new Date(), 'yyyy-MM-dd')}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast({
          title: 'Export Successful',
          description: 'Your transaction history has been downloaded.',
        });
      } else {
        throw new Error('Export failed');
      }
    } catch (error) {
      console.error('Error exporting transactions:', error);
      toast({
        title: 'Export Failed',
        description: 'Unable to export your transaction history. Please try again.',
        variant: 'destructive',
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <History className="w-5 h-5 text-blue-500" />
            Transaction History
          </DialogTitle>
          <DialogDescription>
            View your complete token transaction history including purchases, transfers, and special actions.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  {TRANSACTION_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1">
              <Select value={timeFilter} onValueChange={setTimeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by time" />
                </SelectTrigger>
                <SelectContent>
                  {TIME_PERIODS.map((period) => (
                    <SelectItem key={period.value} value={period.value}>
                      {period.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button variant="outline" onClick={exportTransactions} className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              Export
            </Button>
          </div>

          {/* Transaction List */}
          <ScrollArea className="h-[500px]">
            <div className="space-y-3">
              {transactions.map((transaction, index) => (
                <Card key={transaction.id} className="hover:shadow-sm transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        <div className="mt-1">
                          {getTransactionIcon(transaction.type)}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium text-sm">{transaction.description}</h4>
                            {getStatusBadge(transaction.status)}
                          </div>
                          
                          <div className="text-xs text-muted-foreground mb-2">
                            {format(new Date(transaction.timestamp), 'MMM dd, yyyy • h:mm a')}
                          </div>

                          {/* Additional details based on transaction type */}
                          {transaction.metadata && (
                            <div className="text-xs text-muted-foreground">
                              {transaction.type === 'transfer_sent' && transaction.metadata.toUsername && (
                                <div className="flex items-center gap-2">
                                  <span>To:</span>
                                  <div className="flex items-center gap-1">
                                    <Avatar className="w-4 h-4">
                                      <AvatarFallback className="text-xs">
                                        {getUserInitials(transaction.metadata.toUsername)}
                                      </AvatarFallback>
                                    </Avatar>
                                    <span>{transaction.metadata.toUsername}</span>
                                  </div>
                                </div>
                              )}
                              
                              {transaction.type === 'transfer_received' && transaction.metadata.fromUsername && (
                                <div className="flex items-center gap-2">
                                  <span>From:</span>
                                  <div className="flex items-center gap-1">
                                    <Avatar className="w-4 h-4">
                                      <AvatarFallback className="text-xs">
                                        {getUserInitials(transaction.metadata.fromUsername)}
                                      </AvatarFallback>
                                    </Avatar>
                                    <span>{transaction.metadata.fromUsername}</span>
                                  </div>
                                </div>
                              )}

                              {transaction.type === 'special_action' && transaction.metadata.actionType && (
                                <div>
                                  Action: <span className="capitalize">{transaction.metadata.actionType}</span>
                                  {transaction.metadata.storyTitle && (
                                    <span> in "{transaction.metadata.storyTitle}"</span>
                                  )}
                                </div>
                              )}

                              {transaction.metadata.message && (
                                <div className="mt-1 italic">"{transaction.metadata.message}"</div>
                              )}
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="text-right">
                        <div className={`font-semibold ${getTransactionColor(transaction.type)}`}>
                          {getAmountDisplay(transaction)} tokens
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {loading && (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="w-6 h-6 animate-spin" />
                  <span className="ml-2">Loading transactions...</span>
                </div>
              )}

              {!loading && transactions.length === 0 && (
                <Card>
                  <CardContent className="py-12 text-center">
                    <History className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Transactions Found</h3>
                    <p className="text-muted-foreground">
                      {typeFilter !== 'all' || timeFilter !== 'all'
                        ? 'No transactions match your current filters.'
                        : 'You haven\'t made any token transactions yet.'}
                    </p>
                  </CardContent>
                </Card>
              )}

              {!loading && hasMore && transactions.length > 0 && (
                <div className="text-center py-4">
                  <Button variant="outline" onClick={loadMore}>
                    Load More
                  </Button>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 