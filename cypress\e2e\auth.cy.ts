describe('Authentication Flow', () => {
  beforeEach(() => {
    // Clear any existing auth state
    cy.clearLocalStorage()
    cy.clearCookies()
  })

  it('should display the landing page correctly', () => {
    cy.visitAndWait('/')
    cy.contains('Word by Word Story').should('be.visible')
    cy.get('[data-testid="login-link"]').should('be.visible')
    cy.get('[data-testid="register-link"]').should('be.visible')
  })

  it('should navigate to login page', () => {
    cy.visitAndWait('/')
    cy.get('[data-testid="login-link"]').click()
    cy.url().should('include', '/login')
    cy.contains('Sign In').should('be.visible')
  })

  it('should navigate to register page', () => {
    cy.visitAndWait('/')
    cy.get('[data-testid="register-link"]').click()
    cy.url().should('include', '/register')
    cy.contains('Create Account').should('be.visible')
  })

  it('should show validation errors on empty login form', () => {
    cy.visitAndWait('/login')
    cy.get('[data-testid="login-button"]').click()
    cy.contains('Email is required').should('be.visible')
    cy.contains('Password is required').should('be.visible')
  })

  it('should show validation errors on empty register form', () => {
    cy.visitAndWait('/register')
    cy.get('[data-testid="register-button"]').click()
    cy.contains('Username is required').should('be.visible')
    cy.contains('Email is required').should('be.visible')
    cy.contains('Password is required').should('be.visible')
  })

  it('should handle invalid login credentials', () => {
    cy.visitAndWait('/login')
    cy.get('[data-testid="email-input"]').type('<EMAIL>')
    cy.get('[data-testid="password-input"]').type('wrongpassword')
    cy.get('[data-testid="login-button"]').click()
    
    // Should show error message
    cy.contains('Invalid credentials').should('be.visible')
    // Should remain on login page
    cy.url().should('include', '/login')
  })

  it('should successfully register a new user (mock)', () => {
    // Mock successful registration response
    cy.intercept('POST', '**/auth/v1/signup', { 
      statusCode: 200,
      body: { 
        user: { 
          id: 'mock-user-id',
          email: '<EMAIL>',
          user_metadata: { username: 'newuser' }
        } 
      }
    }).as('signupRequest')

    cy.visitAndWait('/register')
    cy.get('[data-testid="username-input"]').type('newuser')
    cy.get('[data-testid="email-input"]').type('<EMAIL>')
    cy.get('[data-testid="password-input"]').type('password123')
    cy.get('[data-testid="confirm-password-input"]').type('password123')
    cy.get('[data-testid="register-button"]').click()

    cy.wait('@signupRequest')
    cy.url().should('include', '/dashboard')
  })

  it('should successfully login with valid credentials (mock)', () => {
    // Mock successful login response
    cy.intercept('POST', '**/auth/v1/token**', {
      statusCode: 200,
      body: {
        access_token: 'mock-token',
        refresh_token: 'mock-refresh-token',
        user: {
          id: 'mock-user-id',
          email: '<EMAIL>',
          user_metadata: { username: 'testuser' }
        }
      }
    }).as('loginRequest')

    cy.visitAndWait('/login')
    cy.get('[data-testid="email-input"]').type('<EMAIL>')
    cy.get('[data-testid="password-input"]').type('testpassword123')
    cy.get('[data-testid="login-button"]').click()

    cy.wait('@loginRequest')
    cy.url().should('include', '/dashboard')
  })

  it('should logout successfully', () => {
    // First login
    cy.mockSupabaseAuth()
    cy.visitAndWait('/dashboard')
    
    // Then logout
    cy.get('[data-testid="user-menu"]').click()
    cy.get('[data-testid="logout-button"]').click()
    
    // Should redirect to home page
    cy.url().should('eq', Cypress.config().baseUrl + '/')
    // Should show login link again
    cy.get('[data-testid="login-link"]').should('be.visible')
  })

  it('should persist login state across page refreshes', () => {
    cy.mockSupabaseAuth()
    cy.visitAndWait('/dashboard')
    
    // Refresh the page
    cy.reload()
    
    // Should still be logged in
    cy.url().should('include', '/dashboard')
    cy.get('[data-testid="user-menu"]').should('be.visible')
  })

  it('should redirect unauthenticated users from protected routes', () => {
    cy.visitAndWait('/dashboard')
    cy.url().should('include', '/login')
    
    cy.visitAndWait('/create-story')
    cy.url().should('include', '/login')
    
    cy.visitAndWait('/profile')
    cy.url().should('include', '/login')
  })
})