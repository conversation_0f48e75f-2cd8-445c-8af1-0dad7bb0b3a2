import React, { useState } from 'react';
import { useStory } from '@/contexts/StoryContext';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { trackStoryEvent } from '@/lib/posthog';
import { TskTskTskButton } from '@/components/story/TskTskTskButton';
import { QuickUndoTimer } from '@/components/story/QuickUndoTimer';
import { undoContribution } from '@/services/undoService';

interface StoryContributionInputProps {
  storyId: string; // Pass storyId if not relying solely on currentStory from context for submission target
}

const StoryContributionInput: React.FC<StoryContributionInputProps> = ({ storyId }) => {
  const { currentStory, addContributionToStory, handleUserTyping } = useStory();
  const { toast } = useToast();
  const [contributionText, setContributionText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [recentContribution, setRecentContribution] = useState<{
    id: string;
    content: string;
    timestamp: number;
  } | null>(null);
  const { user } = useAuth();

  const validateContribution = (text: string): { isValid: boolean; message: string | null } => {
    if (!text.trim()) {
      return { isValid: false, message: "Contribution cannot be empty." };
    }
    if (currentStory && currentStory.max_words_per_contribution > 0) {
      const wordCount = text.trim().split(/\s+/).length;
      if (wordCount > currentStory.max_words_per_contribution) {
        return { isValid: false, message: `Contribution exceeds maximum of ${currentStory.max_words_per_contribution} words.` };
      }
    }
    if (currentStory && currentStory.contributions && currentStory.contributions.length > 0 && user) {
      const lastContribution = currentStory.contributions[currentStory.contributions.length - 1];
      if (lastContribution.author.id === user.id) {
        return { isValid: false, message: "You cannot make two contributions in a row. Wait for someone else to contribute." };
      }
    }
    return { isValid: true, message: null };
  };

  const handleSubmitContribution = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    handleUserTyping(false);

    if (!currentStory || currentStory.id !== storyId) {
      setError("Story context error. Please refresh.");
      return;
    }
    if (!user) {
      setError("You must be logged in to contribute.");
      return;
    }

    const validation = validateContribution(contributionText);
    if (!validation.isValid) {
      setError(validation.message);
      return;
    }

    setIsSubmitting(true);

    const { data, error: submissionError } = await addContributionToStory(contributionText);

    if (submissionError) {
      const errorMessage = submissionError.message || "Failed to add contribution. Please try again.";
      setError(errorMessage);
      toast({
        title: 'Contribution Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } else {
      // Track successful story contribution
      if (currentStory && data) {
        const contributionType = currentStory.max_words_per_contribution === 1 ? 'word' : 
                               contributionText.trim().split(/\s+/).length === 1 ? 'word' :
                               contributionText.trim().includes('.') || contributionText.trim().includes('!') || contributionText.trim().includes('?') ? 'sentence' : 
                               contributionText.trim().split(/\s+/).length > 10 ? 'paragraph' : 'word';
        
        trackStoryEvent.storyContribution(currentStory.id, contributionType, {
          contribution_text: contributionText.trim(),
          contribution_length: contributionText.trim().length,
          word_count: contributionText.trim().split(/\s+/).length,
          story_title: currentStory.title,
          story_total_contributions: currentStory.contributions?.length || 0,
          contribution_timestamp: new Date().toISOString(),
        });
      }
      
      toast({
        title: 'Contribution Added!',
        description: 'Your contribution has been successfully added to the story.',
      });
      
      // Set recent contribution for quick undo
      if (data && data.id) {
        setRecentContribution({
          id: data.id,
          content: contributionText.trim(),
          timestamp: Date.now(),
        });
      }
      
      setContributionText('');
    }
    setIsSubmitting(false);
  };

  if (!currentStory || currentStory.id !== storyId) {
    return <p className="text-sm text-gray-500">Loading story details or context mismatch...</p>;
  }
  
  if (currentStory.status === 'completed' || currentStory.status === 'abandoned') {
    return <p className="text-sm text-gray-500">This story is {currentStory.status} and no longer accepts contributions.</p>;
  }

  const canContribute = currentStory.contributions.length === 0 || 
                        (user && currentStory.contributions[currentStory.contributions.length - 1]?.author.id !== user.id);

  // Handle undo functionality
  const handleUndo = async (contributionId: string) => {
    if (!user) {
      toast({
        title: 'Authentication Required',
        description: 'You must be logged in to use undo.',
        variant: 'destructive',
      });
      return;
    }

    try {
      const result = await undoContribution(contributionId, user.id);
      
      if (result.success) {
        toast({
          title: 'Contribution Undone',
          description: result.message,
        });
        
        // Refresh the story to show updated contributions
        if (currentStory) {
          // You might want to trigger a story reload here
          window.location.reload(); // Simple solution for now
        }
      } else {
        toast({
          title: 'Undo Failed',
          description: result.message,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Undo Error',
        description: 'Failed to undo contribution. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Get the last contribution for undo functionality
  const lastContribution = currentStory.contributions.length > 0 
    ? currentStory.contributions[currentStory.contributions.length - 1]
    : undefined;
  
  // Convert to the format expected by TskTskTskButton
  const lastContributionForUndo = lastContribution ? {
    id: lastContribution.id,
    content: lastContribution.content,
    author: {
      id: lastContribution.author.id,
      username: lastContribution.author.username || 'Unknown User'
    },
    created_at: lastContribution.created_at
  } : undefined;
  
  // Check if user can undo (last contribution exists and is not from current user)
  const canUndo = lastContribution && user && lastContribution.author.id !== user.id;

  // Handle quick undo for own contributions
  const handleQuickUndo = async (contributionId: string) => {
    try {
      const result = await undoContribution(contributionId, user?.id || '');
      setRecentContribution(null);
      
      if (result.success) {
        toast({
          title: 'Contribution Undone',
          description: 'Your contribution has been removed.',
        });
        
        // Refresh the story to show updated contributions
        if (currentStory) {
          window.location.reload(); // Simple solution for now
        }
      } else {
        toast({
          title: 'Quick Undo Failed',
          description: result.message,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Quick Undo Failed',
        description: 'Failed to undo your contribution. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleQuickUndoExpire = () => {
    setRecentContribution(null);
  };

  return (
    <div className="space-y-4 mt-4">
      {/* Quick Undo Timer - appears immediately after user submits their own contribution */}
      {recentContribution && (
        <div className="flex justify-center">
          <QuickUndoTimer
            contributionId={recentContribution.id}
            contributionContent={recentContribution.content}
            onUndo={handleQuickUndo}
            onExpire={handleQuickUndoExpire}
            duration={3}
          />
        </div>
      )}
      
      {/* Tsk-Tsk-Tsk Undo Button */}
      {canUndo && !recentContribution && (
        <div className="flex justify-center">
                     <TskTskTskButton
             storyId={storyId}
             canUndo={canUndo}
             lastContribution={lastContributionForUndo}
             onUndo={handleUndo}
             disabled={isSubmitting}
           />
        </div>
      )}
      
      <form onSubmit={handleSubmitContribution} className="space-y-4 p-4 border rounded-lg shadow-md">
      <Label htmlFor={`contribution-input-${storyId}`} className="text-lg font-semibold">
        Add Your Contribution
      </Label>
      <Textarea
        id={`contribution-input-${storyId}`}
        value={contributionText}
        onChange={(e) => {
          const newText = e.target.value;
          setContributionText(newText);
          if (error) setError(null);
          handleUserTyping(newText.trim().length > 0);
        }}
        onBlur={() => {
          handleUserTyping(false);
        }}
        placeholder={`Continue the story... (max ${currentStory.max_words_per_contribution} words)`}
        rows={4}
        className={error ? 'border-red-500' : ''}
        disabled={isSubmitting || !canContribute}
      />
      {!canContribute && currentStory.contributions.length > 0 && (
        <p className="text-orange-600 text-xs mt-1">It's not your turn. Wait for someone else to contribute.</p>
      )}
      {error && <p className="text-red-500 text-xs mt-1">{error}</p>}
      <Button 
        type="submit" 
        className="w-full" 
        disabled={isSubmitting || !contributionText.trim() || !canContribute}
      >
        {isSubmitting ? (
          <><Loader2 className="mr-2 h-4 w-4 animate-spin" /> Submitting...</>
        ) : (
          'Add Contribution'
        )}
      </Button>
    </form>
    </div>
  );
};

export default StoryContributionInput; 