import { 
  signInWithPopup, 
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  User as FirebaseUser,
  sendPasswordResetEmail
} from 'firebase/auth';
import { auth, googleProvider } from '@/lib/firebase';
import { supabase } from '@/lib/supabase';

export interface User {
  id: string;
  email: string | null;
  username: string;
  tier: "free" | "premium";
  credits: number;
  createdAt: string;
  updatedAt: string;
}

// Sync Firebase user to Supabase database
const syncUserToSupabase = async (firebaseUser: FirebaseUser): Promise<User> => {
  const userData = {
    id: firebaseUser.uid,
    email: firebaseUser.email,
    username: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User',
    tier: 'free',
    credits: 0,
    created_at: firebaseUser.metadata.creationTime,
    updated_at: new Date().toISOString(),
  };

  // Upsert user in Supabase
  const { data, error } = await supabase
    .from('users')
    .upsert(userData, { onConflict: 'id' })
    .select()
    .single();

  if (error) {
    console.error('Error syncing user to Supabase:', error);
    // Return user data even if sync fails
    return {
      id: userData.id,
      email: userData.email,
      username: userData.username,
      tier: "free" as const,
      credits: userData.credits,
      createdAt: userData.created_at || new Date().toISOString(),
      updatedAt: userData.updated_at,
    };
  }

  return {
    id: data.id,
    email: data.email,
    username: data.username,
    tier: data.tier,
    credits: data.credits,
    createdAt: data.created_at,
    updatedAt: data.updated_at,
  };
};

// Sign in with Google
export const signInWithGoogle = async (): Promise<User> => {
  try {
    const result = await signInWithPopup(auth, googleProvider);
    const user = await syncUserToSupabase(result.user);
    return user;
  } catch (error) {
    console.error('Error signing in with Google:', error);
    throw error;
  }
};

// Sign in with email and password
export const signInWithEmail = async (email: string, password: string): Promise<User> => {
  try {
    const result = await signInWithEmailAndPassword(auth, email, password);
    const user = await syncUserToSupabase(result.user);
    return user;
  } catch (error) {
    console.error('Error signing in with email:', error);
    throw error;
  }
};

// Sign up with email and password
export const signUpWithEmail = async (email: string, password: string, username?: string): Promise<User> => {
  try {
    const result = await createUserWithEmailAndPassword(auth, email, password);
    
    // Update display name if username provided
    if (username && result.user) {
      await result.user.updateProfile({ displayName: username });
    }
    
    const user = await syncUserToSupabase(result.user);
    return user;
  } catch (error) {
    console.error('Error signing up with email:', error);
    throw error;
  }
};

// Sign out
export const signOut = async (): Promise<void> => {
  try {
    await firebaseSignOut(auth);
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};

// Reset password
export const resetPassword = async (email: string): Promise<void> => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    console.error('Error sending password reset email:', error);
    throw error;
  }
};

// Auth state listener
export const onAuthStateChange = (callback: (user: User | null) => void) => {
  return onAuthStateChanged(auth, async (firebaseUser) => {
    if (firebaseUser) {
      try {
        const user = await syncUserToSupabase(firebaseUser);
        callback(user);
      } catch (error) {
        console.error('Error syncing user:', error);
        callback(null);
      }
    } else {
      callback(null);
    }
  });
}; 