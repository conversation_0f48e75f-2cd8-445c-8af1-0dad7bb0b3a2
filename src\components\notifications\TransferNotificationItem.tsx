import React from 'react';
import { formatDistanceToNow } from 'date-fns';
import { Check, X, Alert<PERSON>riangle, ArrowUpRight, ArrowDownLeft, TrendingUp } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface TransferNotificationContent {
  message: string;
  amount?: number;
  recipient_username?: string;
  sender_username?: string;
  transfer_id?: string;
  new_balance?: number;
  transfer_message?: string;
  error?: string;
  limit_type?: 'daily' | 'single';
  remaining_amount?: number;
  period?: 'weekly' | 'monthly';
  total_sent?: number;
  total_received?: number;
  transfer_count?: number;
}

export interface TransferNotificationItemProps {
  id: string;
  type: 'transfer_sent' | 'transfer_received' | 'transfer_failed' | 'transfer_limit' | 'transfer_summary';
  content: TransferNotificationContent;
  isRead: boolean;
  createdAt: string;
  onMarkAsRead: (id: string) => void;
  onViewTransfer?: (transferId: string) => void;
  className?: string;
}

export const TransferNotificationItem: React.FC<TransferNotificationItemProps> = ({
  id,
  type,
  content,
  isRead,
  createdAt,
  onMarkAsRead,
  onViewTransfer,
  className,
}) => {
  const getNotificationIcon = () => {
    switch (type) {
      case 'transfer_sent':
        return <ArrowUpRight className="w-5 h-5 text-blue-500" />;
      case 'transfer_received':
        return <ArrowDownLeft className="w-5 h-5 text-green-500" />;
      case 'transfer_failed':
        return <X className="w-5 h-5 text-red-500" />;
      case 'transfer_limit':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'transfer_summary':
        return <TrendingUp className="w-5 h-5 text-purple-500" />;
      default:
        return <Check className="w-5 h-5 text-gray-500" />;
    }
  };

  const getNotificationColor = () => {
    switch (type) {
      case 'transfer_sent':
        return 'blue';
      case 'transfer_received':
        return 'green';
      case 'transfer_failed':
        return 'red';
      case 'transfer_limit':
        return 'yellow';
      case 'transfer_summary':
        return 'purple';
      default:
        return 'gray';
    }
  };

  const formatTokenAmount = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const handleViewTransfer = () => {
    if (content.transfer_id && onViewTransfer) {
      onViewTransfer(content.transfer_id);
    }
  };

  const color = getNotificationColor();

  return (
    <div 
      className={cn(
        'p-4 border rounded-lg transition-all duration-200 hover:shadow-md',
        !isRead && 'bg-blue-50 border-blue-200 dark:bg-blue-950 dark:border-blue-800',
        isRead && 'bg-white border-gray-200 dark:bg-gray-800 dark:border-gray-700',
        className
      )}
    >
      <div className="flex items-start gap-3">
        {/* Icon */}
        <div className={cn(
          'flex-shrink-0 p-2 rounded-full',
          `bg-${color}-100 dark:bg-${color}-900`
        )}>
          {getNotificationIcon()}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          {/* Header with type and timestamp */}
          <div className="flex items-center justify-between mb-2">
            <Badge 
              variant="outline" 
              className={cn(
                'text-xs',
                `border-${color}-300 text-${color}-700 dark:border-${color}-600 dark:text-${color}-300`
              )}
            >
              {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </Badge>
            <span className="text-xs text-muted-foreground">
              {formatDistanceToNow(new Date(createdAt), { addSuffix: true })}
            </span>
          </div>

          {/* Main message */}
          <p className={cn(
            'text-sm mb-2',
            !isRead && 'font-medium text-gray-900 dark:text-gray-100',
            isRead && 'text-gray-700 dark:text-gray-300'
          )}>
            {content.message}
          </p>

          {/* Transfer details */}
          {(content.amount || content.new_balance !== undefined) && (
            <div className="flex flex-wrap gap-2 mb-2">
              {content.amount && (
                <Badge variant="secondary" className="text-xs">
                  {formatTokenAmount(content.amount)} tokens
                </Badge>
              )}
              {content.new_balance !== undefined && (
                <Badge variant="outline" className="text-xs">
                  Balance: {formatTokenAmount(content.new_balance)}
                </Badge>
              )}
              {content.remaining_amount !== undefined && (
                <Badge variant="outline" className="text-xs text-yellow-700 border-yellow-300">
                  {formatTokenAmount(content.remaining_amount)} remaining today
                </Badge>
              )}
            </div>
          )}

          {/* Transfer message */}
          {content.transfer_message && (
            <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs italic">
              "{content.transfer_message}"
            </div>
          )}

          {/* Error details */}
          {content.error && (
            <div className="mt-2 p-2 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded text-xs text-red-700 dark:text-red-300">
              {content.error}
            </div>
          )}

          {/* Summary details for transfer_summary type */}
          {type === 'transfer_summary' && (
            <div className="mt-2 grid grid-cols-3 gap-2 text-xs">
              <div className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded">
                <div className="font-semibold">{content.transfer_count || 0}</div>
                <div className="text-muted-foreground">Transfers</div>
              </div>
              <div className="text-center p-2 bg-green-50 dark:bg-green-900 rounded">
                <div className="font-semibold">{formatTokenAmount(content.total_received || 0)}</div>
                <div className="text-muted-foreground">Received</div>
              </div>
              <div className="text-center p-2 bg-blue-50 dark:bg-blue-900 rounded">
                <div className="font-semibold">{formatTokenAmount(content.total_sent || 0)}</div>
                <div className="text-muted-foreground">Sent</div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center gap-2 mt-3">
            {!isRead && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onMarkAsRead(id)}
                className="h-7 px-2 text-xs"
              >
                Mark as read
              </Button>
            )}
            
            {content.transfer_id && onViewTransfer && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleViewTransfer}
                className="h-7 px-2 text-xs"
              >
                View Transfer
              </Button>
            )}
            
            {(type === 'transfer_sent' || type === 'transfer_received') && content.recipient_username && (
              <Button
                variant="ghost"
                size="sm"
                className="h-7 px-2 text-xs"
                onClick={() => {
                  // Could trigger a new transfer to the same user
                  console.log('Start new transfer to:', content.recipient_username || content.sender_username);
                }}
              >
                Send Again
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TransferNotificationItem; 