import { describe, it, expect, vi, beforeEach } from 'vitest';
import { Request, Response, NextFunction } from 'express';
import UserAuthMiddleware from '../middleware/userAuthMiddleware';

// Mock Supabase
vi.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getUser: vi.fn(),
    },
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn(),
    })),
  },
}));

describe('UserAuthMiddleware', () => {
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockReq = {
      headers: {},
      cookies: {},
      params: {},
      body: {},
      query: {},
    };
    mockRes = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn().mockReturnThis(),
    };
    mockNext = vi.fn();
    vi.clearAllMocks();
  });

  describe('extractToken', () => {
    it('should extract token from Authorization header', () => {
      mockReq.headers = { authorization: 'Bearer test-token' };
      const token = UserAuthMiddleware.extractToken(mockReq as Request);
      expect(token).toBe('test-token');
    });

    it('should extract token from cookies', () => {
      mockReq.cookies = { 'sb-access-token': 'cookie-token' };
      const token = UserAuthMiddleware.extractToken(mockReq as Request);
      expect(token).toBe('cookie-token');
    });

    it('should return null if no token found', () => {
      const token = UserAuthMiddleware.extractToken(mockReq as Request);
      expect(token).toBeNull();
    });
  });

  describe('requireAuth', () => {
    it('should return 401 if no token provided and required', async () => {
      const middleware = UserAuthMiddleware.requireAuth({ optional: false });
      
      await middleware(mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Authentication required',
        message: 'Please provide a valid JWT token in the Authorization header',
        code: 'AUTH_TOKEN_MISSING'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should call next if no token provided but optional', async () => {
      const middleware = UserAuthMiddleware.requireAuth({ optional: true });
      
      await middleware(mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    it('should validate token and attach user to request', async () => {
      const mockUser = { 
        id: 'user-123', 
        email: '<EMAIL>',
        app_metadata: {},
        user_metadata: {},
        aud: 'authenticated',
        created_at: '2024-01-01T00:00:00Z'
      };
      const { supabase } = await import('@/lib/supabase');
      vi.mocked(supabase.auth.getUser).mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      mockReq.headers = { authorization: 'Bearer valid-token' };
      const middleware = UserAuthMiddleware.requireAuth();
      
      await middleware(mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockReq.user).toBe(mockUser);
      expect(mockReq.userId).toBe('user-123');
      expect(mockNext).toHaveBeenCalled();
    });

    it('should return 401 for invalid token', async () => {
      const { supabase } = await import('@/lib/supabase');
      vi.mocked(supabase.auth.getUser).mockResolvedValue({
        data: { user: null },
        error: { 
          message: 'Invalid token'
        } as any,
      });

      mockReq.headers = { authorization: 'Bearer invalid-token' };
      const middleware = UserAuthMiddleware.requireAuth();
      
      await middleware(mockReq as Request, mockRes as Response, mockNext);
      
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Invalid authentication token',
        message: 'Invalid token',
        code: 'AUTH_TOKEN_INVALID'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('requireApiKeyOwnership', () => {
    it('should return 401 if user not authenticated', async () => {
      await UserAuthMiddleware.requireApiKeyOwnership(
        mockReq as Request, 
        mockRes as Response, 
        mockNext
      );
      
      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        error: 'Authentication required',
        message: 'User authentication is required for API key operations',
        code: 'AUTH_REQUIRED'
      });
    });

    it('should proceed if no keyId in params', async () => {
      mockReq.user = { id: 'user-123' } as any;
      mockReq.userId = 'user-123';
      
      await UserAuthMiddleware.requireApiKeyOwnership(
        mockReq as Request, 
        mockRes as Response, 
        mockNext
      );
      
      expect(mockNext).toHaveBeenCalled();
    });

    it('should verify API key ownership', async () => {
      mockReq.user = { id: 'user-123' } as any;
      mockReq.userId = 'user-123';
      mockReq.params = { keyId: 'key-123' };

      const { supabase } = await import('@/lib/supabase');
      const mockFrom = vi.fn().mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: { user_id: 'user-123' },
              error: null,
            }),
          }),
        }),
      });
      vi.mocked(supabase.from).mockImplementation(mockFrom);
      
      await UserAuthMiddleware.requireApiKeyOwnership(
        mockReq as Request, 
        mockRes as Response, 
        mockNext
      );
      
      expect(mockNext).toHaveBeenCalled();
      expect(supabase.from).toHaveBeenCalledWith('api_keys');
    });
  });

  describe('convenience methods', () => {
    it('requireUser should call requireAuth with correct options', () => {
      const spy = vi.spyOn(UserAuthMiddleware, 'requireAuth');
      UserAuthMiddleware.requireUser();
      expect(spy).toHaveBeenCalledWith({ optional: false });
    });

    it('requireAdmin should call requireAuth with admin flag', () => {
      const spy = vi.spyOn(UserAuthMiddleware, 'requireAuth');
      UserAuthMiddleware.requireAdmin();
      expect(spy).toHaveBeenCalledWith({ adminOnly: true });
    });

    it('optionalAuth should call requireAuth with optional flag', () => {
      const spy = vi.spyOn(UserAuthMiddleware, 'requireAuth');
      UserAuthMiddleware.optionalAuth();
      expect(spy).toHaveBeenCalledWith({ optional: true });
    });
  });
}); 