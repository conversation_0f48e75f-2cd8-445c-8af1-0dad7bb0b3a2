import React, { useState, useCallback, useEffect } from 'react';
import { useAuth } from '@/contexts/auth/hooks';
import { transferService, TransferRequest, TokenTransferUser } from '@/services/transferService';
import { useUserTokens } from '@/hooks/useUserTokens';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  Send, 
  Search, 
  User, 
  Coins, 
  AlertCircle, 
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Clock,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface TokenTransferInterfaceProps {
  onTransferComplete?: (transferId: string) => void;
  onClose?: () => void;
  defaultRecipient?: string;
}

interface UserSearchResult extends TokenTransferUser {
  isSelected: boolean;
}

export const TokenTransferInterface: React.FC<TokenTransferInterfaceProps> = ({
  onTransferComplete,
  onClose,
  defaultRecipient = ''
}) => {
  const { user } = useAuth();
  const { tokenBalance, refreshBalance } = useUserTokens();
  const { toast } = useToast();

  // Form state
  const [recipientUsername, setRecipientUsername] = useState(defaultRecipient);
  const [amount, setAmount] = useState('');
  const [transferMessage, setTransferMessage] = useState('');
  
  // UI state
  const [isTransferring, setIsTransferring] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<UserSearchResult[]>([]);
  const [selectedRecipient, setSelectedRecipient] = useState<TokenTransferUser | null>(null);
  const [validationError, setValidationError] = useState<string>('');
  const [transferLimits, setTransferLimits] = useState<{
    remainingDaily: number;
    todayTransferred: number;
  } | null>(null);

  // Search for users
  const searchUsers = useCallback(async (searchTerm: string) => {
    if (!searchTerm || searchTerm.length < 2) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const results = await transferService.searchUsersByUsername(searchTerm, 8);
      setSearchResults(results.map(user => ({ ...user, isSelected: false })));
    } catch (error) {
      console.error('Error searching users:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  }, []);

  // Handle recipient selection
  const handleRecipientSelect = useCallback((recipient: TokenTransferUser) => {
    setSelectedRecipient(recipient);
    setRecipientUsername(recipient.username);
    setSearchResults([]);
    setValidationError('');
  }, []);

  // Validate recipient when username changes
  const validateRecipient = useCallback(async (username: string) => {
    if (!username || username.length < 2) {
      setSelectedRecipient(null);
      setValidationError('');
      return;
    }

    try {
      const recipient = await transferService.findUserByUsername(username);
      if (recipient) {
        setSelectedRecipient(recipient);
        setValidationError('');
      } else {
        setSelectedRecipient(null);
        setValidationError(`User '@${username}' not found`);
      }
    } catch (error) {
      setSelectedRecipient(null);
      setValidationError('Failed to validate recipient');
    }
  }, []);

  // Load transfer limits
  const loadTransferLimits = useCallback(async () => {
    if (!user?.id) return;

    try {
      const stats = await transferService.getTransferStats(user.id);
      setTransferLimits({
        remainingDaily: stats.remainingDailyLimit,
        todayTransferred: stats.todayTransferred
      });
    } catch (error) {
      console.error('Error loading transfer limits:', error);
    }
  }, [user?.id]);

  // Handle transfer submission
  const handleTransfer = useCallback(async () => {
    if (!user?.id || !selectedRecipient || !amount) return;

    const transferAmount = parseInt(amount);
    if (isNaN(transferAmount) || transferAmount <= 0) {
      setValidationError('Please enter a valid amount');
      return;
    }

    setIsTransferring(true);
    setValidationError('');

    try {
      const transferRequest: TransferRequest = {
        senderId: user.id,
        recipientUsername: selectedRecipient.username,
        amount: transferAmount,
        transferMessage: transferMessage.trim() || undefined
      };

      const result = await transferService.executeTransfer(transferRequest);

      if (result.success) {
        toast({
          title: 'Transfer Successful!',
          description: `Sent ${transferAmount} tokens to @${selectedRecipient.username}`,
          variant: 'default',
        });

        // Reset form
        setRecipientUsername('');
        setAmount('');
        setTransferMessage('');
        setSelectedRecipient(null);
        
        // Refresh balance and limits
        await refreshBalance();
        await loadTransferLimits();

        // Notify parent component
        if (onTransferComplete && result.transferId) {
          onTransferComplete(result.transferId);
        }

      } else {
        setValidationError(result.error || 'Transfer failed');
        toast({
          title: 'Transfer Failed',
          description: result.error || 'Unknown error occurred',
          variant: 'destructive',
        });
      }

    } catch (error) {
      console.error('Error executing transfer:', error);
      setValidationError('Failed to process transfer');
      toast({
        title: 'Transfer Error',
        description: 'Failed to process transfer. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsTransferring(false);
    }
  }, [user?.id, selectedRecipient, amount, transferMessage, toast, onTransferComplete, refreshBalance, loadTransferLimits]);

  // Effect for recipient search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (recipientUsername && !selectedRecipient) {
        searchUsers(recipientUsername);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [recipientUsername, selectedRecipient, searchUsers]);

  // Effect for recipient validation
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (recipientUsername && !searchResults.length) {
        validateRecipient(recipientUsername);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [recipientUsername, searchResults.length, validateRecipient]);

  // Load initial data
  useEffect(() => {
    loadTransferLimits();
  }, [loadTransferLimits]);

  const transferAmount = parseInt(amount) || 0;
  const canAffordTransfer = transferAmount > 0 && transferAmount <= tokenBalance;
  const isValidTransfer = selectedRecipient && canAffordTransfer && !validationError;

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Send className="w-5 h-5" />
          Send Tokens
        </CardTitle>
        <CardDescription>
          Transfer tokens to other users by username
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Current Balance & Limits */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
            <Coins className="w-4 h-4 text-amber-600" />
            <div>
              <div className="text-sm font-medium">{tokenBalance}</div>
              <div className="text-xs text-muted-foreground">Available</div>
            </div>
          </div>
          {transferLimits && (
            <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
              <Clock className="w-4 h-4 text-blue-600" />
              <div>
                <div className="text-sm font-medium">{transferLimits.remainingDaily}</div>
                <div className="text-xs text-muted-foreground">Daily Limit</div>
              </div>
            </div>
          )}
        </div>

        {/* Recipient Search */}
        <div className="space-y-2">
          <Label htmlFor="recipient">Recipient Username</Label>
          <div className="relative">
            <Input
              id="recipient"
              placeholder="@username"
              value={recipientUsername}
              onChange={(e) => setRecipientUsername(e.target.value)}
              className={cn(
                'pl-8',
                validationError && 'border-destructive',
                selectedRecipient && 'border-green-500'
              )}
            />
            <Search className="w-4 h-4 absolute left-2.5 top-2.5 text-muted-foreground" />
            {isSearching && (
              <div className="absolute right-2.5 top-2.5">
                <div className="w-4 h-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
              </div>
            )}
          </div>

          {/* Search Results */}
          {searchResults.length > 0 && (
            <div className="border rounded-md bg-background max-h-48 overflow-y-auto">
              {searchResults.map((user) => (
                <button
                  key={user.userId}
                  onClick={() => handleRecipientSelect(user)}
                  className="w-full flex items-center gap-3 p-3 hover:bg-muted text-left transition-colors"
                >
                  <Avatar className="w-8 h-8">
                    <AvatarImage src={user.avatarUrl} />
                    <AvatarFallback>
                      {user.displayName?.[0] || user.username[0]?.toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm">@{user.username}</div>
                    {user.displayName && (
                      <div className="text-xs text-muted-foreground truncate">
                        {user.displayName}
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </div>
          )}

          {/* Selected Recipient */}
          {selectedRecipient && (
            <div className="flex items-center gap-3 p-3 bg-green-50 dark:bg-green-950 border border-green-200 dark:border-green-800 rounded-lg">
              <Avatar className="w-8 h-8">
                <AvatarImage src={selectedRecipient.avatarUrl} />
                <AvatarFallback>
                  {selectedRecipient.displayName?.[0] || selectedRecipient.username[0]?.toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="font-medium text-sm">@{selectedRecipient.username}</div>
                {selectedRecipient.displayName && (
                  <div className="text-xs text-muted-foreground">
                    {selectedRecipient.displayName}
                  </div>
                )}
              </div>
              <CheckCircle className="w-4 h-4 text-green-600" />
            </div>
          )}

          {/* Validation Error */}
          {validationError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{validationError}</AlertDescription>
            </Alert>
          )}
        </div>

        {/* Amount Input */}
        <div className="space-y-2">
          <Label htmlFor="amount">Amount</Label>
          <div className="relative">
            <Input
              id="amount"
              type="number"
              placeholder="Enter token amount"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              min="1"
              max={Math.min(tokenBalance, transferLimits?.remainingDaily || 500)}
              className={cn(
                'pl-8',
                transferAmount > tokenBalance && 'border-destructive'
              )}
            />
            <Coins className="w-4 h-4 absolute left-2.5 top-2.5 text-muted-foreground" />
          </div>
          
          {transferAmount > 0 && (
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">
                Balance after transfer: {tokenBalance - transferAmount}
              </span>
              {transferAmount > tokenBalance && (
                <Badge variant="destructive" className="text-xs">
                  Insufficient balance
                </Badge>
              )}
            </div>
          )}
        </div>

        {/* Transfer Message */}
        <div className="space-y-2">
          <Label htmlFor="message">Message (Optional)</Label>
          <Textarea
            id="message"
            placeholder="Add a note with your transfer..."
            value={transferMessage}
            onChange={(e) => setTransferMessage(e.target.value)}
            maxLength={200}
            rows={3}
          />
          <div className="text-xs text-muted-foreground text-right">
            {transferMessage.length}/200
          </div>
        </div>

        <Separator />

        {/* Transfer Summary */}
        {selectedRecipient && transferAmount > 0 && (
          <div className="space-y-3 p-4 bg-muted rounded-lg">
            <div className="text-sm font-medium">Transfer Summary</div>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>To:</span>
                <span>@{selectedRecipient.username}</span>
              </div>
              <div className="flex justify-between">
                <span>Amount:</span>
                <span className="font-medium">{transferAmount} tokens</span>
              </div>
              <div className="flex justify-between">
                <span>Your balance after:</span>
                <span>{tokenBalance - transferAmount} tokens</span>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3">
          {onClose && (
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
          )}
          <Button
            onClick={handleTransfer}
            disabled={!isValidTransfer || isTransferring}
            className="flex-1"
          >
            {isTransferring ? (
              <>
                <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-primary-foreground border-t-transparent" />
                Sending...
              </>
            ) : (
              <>
                <Send className="w-4 h-4 mr-2" />
                Send Tokens
              </>
            )}
          </Button>
        </div>

        {/* Transfer Limits Info */}
        {transferLimits && (
          <div className="flex items-start gap-2 p-3 bg-blue-50 dark:bg-blue-950 rounded-lg">
            <Info className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-xs text-blue-700 dark:text-blue-300">
              <div>Daily limit: {transferLimits.remainingDaily} tokens remaining</div>
              <div>Max per transfer: 500 tokens</div>
              <div>Today transferred: {transferLimits.todayTransferred} tokens</div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};