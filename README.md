# Word-by-Word Story

A collaborative storytelling platform built with Vite, React, TypeScript, and Tailwind CSS that enables users to create stories one word at a time.

![Word-by-Word Story](/placeholder.svg)

## 🌟 Features

- **Collaborative Storytelling**: Create stories with friends, each contributing one word at a time
- **Multiple Contribution Modes**: Support for word-by-word, multi-word, sentence, or paragraph contributions
- **Real-time Updates**: See when other users are typing and receive notifications for your turn
- **Story Gallery**: Browse and vote for published stories from the community
- **User Authentication**: Secure login and registration system
- **Subscription Tiers**: Free and premium tiers with different features and capabilities
- **Ad-Free Experience**: Option to subscribe for an ad-free experience
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## 🚀 Quick Start

1. **Clone the repository**

   ```bash
   git clone <YOUR_REPOSITORY_URL>
   cd word-by-word-story
   ```

2. **Install dependencies**

   ```bash
   yarn install
   ```

3. **Start the development server**
   ```bash
   yarn start:development
   ```
   This will launch the application at `http://localhost:5173`

## 🛠️ Development

### Package Management

This project uses [Yarn](https://yarnpkg.com/) as the package manager.  
Please use `yarn` commands instead of `npm` for installing and managing dependencies.

### Environment Setup

The application uses a simplified environment configuration:

- `.env.local` - Local development environment variables (not committed to repository)
- `env.template` - Template showing all available environment variables

Copy `env.template` to `.env.local` and fill in your actual values.

### Google AdSense Integration

To monetize the application with Google AdSense (while still supporting an ad-free subscription), follow these steps:

1. Sign up for AdSense and get your **Publisher ID** (`ca-pub-...`) and **Ad Slot IDs**.
2. Add the following to your `.env.*` or in your host's environment settings:
   ```bash
   VITE_ADSENSE_CLIENT_ID=ca-pub-...
   VITE_ADSENSE_SLOT_HEADER=...
   VITE_ADSENSE_SLOT_CONTENT=...
   VITE_ADSENSE_SLOT_STORY_END=...
   VITE_ADSENSE_SLOT_FOOTER=...
   ```
3. Restart your development server or build your app:
   ```bash
   yarn start:development
   yarn build && yarn preview
   ```
4. Verify that `<ins class="adsbygoogle">` tags appear in the DOM and that the ads script loads from Google.
5. Ad-free subscription is enabled by clicking "Remove Ads" in an ad slot, which navigates to `/subscription`; upon successful payment, the user's tier flips to ad-free and ads are hidden automatically.

Use the `<GoogleAd>` component in your layouts or pages to insert ad slots at header, footer, content, or story-end positions.

### Stripe Subscription Integration

The application includes a complete Stripe integration for ad-free subscriptions:

1. **Setup Stripe Environment Variables**:
   Add the following to your `.env.*` files or host environment settings:

   ```bash
   STRIPE_SECRET_KEY=sk_test_...
   STRIPE_PRICE_ID=price_...
   SUPABASE_URL=https://your-project.supabase.co
   SUPABASE_SERVICE_ROLE_KEY=eyJh...
   ```

2. **Customer Portal Configuration**:

   - Log into your Stripe dashboard
   - Navigate to Settings → Customer Portal
   - Enable "Allow customers to update their payment methods" and "Allow customers to cancel subscriptions"
   - Add your domain to the list of allowed return URLs

3. **Testing Subscriptions**:
   Use Stripe test cards to verify the payment flow:

   - Successful payment: `4242 4242 4242 4242`
   - Failed payment: `4000 0000 0000 0002`
   - Card date: Any future date
   - CVC: Any 3 digits

4. **User Management**:

   - Subscription status is automatically verified via the `verify-ad-free.js` edge function
   - Users can manage their subscription through the customer portal accessible via the account page

5. **Webhooks**:
   For production, set up Stripe webhooks to handle subscription lifecycle events such as payment failures or subscription cancellations.

### Available Scripts

- `yarn start:development` - Start Vite dev server with hot module replacement
- `yarn start:preview-local` - Build in development mode and preview
- `yarn start:production-local` - Build in production mode and preview
- `yarn build` - Build the application for production
- `yarn build:dev` - Build the application for development
- `yarn preview` - Preview the built application
- `yarn lint` - Run ESLint to check code quality
- `yarn test` - Run tests with Vitest
- `yarn test:watch` - Run tests in watch mode

## 💰 Monetization Strategy

The Word-by-Word Story platform includes multiple revenue streams:

### 1. Subscription Tiers

- **Free Tier**: Basic storytelling with ads
- **Premium Tier ($9.99/month)**: Ad-free experience + premium features
- **API Access**: Tiered API pricing for developers

### 2. Google AdSense Integration

- Strategic ad placements that don't disrupt user experience
- Header, content, story-end, footer, and sidebar ad slots
- Automatic ad-free experience for premium subscribers
- Optimized for mobile and desktop viewing

### 3. API Monetization

- **Free API**: 100 requests/hour, 1,000/day
- **Premium API ($49.99/month)**: 10,000 requests/hour, full CRUD access
- **Enterprise API (Custom pricing)**: Unlimited access, dedicated support

### Subscription Management

Users can:
- Upgrade/downgrade subscriptions seamlessly
- Manage billing through Stripe Customer Portal
- Cancel anytime with immediate access until period end
- View usage analytics and billing history

## 📦 Deployment

This application is configured for deployment on Vercel and other platforms. 

### Quick Production Deployment

See [PRODUCTION_DEPLOYMENT.md](./PRODUCTION_DEPLOYMENT.md) for comprehensive production deployment instructions including:
- Environment variable configuration
- Supabase setup and migrations
- Stripe subscription configuration
- Google AdSense integration
- Domain and SSL setup
- Performance optimization
- Security configuration
- Monitoring and maintenance

### Development Deployment

See [DEPLOY_AND_RUN.md](./DEPLOY_AND_RUN.md) for detailed instructions on running and deploying in development, preview, and production environments.

### Vercel Deployment

```bash
# Deploy to development environment
yarn deploy:development

# Deploy to preview environment
yarn deploy:preview

# Deploy to production environment
yarn deploy:production
```

## 📄 Project Structure

```
word-by-word-story/
├── public/              # Static assets
├── src/
│   ├── api/             # API endpoints
│   ├── components/      # React components
│   ├── contexts/        # React contexts for state management
│   ├── edge-functions/  # Vercel Edge Functions
│   ├── hooks/           # Custom React hooks
│   ├── integrations/    # Third-party integrations
│   ├── lib/             # Utility functions and libraries
│   ├── pages/           # Page components
│   ├── routes/          # Routing configuration
│   ├── services/        # Service layer for API calls
│   └── types/           # TypeScript type definitions
├── prisma/              # Prisma ORM configuration
└── supabase/            # Supabase configuration
```

## 🔧 Technologies

- **Frontend**: React, TypeScript, Tailwind CSS, Shadcn UI
- **State Management**: React Context API, React Query
- **Backend Integration**: Supabase, Firebase
- **Build Tools**: Vite, ESLint, PostCSS
- **Deployment**: Vercel

## 📚 Additional Documentation

- [PRODUCTION_DEPLOYMENT.md](./PRODUCTION_DEPLOYMENT.md) - Comprehensive production deployment guide
- [HANDOFF_DOCUMENTATION.md](./HANDOFF_DOCUMENTATION.md) - Complete project handoff documentation
- [env.template](./env.template) - Environment variables template
- [DEPLOY_AND_RUN.md](./DEPLOY_AND_RUN.md) - Development deployment instructions

## 📄 License

MIT
