[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [breadcrumb](../README.md) / BreadcrumbPage

# Variable: BreadcrumbPage

> `const` **BreadcrumbPage**: `ForwardRefExoticComponent`\<`Omit`\<`DetailedHTMLProps`\<`HTMLAttributes`\<`HTMLSpanElement`\>, `HTMLSpanElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLSpanElement`\>\>

Defined in: [src/components/ui/breadcrumb.tsx:60](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/breadcrumb.tsx#L60)
