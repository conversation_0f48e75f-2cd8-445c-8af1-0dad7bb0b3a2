import React, { useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Coins,
  Crown,
  Gift,
  Plus,
  Minus,
  TrendingUp,
  TrendingDown,
  Zap,
  RotateCcw,
  Star,
  Settings,
  CreditCard,
  Users,
  History,
  ShoppingCart,
} from 'lucide-react';
import { useUserTokens } from '@/hooks/useUserTokens';
import { useAuth } from '@/contexts/auth/hooks';
import { useToast } from '@/hooks/use-toast';
import { TokenTransferDialog } from '@/components/tokens/TokenTransferDialog';

export interface TokenTransaction {
  id: string;
  type: 'earn' | 'spend' | 'transfer_in' | 'transfer_out' | 'admin_grant';
  amount: number;
  description: string;
  created_at: string;
  related_user?: {
    id: string;
    username: string;
    avatar_url?: string;
  };
}

export interface SubscriptionInfo {
  isActive: boolean;
  plan: 'free' | 'premium' | 'pro';
  nextBillingDate?: string;
  tokensIncluded: number;
  extraBenefits: string[];
}

export interface TokenStatusBarProps {
  onTokenPurchase?: () => void;
  onTokenTransfer?: () => void;
  onSubscriptionManage?: () => void;
  onTokenHistory?: () => void;
  showTransactionHistory?: boolean;
  className?: string;
}

const tokenActionCosts = {
  gotcha: { cost: 2, icon: Zap, color: 'text-yellow-600 dark:text-yellow-400' },
  reverse: { cost: 5, icon: RotateCcw, color: 'text-purple-600 dark:text-purple-400' },
  golden: { cost: 3, icon: Star, color: 'text-amber-600 dark:text-amber-400' },
};

const subscriptionPlans = {
  free: {
    label: 'Free',
    color: 'bg-gray-500',
    icon: Users,
    tokensIncluded: 10,
  },
  premium: {
    label: 'Premium',
    color: 'bg-blue-500',
    icon: Crown,
    tokensIncluded: 100,
  },
  pro: {
    label: 'Pro',
    color: 'bg-purple-500',
    icon: Star,
    tokensIncluded: 250,
  },
};

export const TokenStatusBar: React.FC<TokenStatusBarProps> = ({
  onTokenPurchase,
  onTokenTransfer,
  onSubscriptionManage,
  onTokenHistory,
  showTransactionHistory = true,
  className,
}) => {
  const { user } = useAuth();
  const { 
    tokenBalance, 
    isLoading, 
    refreshBalance, 
    canAfford, 
    processSpecialAction,
    addTokens
  } = useUserTokens();
  const { toast } = useToast();

  const [recentTransactions, setRecentTransactions] = useState<TokenTransaction[]>([]);
  const [transferDialogOpen, setTransferDialogOpen] = useState(false);
  const [subscriptionInfo] = useState<SubscriptionInfo>({
    isActive: true,
    plan: 'premium',
    nextBillingDate: '2024-01-15',
    tokensIncluded: 100,
    extraBenefits: ['No ads', 'Priority support', 'Export features'],
  });

  // Get subscription plan info
  const currentPlan = subscriptionPlans[subscriptionInfo.plan];

  // Format transaction display
  const getTransactionIcon = (transaction: TokenTransaction) => {
    switch (transaction.type) {
      case 'earn':
        return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'spend':
        return <TrendingDown className="w-4 h-4 text-red-500" />;
      case 'transfer_in':
        return <Gift className="w-4 h-4 text-blue-500" />;
      case 'transfer_out':
        return <Minus className="w-4 h-4 text-orange-500" />;
      case 'admin_grant':
        return <Star className="w-4 h-4 text-purple-500" />;
      default:
        return <Coins className="w-4 h-4 text-muted-foreground" />;
    }
  };

  const formatTransactionAmount = (transaction: TokenTransaction) => {
    const prefix = ['earn', 'transfer_in', 'admin_grant'].includes(transaction.type) ? '+' : '-';
    return `${prefix}${transaction.amount}`;
  };

  // Handle token purchase
  const handleTokenPurchase = useCallback(() => {
    if (onTokenPurchase) {
      onTokenPurchase();
    } else {
      toast({
        title: 'Token Purchase',
        description: 'Token purchase system coming soon!',
      });
    }
  }, [onTokenPurchase, toast]);

  // Handle token transfer
  const handleTokenTransfer = useCallback(() => {
    if (onTokenTransfer) {
      onTokenTransfer();
    } else {
      setTransferDialogOpen(true);
    }
  }, [onTokenTransfer]);

  // Handle subscription management
  const handleSubscriptionManage = useCallback(() => {
    if (onSubscriptionManage) {
      onSubscriptionManage();
    } else {
      toast({
        title: 'Subscription Management',
        description: 'Opening subscription management...',
      });
    }
  }, [onSubscriptionManage, toast]);

  // Quick token spending simulation
  const handleQuickSpend = useCallback(async (action: keyof typeof tokenActionCosts) => {
    const cost = tokenActionCosts[action].cost;
    
    if (!canAfford(action as keyof typeof tokenActionCosts)) {
      toast({
        title: 'Insufficient tokens',
        description: `You need ${cost} tokens for ${action} action. Current balance: ${tokenBalance}`,
        variant: 'destructive',
      });
      return;
    }

    try {
      await processSpecialAction(action, 'messaging-demo');
      toast({
        title: 'Tokens spent!',
        description: `Spent ${cost} tokens for ${action} action`,
      });
    } catch (error) {
      toast({
        title: 'Token spending failed',
        description: 'Please try again later.',
        variant: 'destructive',
      });
    }
  }, [tokenBalance, canAfford, processSpecialAction, toast]);

  return (
    <TooltipProvider>
      <div className={cn(
        'flex items-center gap-3 p-3 bg-background/95 backdrop-blur-sm border-b',
        className
      )}>
        {/* User Profile with Subscription */}
        <div className="flex items-center gap-2">
          <div className="relative">
            <Avatar className="w-8 h-8">
              <AvatarImage src={user?.profilePicture} />
              <AvatarFallback className="text-sm">
                {user?.email?.slice(0, 2).toUpperCase() || 'U'}
              </AvatarFallback>
            </Avatar>
            {subscriptionInfo.isActive && (
              <div className={cn(
                'absolute -top-1 -right-1 w-4 h-4 rounded-full flex items-center justify-center',
                currentPlan.color
              )}>
                <currentPlan.icon className="w-2.5 h-2.5 text-white" />
              </div>
            )}
          </div>
          
          <div className="text-sm">
            <div className="font-medium">{user?.username || user?.email || 'User'}</div>
            <div className="text-xs text-muted-foreground">
              {currentPlan.label} Plan
            </div>
          </div>
        </div>

        {/* Token Balance Display */}
        <div className="flex items-center gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center gap-1 px-3 py-1.5 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-950 dark:to-yellow-950 rounded-lg border border-amber-200 dark:border-amber-800">
                <Coins className="w-4 h-4 text-amber-600 dark:text-amber-400" />
                <span className="font-bold text-amber-700 dark:text-amber-300">
                  {isLoading ? '...' : tokenBalance}
                </span>
                <span className="text-xs text-amber-600 dark:text-amber-400">tokens</span>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-center">
                <div className="font-medium">Token Balance</div>
                <div className="text-xs text-muted-foreground">
                  {subscriptionInfo.isActive && `+${currentPlan.tokensIncluded} monthly from ${currentPlan.label}`}
                </div>
              </div>
            </TooltipContent>
          </Tooltip>

          {/* Quick Action Costs */}
          <div className="flex items-center gap-1">
            {Object.entries(tokenActionCosts).map(([action, config]) => (
              <Tooltip key={action}>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleQuickSpend(action as keyof typeof tokenActionCosts)}
                    disabled={!canAfford(action as keyof typeof tokenActionCosts) || isLoading}
                    className="h-7 px-2 gap-1"
                  >
                    <config.icon className={cn('w-3 h-3', config.color)} />
                    <span className="text-xs">{config.cost}</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="text-center">
                    <div className="font-medium capitalize">{action} Action</div>
                    <div className="text-xs text-muted-foreground">
                      Costs {config.cost} tokens
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
            ))}
          </div>
        </div>

        {/* Token Actions Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <Settings className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-64">
            <DropdownMenuLabel className="flex items-center gap-2">
              <Coins className="w-4 h-4" />
              Token Management
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            
            <DropdownMenuItem onClick={handleTokenPurchase}>
              <ShoppingCart className="w-4 h-4 mr-2" />
              <div className="flex-1">
                <div className="font-medium">Buy Tokens</div>
                <div className="text-xs text-muted-foreground">Purchase additional tokens</div>
              </div>
            </DropdownMenuItem>

            <DropdownMenuItem onClick={handleTokenTransfer}>
              <Gift className="w-4 h-4 mr-2" />
              <div className="flex-1">
                <div className="font-medium">Send Tokens</div>
                <div className="text-xs text-muted-foreground">Transfer to another user</div>
              </div>
            </DropdownMenuItem>

            {showTransactionHistory && (
              <DropdownMenuItem onClick={onTokenHistory}>
                <History className="w-4 h-4 mr-2" />
                <div className="flex-1">
                  <div className="font-medium">Transaction History</div>
                  <div className="text-xs text-muted-foreground">View all token activity</div>
                </div>
              </DropdownMenuItem>
            )}

            <DropdownMenuSeparator />
            <DropdownMenuLabel>Subscription</DropdownMenuLabel>
            
            <DropdownMenuItem onClick={handleSubscriptionManage}>
              <CreditCard className="w-4 h-4 mr-2" />
              <div className="flex-1">
                <div className="font-medium">Manage Plan</div>
                <div className="text-xs text-muted-foreground">
                  Current: {currentPlan.label}
                </div>
              </div>
              <Badge variant="secondary" className="text-xs">
                {subscriptionInfo.isActive ? 'Active' : 'Inactive'}
              </Badge>
            </DropdownMenuItem>

            {subscriptionInfo.isActive && (
              <div className="px-2 py-2 text-xs text-muted-foreground">
                <div>Benefits:</div>
                <ul className="mt-1 space-y-1">
                  {subscriptionInfo.extraBenefits.map((benefit, index) => (
                    <li key={index} className="flex items-center gap-1">
                      <div className="w-1 h-1 bg-green-500 rounded-full" />
                      {benefit}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Recent Transactions (if enabled) */}
        {showTransactionHistory && recentTransactions.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8 relative">
                <History className="h-4 w-4" />
                <Badge 
                  variant="secondary" 
                  className="absolute -top-1 -right-1 h-4 w-4 p-0 text-xs flex items-center justify-center"
                >
                  {Math.min(recentTransactions.length, 9)}
                </Badge>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-72">
              <DropdownMenuLabel>Recent Transactions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              
              <div className="max-h-64 overflow-y-auto">
                {recentTransactions.slice(0, 5).map((transaction) => (
                  <div key={transaction.id} className="px-2 py-2 text-sm border-b last:border-b-0">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center gap-2">
                        {getTransactionIcon(transaction)}
                        <span className="font-medium">
                          {formatTransactionAmount(transaction)}
                        </span>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {new Date(transaction.created_at).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {transaction.description}
                    </div>
                    {transaction.related_user && (
                      <div className="flex items-center gap-1 mt-1">
                        <Avatar className="w-3 h-3">
                          <AvatarImage src={transaction.related_user.avatar_url} />
                          <AvatarFallback className="text-xs">
                            {transaction.related_user.username.slice(0, 1).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-xs text-muted-foreground">
                          {transaction.related_user.username}
                        </span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
              
              {onTokenHistory && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={onTokenHistory} className="text-center">
                    <span className="w-full text-center">View All Transactions</span>
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {/* Refresh Button */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              onClick={refreshBalance}
              disabled={isLoading}
              className="h-8 w-8"
            >
              <TrendingUp className={cn(
                'h-4 w-4',
                isLoading && 'animate-spin'
              )} />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Refresh token balance</TooltipContent>
        </Tooltip>
      </div>

      {/* Token Transfer Dialog */}
      <TokenTransferDialog
        open={transferDialogOpen}
        onOpenChange={setTransferDialogOpen}
        defaultTab="transfer"
      />
    </TooltipProvider>
  );
};

export default TokenStatusBar; 