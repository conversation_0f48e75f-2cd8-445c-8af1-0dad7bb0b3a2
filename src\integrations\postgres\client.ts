// This file replaces the Supabase client with a Postgres client using Prisma
import { db } from "@/lib/db-adapter";
import type { Database } from "../supabase/types";

// This client mimics the Supabase client interface but uses Prisma underneath
export const postgres = {
  from: db.from,
  // Add auth methods if needed
  auth: {
    // These would need to be implemented with a different auth provider
    // or you can continue using Firebase auth as you already have it
  },
};

// For backward compatibility with existing code
export const supabase = postgres;

// Export types for compatibility
export type { Database };
