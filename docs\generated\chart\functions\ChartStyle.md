[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [chart](../README.md) / ChartStyle

# Function: ChartStyle()

> **ChartStyle**(`__namedParameters`): `Element`

Defined in: [src/components/ui/chart.tsx:68](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/chart.tsx#L68)

## Parameters

### \_\_namedParameters

#### config

[`ChartConfig`](../type-aliases/ChartConfig.md)

#### id

`string`

## Returns

`Element`
