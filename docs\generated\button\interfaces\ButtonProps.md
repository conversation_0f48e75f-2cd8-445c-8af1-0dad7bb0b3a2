[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [button](../README.md) / ButtonProps

# Interface: ButtonProps

Defined in: [src/components/ui/button.tsx:36](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/button.tsx#L36)

## Extends

- `ButtonHTMLAttributes`\<`HTMLButtonElement`\>.`VariantProps`\<*typeof* [`buttonVariants`](../variables/buttonVariants.md)\>

## Properties

### asChild?

> `optional` **asChild**: `boolean`

Defined in: [src/components/ui/button.tsx:39](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/button.tsx#L39)

***

### size?

> `optional` **size**: `"sm"` \| `"lg"` \| `"default"` \| `"icon"`

Defined in: [src/components/ui/button.tsx:22](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/button.tsx#L22)

#### Inherited from

`VariantProps.size`

***

### variant?

> `optional` **variant**: `"default"` \| `"secondary"` \| `"destructive"` \| `"outline"` \| `"ghost"` \| `"link"`

Defined in: [src/components/ui/button.tsx:11](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/button.tsx#L11)

#### Inherited from

`VariantProps.variant`
