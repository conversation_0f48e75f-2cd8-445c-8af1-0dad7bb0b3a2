import { cacheService } from './cacheService';

// External service cost categories
export enum CostCategory {
  DATABASE = 'database',
  CACHE = 'cache', 
  HOSTING = 'hosting',
  AUTH = 'auth',
  STORAGE = 'storage',
  CDN = 'cdn',
  MONITORING = 'monitoring',
  EMAIL = 'email',
  PAYMENTS = 'payments',
  AI_SERVICES = 'ai_services'
}

// Cost tracking interfaces
export interface ServiceCost {
  service: string;
  category: CostCategory;
  provider: string;
  baseCost: number; // Monthly base cost in USD
  variableCost: number; // Cost per unit (request, GB, etc.)
  unit: string; // 'request', 'GB', 'user', etc.
  currentUsage: number;
  projectedUsage: number;
  actualCost: number; // This month's actual cost
  projectedCost: number; // Projected monthly cost
  lastUpdated: Date;
}

export interface CostOptimization {
  service: string;
  currentCost: number;
  optimizedCost: number;
  savings: number;
  recommendation: string;
  priority: 'high' | 'medium' | 'low';
  implementation: string;
  effort: 'low' | 'medium' | 'high';
}

export interface ProfitMarginAnalysis {
  revenue: {
    premiumSubscriptions: number;
    enterpriseSubscriptions: number;
    totalMonthlyRevenue: number;
  };
  costs: {
    totalExternalServices: number;
    costByCategory: Record<CostCategory, number>;
    costPerRequest: number;
    costPerUser: number;
  };
  margins: {
    grossMargin: number;
    netMargin: number;
    marginPerUser: number;
    breakEvenUsers: number;
  };
  projections: {
    nextMonthCosts: number;
    nextQuarterCosts: number;
    scalingThreshold: number; // Users where costs become unsustainable
  };
}

export class CostTrackingService {
  
  // Current external service costs for Word-by-Word Story
  private static readonly SERVICE_COSTS: ServiceCost[] = [
    {
      service: 'Supabase',
      category: CostCategory.DATABASE,
      provider: 'supabase',
      baseCost: 25.00, // Pro plan
      variableCost: 0.0003, // $0.0003 per database request
      unit: 'request',
      currentUsage: 1250000, // 1.25M requests/month
      projectedUsage: 1560000, // 25% growth
      actualCost: 400.00, // Current month
      projectedCost: 493.00, // Next month projection
      lastUpdated: new Date()
    },
    {
      service: 'Redis Cloud',
      category: CostCategory.CACHE,
      provider: 'redis',
      baseCost: 15.00, // 500MB plan
      variableCost: 0.00001, // $0.00001 per cache operation
      unit: 'operation',
      currentUsage: 8500000, // 8.5M cache ops/month
      projectedUsage: 10625000, // 25% growth
      actualCost: 100.20,
      projectedCost: 121.25,
      lastUpdated: new Date()
    },
    {
      service: 'Vercel',
      category: CostCategory.HOSTING,
      provider: 'vercel',
      baseCost: 20.00, // Pro plan
      variableCost: 0.0001, // $0.0001 per function invocation
      unit: 'invocation',
      currentUsage: 2100000, // 2.1M invocations/month
      projectedUsage: 2625000, // 25% growth
      actualCost: 230.00,
      projectedCost: 282.50,
      lastUpdated: new Date()
    },
    {
      service: 'Stripe',
      category: CostCategory.PAYMENTS,
      provider: 'stripe',
      baseCost: 0.00, // No base cost
      variableCost: 1.495, // 2.9% + 30¢ per transaction, averaged to $1.495 per $49.99 subscription
      unit: 'transaction',
      currentUsage: 289, // 289 premium subscribers
      projectedUsage: 361, // 25% growth
      actualCost: 432.15,
      projectedCost: 540.20,
      lastUpdated: new Date()
    },
    {
      service: 'SendGrid',
      category: CostCategory.EMAIL,
      provider: 'sendgrid',
      baseCost: 14.95, // Essentials plan
      variableCost: 0.0006, // $0.0006 per email
      unit: 'email',
      currentUsage: 45000, // 45K emails/month
      projectedUsage: 56250, // 25% growth
      actualCost: 41.95,
      projectedCost: 48.70,
      lastUpdated: new Date()
    },
    {
      service: 'Cloudflare',
      category: CostCategory.CDN,
      provider: 'cloudflare',
      baseCost: 20.00, // Pro plan
      variableCost: 0.0001, // $0.0001 per request
      unit: 'request',
      currentUsage: 5200000, // 5.2M requests/month
      projectedUsage: 6500000, // 25% growth
      actualCost: 540.00,
      projectedCost: 670.00,
      lastUpdated: new Date()
    }
  ];

  // Get all tracked service costs
  static async getServiceCosts(): Promise<ServiceCost[]> {
    try {
      // Try to get from cache first
      const cached = await cacheService.get<ServiceCost[]>('service_costs');
      if (cached) {
        return cached;
      }

      // In production, this would fetch from monitoring APIs
      // For now, return our defined costs with some real-time simulation
      const costs = this.SERVICE_COSTS.map(cost => ({
        ...cost,
        // Simulate some variance in actual usage
        currentUsage: Math.floor(cost.currentUsage * (0.95 + Math.random() * 0.1)),
        actualCost: cost.actualCost * (0.95 + Math.random() * 0.1),
        lastUpdated: new Date()
      }));

      // Cache for 1 hour
      await cacheService.set('service_costs', costs, 3600);

      return costs;
    } catch (error) {
      console.error('❌ Error fetching service costs:', error);
      return this.SERVICE_COSTS;
    }
  }

  // Calculate total external service costs
  static async getTotalExternalCosts(): Promise<{
    monthly: number;
    projected: number;
    byCategory: Record<CostCategory, number>;
  }> {
    try {
      const serviceCosts = await this.getServiceCosts();
      
      let totalMonthlyCost = 0;
      let totalProjectedCost = 0;
      const costsByCategory: Record<CostCategory, number> = {} as Record<CostCategory, number>;

      // Initialize categories
      Object.values(CostCategory).forEach(category => {
        costsByCategory[category] = 0;
      });

      serviceCosts.forEach(service => {
        totalMonthlyCost += service.actualCost;
        totalProjectedCost += service.projectedCost;
        costsByCategory[service.category] += service.actualCost;
      });

      return {
        monthly: totalMonthlyCost,
        projected: totalProjectedCost,
        byCategory: costsByCategory
      };
    } catch (error) {
      console.error('❌ Error calculating total external costs:', error);
      return {
        monthly: 0,
        projected: 0,
        byCategory: {} as Record<CostCategory, number>
      };
    }
  }

  // Generate cost optimization recommendations
  static async generateOptimizations(): Promise<CostOptimization[]> {
    try {
      const serviceCosts = await this.getServiceCosts();
      const optimizations: CostOptimization[] = [];

      serviceCosts.forEach(service => {
        // Analyze each service for optimization opportunities
        if (service.service === 'Supabase') {
          // Database optimization opportunities
          optimizations.push({
            service: service.service,
            currentCost: service.actualCost,
            optimizedCost: service.actualCost * 0.65, // 35% reduction
            savings: service.actualCost * 0.35,
            recommendation: 'Implement aggressive caching strategy to reduce database calls by 60-80%',
            priority: 'high',
            implementation: 'Extend cache TTL to 30 minutes for stories, implement write-through caching for contributions',
            effort: 'medium'
          });
        }

        if (service.service === 'Vercel') {
          // Function optimization
          optimizations.push({
            service: service.service,
            currentCost: service.actualCost,
            optimizedCost: service.actualCost * 0.75, // 25% reduction
            savings: service.actualCost * 0.25,
            recommendation: 'Optimize Edge Functions and reduce cold starts with function warming',
            priority: 'medium',
            implementation: 'Bundle functions, implement function warming, use edge caching',
            effort: 'medium'
          });
        }

        if (service.service === 'Cloudflare') {
          // CDN optimization
          optimizations.push({
            service: service.service,
            currentCost: service.actualCost,
            optimizedCost: service.actualCost * 0.80, // 20% reduction
            savings: service.actualCost * 0.20,
            recommendation: 'Optimize CDN cache rules and implement image optimization',
            priority: 'low',
            implementation: 'Longer cache times for static assets, WebP image conversion, compression',
            effort: 'low'
          });
        }
      });

      // Sort by potential savings (highest first)
      optimizations.sort((a, b) => b.savings - a.savings);

      return optimizations;
    } catch (error) {
      console.error('❌ Error generating optimizations:', error);
      return [];
    }
  }

  // Calculate profit margin analysis
  static async calculateProfitMargins(): Promise<ProfitMarginAnalysis> {
    try {
      const costs = await this.getTotalExternalCosts();
      
      // Revenue assumptions based on $49.99 Premium tier
      const premiumSubscribers = 289; // Current premium users
      const enterpriseSubscribers = 23; // Current enterprise users (assume $199/month)
      const premiumRevenue = premiumSubscribers * 49.99;
      const enterpriseRevenue = enterpriseSubscribers * 199;
      const totalRevenue = premiumRevenue + enterpriseRevenue;

      // Cost per request/user calculations
      const totalRequests = 1250000; // Monthly API requests
      const totalUsers = premiumSubscribers + enterpriseSubscribers;
      const costPerRequest = costs.monthly / totalRequests;
      const costPerUser = costs.monthly / totalUsers;

      // Margin calculations
      const grossMargin = ((totalRevenue - costs.monthly) / totalRevenue) * 100;
      const netMargin = grossMargin; // Simplified - in reality would include other costs
      const marginPerUser = (totalRevenue - costs.monthly) / totalUsers;
      
      // Break-even analysis (at what user count do costs exceed revenue)
      const avgRevenuePerUser = totalRevenue / totalUsers;
      const breakEvenUsers = totalRevenue / avgRevenuePerUser; // Simplified calculation

      return {
        revenue: {
          premiumSubscriptions: premiumRevenue,
          enterpriseSubscriptions: enterpriseRevenue,
          totalMonthlyRevenue: totalRevenue
        },
        costs: {
          totalExternalServices: costs.monthly,
          costByCategory: costs.byCategory,
          costPerRequest,
          costPerUser
        },
        margins: {
          grossMargin,
          netMargin,
          marginPerUser,
          breakEvenUsers
        },
        projections: {
          nextMonthCosts: costs.projected,
          nextQuarterCosts: costs.projected * 3 * 1.05, // Assume 5% quarterly growth
          scalingThreshold: 1250 // Users where costs become concerning
        }
      };
    } catch (error) {
      console.error('❌ Error calculating profit margins:', error);
      return {
        revenue: { premiumSubscriptions: 0, enterpriseSubscriptions: 0, totalMonthlyRevenue: 0 },
        costs: { totalExternalServices: 0, costByCategory: {} as Record<CostCategory, number>, costPerRequest: 0, costPerUser: 0 },
        margins: { grossMargin: 0, netMargin: 0, marginPerUser: 0, breakEvenUsers: 0 },
        projections: { nextMonthCosts: 0, nextQuarterCosts: 0, scalingThreshold: 0 }
      };
    }
  }

  // Monitor cost alerts and thresholds
  static async checkCostAlerts(): Promise<Array<{
    service: string;
    alert: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    recommendation: string;
  }>> {
    try {
      const serviceCosts = await this.getServiceCosts();
      const alerts: Array<{
        service: string;
        alert: string;
        severity: 'low' | 'medium' | 'high' | 'critical';
        recommendation: string;
      }> = [];

      serviceCosts.forEach(service => {
        // Check for cost spikes
        const costIncrease = (service.projectedCost - service.actualCost) / service.actualCost;
        
        if (costIncrease > 0.5) { // 50% increase
          alerts.push({
            service: service.service,
            alert: `Cost projected to increase by ${(costIncrease * 100).toFixed(1)}%`,
            severity: 'critical',
            recommendation: 'Implement aggressive cost optimizations immediately'
          });
        } else if (costIncrease > 0.25) { // 25% increase
          alerts.push({
            service: service.service,
            alert: `Cost projected to increase by ${(costIncrease * 100).toFixed(1)}%`,
            severity: 'high',
            recommendation: 'Review usage patterns and implement optimizations'
          });
        }

        // Check for high cost per unit
        if (service.category === CostCategory.DATABASE && service.variableCost > 0.0005) {
          alerts.push({
            service: service.service,
            alert: 'High database cost per request detected',
            severity: 'medium',
            recommendation: 'Implement caching to reduce database calls'
          });
        }
      });

      return alerts;
    } catch (error) {
      console.error('❌ Error checking cost alerts:', error);
      return [];
    }
  }

  // Get cost efficiency metrics
  static async getCostEfficiencyMetrics(): Promise<{
    revenuePerDollarSpent: number;
    costOptimizationPotential: number;
    marginTrend: 'improving' | 'stable' | 'declining';
    recommendedActions: string[];
  }> {
    try {
      const margins = await this.calculateProfitMargins();
      const optimizations = await this.generateOptimizations();
      
      const revenuePerDollarSpent = margins.revenue.totalMonthlyRevenue / margins.costs.totalExternalServices;
      const totalOptimizationSavings = optimizations.reduce((sum, opt) => sum + opt.savings, 0);
      const costOptimizationPotential = (totalOptimizationSavings / margins.costs.totalExternalServices) * 100;

      // Simulate margin trend (in production, this would use historical data)
      const marginTrend: 'improving' | 'stable' | 'declining' = 
        margins.margins.grossMargin > 75 ? 'improving' :
        margins.margins.grossMargin > 65 ? 'stable' : 'declining';

      const recommendedActions = [
        'Implement Redis caching for 60-80% database call reduction',
        'Optimize Edge Functions to reduce Vercel costs by 25%',
        'Increase Premium tier pricing to $59.99 to improve margins',
        'Implement usage-based overage billing for high-volume users',
        'Consider Enterprise tier at $199/month for larger customers'
      ];

      return {
        revenuePerDollarSpent,
        costOptimizationPotential,
        marginTrend,
        recommendedActions
      };
    } catch (error) {
      console.error('❌ Error calculating cost efficiency metrics:', error);
      return {
        revenuePerDollarSpent: 0,
        costOptimizationPotential: 0,
        marginTrend: 'stable',
        recommendedActions: []
      };
    }
  }

  // Update service cost (called by monitoring integrations)
  static async updateServiceCost(serviceName: string, actualCost: number, currentUsage: number): Promise<void> {
    try {
      const serviceCosts = await this.getServiceCosts();
      const serviceIndex = serviceCosts.findIndex(s => s.service === serviceName);
      
      if (serviceIndex !== -1) {
        serviceCosts[serviceIndex].actualCost = actualCost;
        serviceCosts[serviceIndex].currentUsage = currentUsage;
        serviceCosts[serviceIndex].lastUpdated = new Date();
        
        // Update cache
        await cacheService.set('service_costs', serviceCosts, 3600);
        
        console.log(`✅ Updated ${serviceName} cost: $${actualCost}, usage: ${currentUsage}`);
      }
    } catch (error) {
      console.error('❌ Error updating service cost:', error);
    }
  }

  // Generate monthly cost report
  static async generateMonthlyCostReport(month: number, year: number): Promise<{
    period: { month: number; year: number };
    totalCosts: number;
    totalRevenue: number;
    netProfit: number;
    marginPercentage: number;
    costsByService: Array<{ service: string; cost: number; percentage: number }>;
    optimizationOpportunities: CostOptimization[];
    recommendations: string[];
  }> {
    try {
      const costs = await this.getTotalExternalCosts();
      const margins = await this.calculateProfitMargins();
      const optimizations = await this.generateOptimizations();
      
      const netProfit = margins.revenue.totalMonthlyRevenue - costs.monthly;
      const marginPercentage = (netProfit / margins.revenue.totalMonthlyRevenue) * 100;

      const serviceCosts = await this.getServiceCosts();
      const costsByService = serviceCosts.map(service => ({
        service: service.service,
        cost: service.actualCost,
        percentage: (service.actualCost / costs.monthly) * 100
      })).sort((a, b) => b.cost - a.cost);

      const recommendations = [
        `Current margin: ${marginPercentage.toFixed(1)}% - ${marginPercentage > 70 ? 'Excellent' : marginPercentage > 60 ? 'Good' : 'Needs improvement'}`,
        `Top cost driver: ${costsByService[0].service} (${costsByService[0].percentage.toFixed(1)}% of total)`,
        `Optimization potential: Save $${optimizations.reduce((sum, opt) => sum + opt.savings, 0).toFixed(2)}/month`,
        marginPercentage < 60 ? 'Consider raising prices or implementing aggressive cost optimizations' : 'Margins are healthy, focus on growth'
      ];

      return {
        period: { month, year },
        totalCosts: costs.monthly,
        totalRevenue: margins.revenue.totalMonthlyRevenue,
        netProfit,
        marginPercentage,
        costsByService,
        optimizationOpportunities: optimizations,
        recommendations
      };
    } catch (error) {
      console.error('❌ Error generating monthly cost report:', error);
      throw error;
    }
  }
}

export default CostTrackingService; 