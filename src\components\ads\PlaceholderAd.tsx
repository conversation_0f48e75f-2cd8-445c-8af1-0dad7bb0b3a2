import React from 'react';

interface PlaceholderAdProps {
  width?: string | number;
  height?: string | number;
  className?: string;
  style?: React.CSSProperties;
  message?: string;
}

const PlaceholderAd: React.FC<PlaceholderAdProps> = ({
  width = '100%',
  height = '90px',
  className = '',
  style = {},
  message = 'Ad Placeholder',
}) => {
  return (
    <div
      style={{
        width,
        height,
        backgroundColor: '#f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        border: '1px dashed #ccc',
        color: '#999',
        fontSize: '0.875rem',
        minHeight: '50px', // Ensure it's visible even if height is small
        ...style,
      }}
      className={`placeholder-ad ${className}`.trim()}
    >
      {message}
    </div>
  );
};

export default PlaceholderAd; 