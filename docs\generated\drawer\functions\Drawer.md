[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [drawer](../README.md) / Drawer

# Function: Drawer()

> **Drawer**(`__namedParameters`): `Element`

Defined in: [src/components/ui/drawer.tsx:6](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/drawer.tsx#L6)

## Parameters

### \_\_namedParameters

`DialogProps`

## Returns

`Element`
