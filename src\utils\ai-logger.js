// ai-logger.js - Utility for logging AI operations and costs
import { safeJsonParse } from "../lib/utils";

// Initialize the logger
const aiLogger = {
  logs: [],

  // Log an AI operation
  logOperation: function (operation) {
    const timestamp = new Date();
    const log = {
      ...operation,
      timestamp,
      id: `ai-op-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    };

    // Add to logs array
    this.logs.push(log);

    // Log to console
    console.log(
      `[AI LOG] ${timestamp.toISOString()} - ${operation.type}`,
      operation,
    );

    // Save to localStorage
    this.saveToStorage();

    return log;
  },

  // Log an AI word suggestion operation
  logWordSuggestion: function (
    userId,
    storyId,
    context,
    suggestions,
    creditsUsed,
  ) {
    return this.logOperation({
      type: "word-suggestion",
      userId,
      storyId,
      context,
      suggestions,
      creditsUsed,
      tokensUsed:
        this.estimateTokens(context) +
        this.estimateTokens(suggestions.join(" ")),
    });
  },

  // Log an AI title generation operation
  logTitleGeneration: function (userId, titles, creditsUsed) {
    return this.logOperation({
      type: "title-generation",
      userId,
      titles,
      creditsUsed,
      tokensUsed: this.estimateTokens(titles.join(" ")),
    });
  },

  // Log an AI description generation operation
  logDescriptionGeneration: function (
    userId,
    title,
    descriptions,
    creditsUsed,
  ) {
    return this.logOperation({
      type: "description-generation",
      userId,
      title,
      descriptions,
      creditsUsed,
      tokensUsed:
        this.estimateTokens(title) +
        this.estimateTokens(descriptions.join(" ")),
    });
  },

  // Utility to estimate token count roughly (1 token ≈ 4 chars)
  estimateTokens: function (text) {
    if (!text) return 0;
    return Math.ceil(text.length / 4);
  },

  // Save logs to localStorage
  saveToStorage: function () {
    try {
      // Only keep the latest 100 logs to avoid storage issues
      const recentLogs = this.logs.slice(-100);
      localStorage.setItem("ai-operation-logs", JSON.stringify(recentLogs));
    } catch (error) {
      console.error("Error saving AI logs to storage:", error);
    }
  },

  // Load logs from localStorage
  loadFromStorage: function () {
    try {
      const storedLogs = localStorage.getItem("ai-operation-logs");
      if (storedLogs) {
        this.logs = safeJsonParse(storedLogs, []);
      }
    } catch (error) {
      console.error("Error loading AI logs from storage:", error);
    }
  },

  // Get all logs
  getLogs: function () {
    return this.logs;
  },

  // Get logs for a specific user
  getUserLogs: function (userId) {
    return this.logs.filter((log) => log.userId === userId);
  },

  // Get logs for a specific story
  getStoryLogs: function (storyId) {
    return this.logs.filter((log) => log.storyId === storyId);
  },

  // Get usage summary
  getUsageSummary: function (userId = null) {
    let filteredLogs = userId ? this.getUserLogs(userId) : this.logs;

    return {
      totalOperations: filteredLogs.length,
      totalCreditsUsed: filteredLogs.reduce(
        (sum, log) => sum + (log.creditsUsed || 0),
        0,
      ),
      totalTokensUsed: filteredLogs.reduce(
        (sum, log) => sum + (log.tokensUsed || 0),
        0,
      ),
      operationsByType: filteredLogs.reduce((acc, log) => {
        acc[log.type] = (acc[log.type] || 0) + 1;
        return acc;
      }, {}),
    };
  },

  // Clear logs
  clearLogs: function () {
    this.logs = [];
    localStorage.removeItem("ai-operation-logs");
  },
};

// Initialize by loading any existing logs
if (typeof window !== "undefined") {
  setTimeout(() => {
    aiLogger.loadFromStorage();
  }, 500);
}

export default aiLogger;
