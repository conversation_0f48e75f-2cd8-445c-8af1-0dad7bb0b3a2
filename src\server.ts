import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import apiRoutes from './routes/api';
import path from 'path';
import { fileURLToPath } from 'url';

// Import cache middleware
import APICacheMiddleware from './middleware/cacheMiddleware.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Add cache middleware for GET requests
app.use('/api', APICacheMiddleware.cache());

// Add cache invalidation middleware for mutations
app.use('/api', APICacheMiddleware.invalidateOnMutation());

// Cache health check endpoint
app.get('/api/cache/health', APICacheMiddleware.cacheHealthEndpoint());

// Serve static files from the React app
app.use(express.static(path.join(__dirname, '../dist')));

// Catch all handler: send back React's index.html file
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../dist/index.html'));
});

// API Routes
app.use('/api/v1', apiRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Server error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Start the server
app.listen(PORT, () => {
  console.log(`🚀 Server is running on port ${PORT}`);
  console.log(`🔥 Cache middleware enabled for API endpoints`);
  console.log(`📊 Cache health available at http://localhost:${PORT}/api/cache/health`);
});

export default app;
