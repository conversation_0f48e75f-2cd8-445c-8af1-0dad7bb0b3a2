[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [form](../README.md) / useFormField

# Function: useFormField()

> **useFormField**(): `object`

Defined in: [src/components/ui/form.tsx:42](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/form.tsx#L42)

## Returns

`object`

### formDescriptionId

> **formDescriptionId**: `string`

### formItemId

> **formItemId**: `string`

### formMessageId

> **formMessageId**: `string`

### id

> **id**: `string`

### name

> **name**: `string` = `fieldContext.name`
