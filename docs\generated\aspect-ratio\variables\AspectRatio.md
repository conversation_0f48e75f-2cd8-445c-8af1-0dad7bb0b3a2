[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [aspect-ratio](../README.md) / AspectRatio

# Variable: AspectRatio

> `const` **AspectRatio**: `ForwardRefExoticComponent`\<`AspectRatioProps` & `RefAttributes`\<`HTMLDivElement`\>\> = `AspectRatioPrimitive.Root`

Defined in: [src/components/ui/aspect-ratio.tsx:3](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/aspect-ratio.tsx#L3)
