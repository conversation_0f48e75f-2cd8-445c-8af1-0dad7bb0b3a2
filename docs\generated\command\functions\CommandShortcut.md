[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [command](../README.md) / CommandShortcut

# Function: CommandShortcut()

> **CommandShortcut**(`__namedParameters`): `Element`

Defined in: [src/components/ui/command.tsx:141](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/command.tsx#L141)

## Parameters

### \_\_namedParameters

`HTMLAttributes`\<`HTMLSpanElement`\>

## Returns

`Element`
