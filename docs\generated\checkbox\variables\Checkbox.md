[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [checkbox](../README.md) / Checkbox

# Variable: Checkbox

> `const` **Checkbox**: `ForwardRefExoticComponent`\<`Omit`\<`CheckboxProps` & `RefAttributes`\<`HTMLButtonElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLButtonElement`\>\>

Defined in: [src/components/ui/checkbox.tsx:7](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/checkbox.tsx#L7)
