import React from "react";
import { <PERSON> } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import { But<PERSON> } from "@/components/ui/button";
import { User, Book, Crown } from "lucide-react";

const FeatureHighlights: React.FC = () => {
  const { isAuthenticated } = useAuth();

  return (
    <div className="container mx-auto py-16">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-10 items-center">
        <div>
          <h2 className="text-3xl font-serif font-bold mb-6">
            Creative Storytelling Made Social
          </h2>
          <div className="space-y-4 mb-6">
            <FeatureItem
              icon={<User size={16} className="text-literary-gold" />}
              title="User Profiles"
              description="Track your stories and contributions with personalized profiles."
            />
            <FeatureItem
              icon={<Book size={16} className="text-literary-gold" />}
              title="Story Management"
              description="Create, manage, and track progress on all your collaborative stories."
            />
            <FeatureItem
              icon={<Crown size={16} className="text-literary-gold" />}
              title="Premium Features"
              description="Unlock AI enhancements, unlimited stories, and more with subscription plans."
            />
          </div>
          <Link to={isAuthenticated ? "/create-story" : "/register"}>
            <Button className="bg-literary-burgundy hover:bg-opacity-90">
              Get Started
            </Button>
          </Link>
        </div>
        <StoryExample />
      </div>
    </div>
  );
};

interface FeatureItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const FeatureItem: React.FC<FeatureItemProps> = ({
  icon,
  title,
  description,
}) => (
  <div className="flex items-start">
    <div className="bg-literary-gold/20 p-2 rounded-full mr-4 mt-1">{icon}</div>
    <div>
      <h3 className="font-medium text-lg">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  </div>
);

const StoryExample: React.FC = () => (
  <div className="bg-literary-paper rounded-lg p-8 shadow-book">
    <div className="space-y-3">
      <h3 className="text-xl font-serif font-medium mb-4 text-center">
        Example Story
      </h3>
      <p className="italic text-gray-700">
        <span className="story-word">Once</span>{" "}
        <span className="story-word">upon</span>{" "}
        <span className="story-word">a</span>{" "}
        <span className="story-word">time</span>{" "}
        <span className="story-word">there</span>{" "}
        <span className="story-word">lived</span>{" "}
        <span className="story-word">a</span>{" "}
        <span className="story-word">curious</span>{" "}
        <span className="story-word">rabbit</span>{" "}
        <span className="story-word">who</span>{" "}
        <span className="story-word">discovered</span>{" "}
        <span className="story-word">a</span>{" "}
        <span className="story-word">mysterious</span>{" "}
        <span className="story-word">door</span>{" "}
        <span className="story-word">beneath</span>{" "}
        <span className="story-word">the</span>{" "}
        <span className="story-word">old</span>{" "}
        <span className="story-word">oak</span>{" "}
        <span className="story-word">tree.</span>
      </p>
      <div className="flex items-center justify-center space-x-2 mt-4 text-sm text-gray-500">
        <span>5 contributors</span>
        <span>•</span>
        <span>20 words</span>
      </div>
    </div>
  </div>
);

export default FeatureHighlights;
