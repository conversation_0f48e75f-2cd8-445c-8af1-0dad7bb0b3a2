import React from 'react';
import { PostHogProvider as PostHogReactProvider } from 'posthog-js/react';
import { posthog } from '../../lib/posthog';

interface PostHogProviderProps {
  children: React.ReactNode;
}

export const PostHogProvider: React.FC<PostHogProviderProps> = ({ children }) => {
  // Only provide PostHog in browser environment and when properly configured
  if (typeof window === 'undefined' || !import.meta.env.VITE_POSTHOG_KEY) {
    return <>{children}</>;
  }

  return (
    <PostHogReactProvider client={posthog}>
      {children}
    </PostHogReactProvider>
  );
};