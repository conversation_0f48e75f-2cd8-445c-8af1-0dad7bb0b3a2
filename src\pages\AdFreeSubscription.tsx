import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { useAuth } from "@/contexts/auth";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Check, X, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const AdFreeSubscription = () => {
  const [selectedPlan, setSelectedPlan] = useState<"monthly" | "yearly">(
    "monthly",
  );
  const [isProcessing, setIsProcessing] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const plans = {
    monthly: {
      price: 1.99,
      savings: 0,
      billingPeriod: "month",
    },
    yearly: {
      price: 19.99,
      savings: 3.89,
      billingPeriod: "year",
    },
  };

  const features = [
    "Ad-free experience across the entire platform",
    "Support ongoing development of the platform",
    "Faster page loading times",
    "Cleaner reading experience",
    "Cancel anytime",
  ];

  const handleSubscribe = async () => {
    setIsProcessing(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Show success message
      toast({
        title: "Subscription successful!",
        description:
          "Welcome to the ad-free experience. Enjoy the uninterrupted storytelling!",
      });

      // Redirect to dashboard
      setTimeout(() => {
        navigate("/dashboard");
      }, 1000);
    } catch (error) {
      console.error("Error subscribing:", error);

      toast({
        title: "Subscription failed",
        description:
          "There was a problem processing your subscription. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="max-w-3xl mx-auto py-8">
      <h1 className="text-3xl font-serif mb-2">Ad-Free Experience</h1>
      <p className="text-gray-600 mb-8">
        Enjoy an uninterrupted storytelling experience without any
        advertisements.
      </p>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Choose Your Plan</CardTitle>
              <CardDescription>
                Select the billing cycle that works best for you
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RadioGroup
                value={selectedPlan}
                onValueChange={(value) =>
                  setSelectedPlan(value as "monthly" | "yearly")
                }
                className="space-y-4"
              >
                <div
                  className={`flex items-center justify-between p-4 rounded-lg border-2 ${selectedPlan === "monthly" ? "border-literary-burgundy bg-literary-burgundy/5" : "border-gray-200"}`}
                >
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="monthly" id="monthly" />
                    <Label htmlFor="monthly" className="font-medium">
                      Monthly Plan
                    </Label>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold">
                      ${plans.monthly.price}/month
                    </div>
                    <div className="text-sm text-gray-500">Billed monthly</div>
                  </div>
                </div>

                <div
                  className={`flex items-center justify-between p-4 rounded-lg border-2 ${selectedPlan === "yearly" ? "border-literary-burgundy bg-literary-burgundy/5" : "border-gray-200"}`}
                >
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="yearly" id="yearly" />
                    <div>
                      <Label htmlFor="yearly" className="font-medium">
                        Annual Plan
                      </Label>
                      <div className="text-sm text-green-600 font-medium">
                        Save ${plans.yearly.savings}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold">
                      ${plans.yearly.price}/year
                    </div>
                    <div className="text-sm text-gray-500">Billed annually</div>
                  </div>
                </div>
              </RadioGroup>

              <div className="mt-6">
                <h3 className="font-medium mb-3">What's included:</h3>
                <ul className="space-y-2">
                  {features.map((feature, index) => (
                    <li key={index} className="flex items-center">
                      <Check className="h-5 w-5 text-green-500 mr-2" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <Button
                onClick={handleSubscribe}
                className="w-full bg-literary-burgundy hover:bg-literary-burgundy/90"
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>Subscribe Now</>
                )}
              </Button>
              <p className="text-xs text-center text-gray-500">
                By subscribing, you agree to our Terms of Service and Privacy
                Policy. You can cancel your subscription at any time.
              </p>
            </CardFooter>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Order Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span>Ad-Free Experience</span>
                <span>${plans[selectedPlan].price}</span>
              </div>

              {selectedPlan === "yearly" && (
                <div className="flex justify-between text-green-600">
                  <span>Annual Savings</span>
                  <span>-${plans.yearly.savings}</span>
                </div>
              )}

              <Separator />

              <div className="flex justify-between font-bold">
                <span>Total</span>
                <span>
                  ${plans[selectedPlan].price}/
                  {plans[selectedPlan].billingPeriod}
                </span>
              </div>

              <div className="text-sm text-gray-500">
                {selectedPlan === "monthly"
                  ? "Billed monthly. Cancel anytime."
                  : "Billed annually. Cancel anytime."}
              </div>
            </CardContent>
          </Card>

          <div className="mt-4 bg-amber-50 border border-amber-200 rounded-lg p-4">
            <h3 className="font-medium text-amber-800 mb-2">
              Money-Back Guarantee
            </h3>
            <p className="text-sm text-amber-700">
              If you're not satisfied with your ad-free experience, contact us
              within 14 days for a full refund.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdFreeSubscription;
