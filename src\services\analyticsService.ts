import { supabase } from '@/lib/supabase';
import { cacheService } from './cacheService';
import { ApiTier, ApiKeyService } from './apiKeyService';

export interface AnalyticsMetrics {
  usage: {
    totalRequests: number;
    requestsToday: number;
    requestsThisMonth: number;
    avgResponseTime: number;
    cacheHitRate: number;
    topEndpoints: Array<{
      endpoint: string;
      requests: number;
      avgResponseTime: number;
      cost: number;
      errorRate: number;
    }>;
  };
  billing: {
    tier: ApiTier;
    monthlyFee: number;
    usageCost: number;
    totalCost: number;
    estimatedSavings: number;
    nextBillDate: string;
  };
  performance: {
    uptime: number;
    errorRate: number;
    latencyP95: number;
    throughput: number;
  };
  revenue?: {
    mrr: number;
    arr: number;
    churnRate: number;
    upgradeRate: number;
  };
}

export interface EndpointMetrics {
  endpoint: string;
  requests: number;
  avgResponseTime: number;
  cost: number;
  errors: number;
  errorRate: number;
  cacheHitRate: number;
  totalBytes: number;
}

export interface RealtimeMetrics {
  activeRequests: number;
  requestsPerSecond: number;
  avgResponseTime: number;
  errorRate: number;
  cacheHitRate: number;
  activeKeys: number;
  timestamp: string;
}

export class AnalyticsService {
  
  // Get comprehensive analytics for a user
  static async getUserAnalytics(userId: string, period: '24h' | '7d' | '30d' = '30d'): Promise<AnalyticsMetrics | null> {
    try {
      const cacheKey = `analytics:user:${userId}:${period}`;
      
      // Try cache first
      const cached = await cacheService.get<AnalyticsMetrics>(cacheKey);
      if (cached) {
        return cached;
      }

      // Get user's API keys
      const { data: apiKeys, error: keysError } = await supabase
        .from('api_keys')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true);

      if (keysError) {
        console.error('❌ Error fetching API keys:', keysError);
        return null;
      }

      if (!apiKeys || apiKeys.length === 0) {
        // Return empty analytics for users with no API keys
        return this.getEmptyAnalytics();
      }

      const keyIds = apiKeys.map(key => key.id);
      const timeRange = this.getTimeRange(period);

      // Get usage data
      const usageData = await this.getUsageMetrics(keyIds, timeRange);
      const billingData = await this.getBillingMetrics(userId, apiKeys);
      const performanceData = await this.getPerformanceMetrics(keyIds, timeRange);

      const analytics: AnalyticsMetrics = {
        usage: usageData,
        billing: billingData,
        performance: performanceData
      };

      // Cache for 5 minutes
      await cacheService.set(cacheKey, analytics, 300);
      
      return analytics;

    } catch (error) {
      console.error('❌ Error getting user analytics:', error);
      return null;
    }
  }

  // Get usage metrics from api_usage table
  static async getUsageMetrics(keyIds: string[], timeRange: { start: Date; end: Date }) {
    const { data: usage, error } = await supabase
      .from('api_usage')
      .select('*')
      .in('key_id', keyIds)
      .gte('timestamp', timeRange.start.toISOString())
      .lte('timestamp', timeRange.end.toISOString());

    if (error) {
      console.error('❌ Error fetching usage metrics:', error);
      throw error;
    }

    const totalRequests = usage?.length || 0;
    const avgResponseTime = usage?.length ? 
      usage.reduce((sum, u) => sum + u.response_time, 0) / usage.length : 0;
    
    const cachedRequests = usage?.filter(u => u.cached).length || 0;
    const cacheHitRate = totalRequests > 0 ? (cachedRequests / totalRequests) * 100 : 0;

    // Calculate today's requests
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const requestsToday = usage?.filter(u => 
      new Date(u.timestamp) >= today
    ).length || 0;

    // Calculate this month's requests
    const thisMonth = new Date();
    thisMonth.setDate(1);
    thisMonth.setHours(0, 0, 0, 0);
    const requestsThisMonth = usage?.filter(u => 
      new Date(u.timestamp) >= thisMonth
    ).length || 0;

    // Get top endpoints
    const endpointStats = usage?.reduce((acc, u) => {
      if (!acc[u.endpoint]) {
        acc[u.endpoint] = {
          requests: 0,
          totalResponseTime: 0,
          totalCost: 0,
          errors: 0
        };
      }
      acc[u.endpoint].requests++;
      acc[u.endpoint].totalResponseTime += u.response_time;
      acc[u.endpoint].totalCost += u.cost;
      if (u.status_code >= 400) {
        acc[u.endpoint].errors++;
      }
      return acc;
    }, {} as Record<string, any>) || {};

    const topEndpoints = Object.entries(endpointStats)
      .map(([endpoint, stats]: [string, any]) => ({
        endpoint,
        requests: stats.requests,
        avgResponseTime: Math.round(stats.totalResponseTime / stats.requests),
        cost: Math.round(stats.totalCost * 100) / 100,
        errorRate: (stats.errors / stats.requests) * 100
      }))
      .sort((a, b) => b.requests - a.requests)
      .slice(0, 10);

    return {
      totalRequests,
      requestsToday,
      requestsThisMonth,
      avgResponseTime: Math.round(avgResponseTime),
      cacheHitRate: Math.round(cacheHitRate * 10) / 10,
      topEndpoints
    };
  }

  // Get billing metrics
  static async getBillingMetrics(userId: string, apiKeys: any[]) {
    // Get the highest tier among user's API keys
    const tiers = apiKeys.map(key => key.tier);
    const tier = tiers.includes(ApiTier.ENTERPRISE) ? ApiTier.ENTERPRISE :
                 tiers.includes(ApiTier.PREMIUM) ? ApiTier.PREMIUM : ApiTier.FREE;

    // Calculate monthly fee based on tier
    const monthlyFee = tier === ApiTier.PREMIUM ? 49.99 : 
                      tier === ApiTier.ENTERPRISE ? 199.99 : 0;

    // Get usage cost from this month
    const thisMonth = new Date();
    thisMonth.setDate(1);
    thisMonth.setHours(0, 0, 0, 0);

    const { data: monthlyUsage, error } = await supabase
      .from('api_usage')
      .select('cost')
      .in('key_id', apiKeys.map(k => k.id))
      .gte('timestamp', thisMonth.toISOString());

    const usageCost = monthlyUsage?.reduce((sum, u) => sum + u.cost, 0) || 0;
    const totalCost = monthlyFee + usageCost;

    // Estimate cache savings (90% of what would have been charged without cache)
    const { data: cachedUsage } = await supabase
      .from('api_usage')
      .select('cost')
      .in('key_id', apiKeys.map(k => k.id))
      .eq('cached', true)
      .gte('timestamp', thisMonth.toISOString());

    const estimatedSavings = (cachedUsage?.reduce((sum, u) => sum + u.cost, 0) || 0) * 9; // 90% savings

    // Calculate next bill date (assuming monthly billing)
    const nextBillDate = new Date();
    nextBillDate.setMonth(nextBillDate.getMonth() + 1);
    nextBillDate.setDate(1);

    return {
      tier,
      monthlyFee: Math.round(monthlyFee * 100) / 100,
      usageCost: Math.round(usageCost * 100) / 100,
      totalCost: Math.round(totalCost * 100) / 100,
      estimatedSavings: Math.round(estimatedSavings * 100) / 100,
      nextBillDate: nextBillDate.toISOString().split('T')[0]
    };
  }

  // Get performance metrics
  static async getPerformanceMetrics(keyIds: string[], timeRange: { start: Date; end: Date }) {
    const { data: usage, error } = await supabase
      .from('api_usage')
      .select('response_time, status_code, timestamp')
      .in('key_id', keyIds)
      .gte('timestamp', timeRange.start.toISOString())
      .lte('timestamp', timeRange.end.toISOString());

    if (error) {
      console.error('❌ Error fetching performance metrics:', error);
      throw error;
    }

    const totalRequests = usage?.length || 0;
    const errorRequests = usage?.filter(u => u.status_code >= 400).length || 0;
    const errorRate = totalRequests > 0 ? (errorRequests / totalRequests) * 100 : 0;

    // Calculate 95th percentile latency
    const responseTimes = usage?.map(u => u.response_time).sort((a, b) => a - b) || [];
    const p95Index = Math.floor(responseTimes.length * 0.95);
    const latencyP95 = responseTimes[p95Index] || 0;

    // Calculate throughput (requests per second)
    const timeSpanHours = (timeRange.end.getTime() - timeRange.start.getTime()) / (1000 * 60 * 60);
    const throughput = timeSpanHours > 0 ? totalRequests / (timeSpanHours * 3600) : 0;

    // Calculate uptime (assume 99.9% base uptime, reduce based on error rate)
    const uptime = Math.max(99.0, 99.9 - (errorRate * 0.1));

    return {
      uptime: Math.round(uptime * 100) / 100,
      errorRate: Math.round(errorRate * 100) / 100,
      latencyP95: Math.round(latencyP95),
      throughput: Math.round(throughput * 10) / 10
    };
  }

  // Get endpoint-specific metrics
  static async getEndpointMetrics(keyIds: string[], timeRange: { start: Date; end: Date }, limit: number = 10): Promise<EndpointMetrics[]> {
    const { data: usage, error } = await supabase
      .from('api_usage')
      .select('*')
      .in('key_id', keyIds)
      .gte('timestamp', timeRange.start.toISOString())
      .lte('timestamp', timeRange.end.toISOString());

    if (error) {
      console.error('❌ Error fetching endpoint metrics:', error);
      return [];
    }

    const endpointStats = usage?.reduce((acc, u) => {
      if (!acc[u.endpoint]) {
        acc[u.endpoint] = {
          requests: 0,
          totalResponseTime: 0,
          totalCost: 0,
          errors: 0,
          cachedRequests: 0,
          totalBytes: 0
        };
      }
      
      const stats = acc[u.endpoint];
      stats.requests++;
      stats.totalResponseTime += u.response_time;
      stats.totalCost += u.cost;
      stats.totalBytes += u.bytes;
      
      if (u.status_code >= 400) {
        stats.errors++;
      }
      
      if (u.cached) {
        stats.cachedRequests++;
      }
      
      return acc;
    }, {} as Record<string, any>) || {};

    return Object.entries(endpointStats)
      .map(([endpoint, stats]: [string, any]) => ({
        endpoint,
        requests: stats.requests,
        avgResponseTime: Math.round(stats.totalResponseTime / stats.requests),
        cost: Math.round(stats.totalCost * 100) / 100,
        errors: stats.errors,
        errorRate: Math.round((stats.errors / stats.requests) * 100 * 10) / 10,
        cacheHitRate: Math.round((stats.cachedRequests / stats.requests) * 100 * 10) / 10,
        totalBytes: stats.totalBytes
      }))
      .sort((a, b) => b.requests - a.requests)
      .slice(0, limit);
  }

  // Get real-time metrics
  static async getRealtimeMetrics(keyIds: string[]): Promise<RealtimeMetrics> {
    try {
      // Get metrics from the last 5 minutes
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
      
      const { data: recentUsage, error } = await supabase
        .from('api_usage')
        .select('*')
        .in('key_id', keyIds)
        .gte('timestamp', fiveMinutesAgo.toISOString());

      if (error) {
        console.error('❌ Error fetching realtime metrics:', error);
        throw error;
      }

      const totalRequests = recentUsage?.length || 0;
      const requestsPerSecond = totalRequests / 300; // 5 minutes = 300 seconds
      
      const avgResponseTime = recentUsage?.length ? 
        recentUsage.reduce((sum, u) => sum + u.response_time, 0) / recentUsage.length : 0;
      
      const errorRequests = recentUsage?.filter(u => u.status_code >= 400).length || 0;
      const errorRate = totalRequests > 0 ? (errorRequests / totalRequests) * 100 : 0;
      
      const cachedRequests = recentUsage?.filter(u => u.cached).length || 0;
      const cacheHitRate = totalRequests > 0 ? (cachedRequests / totalRequests) * 100 : 0;

      return {
        activeRequests: totalRequests,
        requestsPerSecond: Math.round(requestsPerSecond * 10) / 10,
        avgResponseTime: Math.round(avgResponseTime),
        errorRate: Math.round(errorRate * 10) / 10,
        cacheHitRate: Math.round(cacheHitRate * 10) / 10,
        activeKeys: keyIds.length,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('❌ Error getting realtime metrics:', error);
      return {
        activeRequests: 0,
        requestsPerSecond: 0,
        avgResponseTime: 0,
        errorRate: 0,
        cacheHitRate: 0,
        activeKeys: keyIds.length,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Get revenue metrics (admin/business view)
  static async getRevenueMetrics(): Promise<any> {
    try {
      // Get all active subscriptions
      const { data: apiKeys, error } = await supabase
        .from('api_keys')
        .select('tier, user_id, created_at')
        .eq('is_active', true);

      if (error) {
        console.error('❌ Error fetching revenue data:', error);
        return null;
      }

      // Calculate subscription counts
      const tierCounts = apiKeys?.reduce((acc, key) => {
        acc[key.tier] = (acc[key.tier] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) || {};

      const premiumCount = tierCounts[ApiTier.PREMIUM] || 0;
      const enterpriseCount = tierCounts[ApiTier.ENTERPRISE] || 0;
      
      // Calculate MRR (Monthly Recurring Revenue)
      const mrr = (premiumCount * 49.99) + (enterpriseCount * 199.99);
      const arr = mrr * 12;

      // Calculate growth metrics (simplified)
      const totalCustomers = premiumCount + enterpriseCount;
      
      return {
        mrr: Math.round(mrr * 100) / 100,
        arr: Math.round(arr * 100) / 100,
        customers: {
          total: totalCustomers,
          premium: premiumCount,
          enterprise: enterpriseCount,
          churnRate: 2.3, // Would need historical data to calculate
          upgradeRate: 15.7 // Would need conversion tracking
        },
        growth: {
          monthlyGrowth: 8.4, // Would need historical data
          quarterlyGrowth: 23.1,
          yearlyGrowth: 145.7
        }
      };

    } catch (error) {
      console.error('❌ Error calculating revenue metrics:', error);
      return null;
    }
  }

  // Helper methods
  static getTimeRange(period: '24h' | '7d' | '30d') {
    const end = new Date();
    const start = new Date();
    
    switch (period) {
      case '24h':
        start.setHours(start.getHours() - 24);
        break;
      case '7d':
        start.setDate(start.getDate() - 7);
        break;
      case '30d':
        start.setDate(start.getDate() - 30);
        break;
    }
    
    return { start, end };
  }

  private static getEmptyAnalytics(): AnalyticsMetrics {
    return {
      usage: {
        totalRequests: 0,
        requestsToday: 0,
        requestsThisMonth: 0,
        avgResponseTime: 0,
        cacheHitRate: 0,
        topEndpoints: []
      },
      billing: {
        tier: ApiTier.FREE,
        monthlyFee: 0,
        usageCost: 0,
        totalCost: 0,
        estimatedSavings: 0,
        nextBillDate: new Date().toISOString().split('T')[0]
      },
      performance: {
        uptime: 100,
        errorRate: 0,
        latencyP95: 0,
        throughput: 0
      }
    };
  }

  // Get user's API keys
  static async getUserApiKeys(userId: string) {
    const { data: apiKeys, error } = await supabase
      .from('api_keys')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Error fetching user API keys:', error);
      return [];
    }

    return apiKeys?.map(key => ({
      id: key.id,
      name: key.name,
      tier: key.tier,
      isActive: key.is_active,
      totalRequests: key.total_requests || 0,
      monthlyRequests: key.monthly_requests || 0,
      lastUsedAt: key.last_used_at,
      keyPreview: key.key_preview,
      createdAt: key.created_at,
      expiresAt: key.expires_at
    })) || [];
  }
}

export default AnalyticsService; 