/**
 * PostHog Test Panel Component
 * 
 * A development component for testing PostHog integration.
 * Only renders in development mode.
 */

import React, { useState } from 'react';
import { runAllPostHogTests, sendTestEvents, PostHogTestResult } from '../../utils/posthog-test';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Loader2, TestTube, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface TestSummary {
  total: number;
  passed: number;
  failed: number;
  successRate: number;
}

const PostHogTestPanel: React.FC = () => {
  const [testResults, setTestResults] = useState<PostHogTestResult[]>([]);
  const [summary, setSummary] = useState<TestSummary | null>(null);
  const [isRunning, setIsRunning] = useState(false);

  // Only render in development mode
  if (!import.meta.env.DEV) {
    return null;
  }

  const runTests = async () => {
    setIsRunning(true);
    try {
      const { results, summary: testSummary } = await runAllPostHogTests();
      setTestResults(results);
      setSummary(testSummary);
    } catch (error) {
      console.error('Error running PostHog tests:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const sendTestEventsOnly = async () => {
    setIsRunning(true);
    try {
      const results = await sendTestEvents();
      console.log('Test events sent:', results);
      alert('Test events sent! Check PostHog dashboard or browser network tab.');
    } catch (error) {
      console.error('Error sending test events:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-4 w-4 text-green-600" />
    ) : (
      <XCircle className="h-4 w-4 text-red-600" />
    );
  };

  const getStatusBadge = (success: boolean) => {
    return success ? (
      <Badge variant="default" className="bg-green-100 text-green-800">
        Pass
      </Badge>
    ) : (
      <Badge variant="destructive">
        Fail
      </Badge>
    );
  };

  return (
    <Card className="w-full max-w-4xl mx-auto mt-8">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TestTube className="h-5 w-5" />
          PostHog Integration Test Panel
        </CardTitle>
        <CardDescription>
          Development tool for testing PostHog analytics integration. This panel only appears in development mode.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Test Actions */}
        <div className="flex gap-3 flex-wrap">
          <Button
            onClick={runTests}
            disabled={isRunning}
            className="flex items-center gap-2"
          >
            {isRunning ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <TestTube className="h-4 w-4" />
            )}
            Run All Tests
          </Button>
          
          <Button
            onClick={sendTestEventsOnly}
            disabled={isRunning}
            variant="outline"
            className="flex items-center gap-2"
          >
            {isRunning ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            Send Test Events
          </Button>
        </div>

        {/* Test Summary */}
        {summary && (
          <div className="border rounded-lg p-4 bg-gray-50">
            <h3 className="font-semibold mb-2">Test Summary</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Total Tests:</span>
                <div className="font-semibold">{summary.total}</div>
              </div>
              <div>
                <span className="text-gray-600">Passed:</span>
                <div className="font-semibold text-green-600">{summary.passed}</div>
              </div>
              <div>
                <span className="text-gray-600">Failed:</span>
                <div className="font-semibold text-red-600">{summary.failed}</div>
              </div>
              <div>
                <span className="text-gray-600">Success Rate:</span>
                <div className="font-semibold">{summary.successRate.toFixed(1)}%</div>
              </div>
            </div>
          </div>
        )}

        {/* Test Results */}
        {testResults.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-semibold">Test Results</h3>
            {testResults.map((result, index) => (
              <div key={index} className="border rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(result.success)}
                    <span className="font-medium">{result.test}</span>
                  </div>
                  {getStatusBadge(result.success)}
                </div>
                
                {result.error && (
                  <div className="text-sm text-red-600 bg-red-50 p-2 rounded mt-2">
                    <strong>Error:</strong> {result.error}
                  </div>
                )}
                
                {result.data && (
                  <details className="mt-2">
                    <summary className="text-sm text-gray-600 cursor-pointer hover:text-gray-800">
                      Show Details
                    </summary>
                    <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Instructions */}
        <div className="border-t pt-4 text-sm text-gray-600">
          <h4 className="font-medium mb-2">Instructions:</h4>
          <ul className="space-y-1 list-disc list-inside">
            <li>Run "Run All Tests" to verify PostHog integration is working correctly</li>
            <li>Use "Send Test Events" to send sample events and verify they appear in PostHog dashboard</li>
            <li>Check the browser's Network tab to see if events are being sent to PostHog</li>
            <li>Verify events appear in your PostHog dashboard (may take a few minutes)</li>
            <li>Open browser console and run <code>window.posthogDiagnostics()</code> for detailed diagnostics</li>
          </ul>
        </div>

        {/* Environment Info */}
        <div className="border-t pt-4 text-xs text-gray-500">
          <div className="grid grid-cols-2 gap-2">
            <div>
              <strong>Environment:</strong> {import.meta.env.DEV ? 'Development' : 'Production'}
            </div>
            <div>
              <strong>PostHog Key:</strong> {import.meta.env.VITE_POSTHOG_KEY ? 'Configured' : 'Not Set'}
            </div>
            <div>
              <strong>PostHog Host:</strong> {import.meta.env.VITE_POSTHOG_HOST || 'Default'}
            </div>
            <div>
              <strong>Disabled in Dev:</strong> {import.meta.env.VITE_POSTHOG_DISABLE_IN_DEV || 'false'}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PostHogTestPanel;