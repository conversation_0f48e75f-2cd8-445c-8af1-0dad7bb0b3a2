import { ContributionMode } from '@/services/realTimeService';

/**
 * Service for validating contributions based on their mode
 */
export class ContributionValidator {
  /**
   * Maximum number of words allowed in multi-word mode
   */
  private static MAX_MULTI_WORD_COUNT = 5;

  /**
   * Maximum number of characters allowed in a sentence
   */
  private static MAX_SENTENCE_LENGTH = 200;

  /**
   * Maximum number of characters allowed in a paragraph
   */
  private static MAX_PARAGRAPH_LENGTH = 1000;

  /**
   * Validate a contribution based on its mode
   * @param content The content to validate
   * @param mode The contribution mode
   * @param wordsPerContribution Optional limit for multi-word mode
   * @returns Object with isValid flag and error message if invalid
   */
  static validate(
    content: string,
    mode: ContributionMode,
    wordsPerContribution?: number
  ): { isValid: boolean; error?: string } {
    // Common validation for all modes
    if (!content || content.trim().length === 0) {
      return { isValid: false, error: 'Content cannot be empty' };
    }

    // Check for prohibited content (profanity, etc. - simplified version)
    if (this.containsProhibitedContent(content)) {
      return { isValid: false, error: 'Content contains prohibited words or phrases' };
    }

    // Mode-specific validation
    switch (mode) {
      case ContributionMode.WORD:
        return this.validateWordMode(content);
      case ContributionMode.MULTI_WORD:
        return this.validateMultiWordMode(content, wordsPerContribution);
      case ContributionMode.SENTENCE:
        return this.validateSentenceMode(content);
      case ContributionMode.PARAGRAPH:
        return this.validateParagraphMode(content);
      default:
        return { isValid: false, error: 'Invalid contribution mode' };
    }
  }

  /**
   * Validate content for word mode (single word only)
   * @param content The content to validate
   * @returns Validation result
   */
  private static validateWordMode(content: string): { isValid: boolean; error?: string } {
    const trimmed = content.trim();
    
    // Check if it's a single word (no spaces)
    if (trimmed.includes(' ')) {
      return { isValid: false, error: 'Word mode only accepts a single word' };
    }

    // Additional word-specific validation can be added here
    return { isValid: true };
  }

  /**
   * Validate content for multi-word mode
   * @param content The content to validate
   * @param wordsPerContribution Optional custom limit for words
   * @returns Validation result
   */
  private static validateMultiWordMode(
    content: string,
    wordsPerContribution?: number
  ): { isValid: boolean; error?: string } {
    const trimmed = content.trim();
    const words = trimmed.split(/\s+/);
    const maxWords = wordsPerContribution || this.MAX_MULTI_WORD_COUNT;

    if (words.length > maxWords) {
      return {
        isValid: false,
        error: `Multi-word mode accepts a maximum of ${maxWords} words`
      };
    }

    // Additional multi-word validation can be added here
    return { isValid: true };
  }

  /**
   * Validate content for sentence mode
   * @param content The content to validate
   * @returns Validation result
   */
  private static validateSentenceMode(content: string): { isValid: boolean; error?: string } {
    const trimmed = content.trim();

    // Check length
    if (trimmed.length > this.MAX_SENTENCE_LENGTH) {
      return {
        isValid: false,
        error: `Sentence mode accepts a maximum of ${this.MAX_SENTENCE_LENGTH} characters`
      };
    }

    // Check for sentence ending punctuation
    const endsWithPunctuation = /[.!?]$/.test(trimmed);
    if (!endsWithPunctuation) {
      return {
        isValid: false,
        error: 'Sentence must end with proper punctuation (., !, or ?)'
      };
    }

    // Additional sentence validation can be added here
    return { isValid: true };
  }

  /**
   * Validate content for paragraph mode
   * @param content The content to validate
   * @returns Validation result
   */
  private static validateParagraphMode(content: string): { isValid: boolean; error?: string } {
    const trimmed = content.trim();

    // Check length
    if (trimmed.length > this.MAX_PARAGRAPH_LENGTH) {
      return {
        isValid: false,
        error: `Paragraph mode accepts a maximum of ${this.MAX_PARAGRAPH_LENGTH} characters`
      };
    }

    // Check for at least one complete sentence
    const hasSentence = /[.!?]/.test(trimmed);
    if (!hasSentence) {
      return {
        isValid: false,
        error: 'Paragraph must contain at least one complete sentence'
      };
    }

    // Additional paragraph validation can be added here
    return { isValid: true };
  }

  /**
   * Check if content contains prohibited words or phrases
   * @param content The content to check
   * @returns True if prohibited content is found
   */
  private static containsProhibitedContent(content: string): boolean {
    // This is a simplified implementation
    // In a real app, you would use a more comprehensive profanity filter
    const prohibitedWords = [
      'badword1',
      'badword2',
      // Add more prohibited words as needed
    ];

    const lowerContent = content.toLowerCase();
    return prohibitedWords.some(word => lowerContent.includes(word));
  }

  /**
   * Split multi-word content into individual tokens
   * @param content The content to tokenize
   * @returns Array of tokens
   */
  static tokenizeMultiWord(content: string): string[] {
    return content.trim().split(/\s+/);
  }
}
