/**
 * Tests for Viewer Interface Components
 * Tests read-only participant UI and permission enforcement
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import {
  ViewerInterface,
  ViewerMessageInput,
  ViewerContributionPrompt,
  ViewerReactionBar,
  PermissionGate,
} from '../../components/story/ViewerInterface';

// Mock auth context
vi.mock('@/contexts/auth', () => ({
  useAuth: vi.fn(() => ({
    user: {
      id: 'viewer-user-1',
      email: '<EMAIL>',
      is_admin: false,
    }
  })),
}));

// Mock permissions hook
vi.mock('../../hooks/use-permissions', () => ({
  usePermissions: vi.fn(),
}));

// Mock toast
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({ toast: vi.fn() }),
}));

// Import the mocked functions
import { useAuth } from '@/contexts/auth';
import { usePermissions } from '../../hooks/use-permissions';

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('ViewerInterface Components', () => {
  const mockStoryId = 'test-story-1';
  const mockStoryTitle = 'Test Story Title';

  const viewerPermissions = {
    canCreateStory: true,
    canContribute: false,
    canEditStoryDetails: false,
    canDeleteStory: false,
    canManageParticipants: false,
    canCreateInvitations: false,
    canEditOwnContributions: false,
    canDeleteOwnContributions: false,
    canSendMessages: false,
    canReactToMessages: true,
    canViewStory: true,
    canViewMessages: true,
    canViewParticipants: true,
    canReportContent: true,
    canModerateContent: false,
    role: 'viewer' as const,
    status: 'active' as const,
    isCreator: false,
    isMuted: false,
    isBanned: false,
  };

  const viewerSummary = {
    label: 'Viewer (Read-Only)',
    description: 'Can follow the story as it develops',
    capabilities: [
      'View story content as it updates',
      'See messages and conversations',
      'React to messages',
      'View participant list',
    ],
    restrictions: [
      'Cannot add story content',
      'Cannot send messages',
      'Cannot edit or delete content',
      'Cannot manage participants',
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Default mock for viewer permissions
    vi.mocked(usePermissions).mockReturnValue({
      permissions: viewerPermissions,
      validation: {
        canContribute: false,
        canMessage: false,
        contributionReason: 'Viewers cannot contribute',
        messagingReason: 'Viewers cannot send messages',
      },
      summary: viewerSummary,
      isLoading: false,
      error: null,
      isCreator: false,
      isContributor: false,
      isViewer: true,
      isMuted: false,
      isBanned: false,
      hasAccess: true,
      canPerform: (action: string) => Boolean(viewerPermissions[action as keyof typeof viewerPermissions]),
      checkContribution: vi.fn().mockResolvedValue({ canContribute: false, reason: 'Viewers cannot contribute' }),
      checkMessaging: vi.fn().mockResolvedValue({ canMessage: false, reason: 'Viewers cannot send messages' }),
      refreshPermissions: vi.fn().mockResolvedValue(undefined),
      clearPermissions: vi.fn(),
    });
  });

  describe('ViewerInterface', () => {
    it('should display viewer status banner for read-only participants', () => {
      render(
        <TestWrapper>
          <ViewerInterface storyId={mockStoryId} storyTitle={mockStoryTitle}>
            <div>Story content</div>
          </ViewerInterface>
        </TestWrapper>
      );

      // Check for viewer status banner
      expect(screen.getByText(`Viewing: ${mockStoryTitle}`)).toBeInTheDocument();
      expect(screen.getByText('Read-Only')).toBeInTheDocument();
      expect(screen.getByText('You are following this story as it develops in real-time')).toBeInTheDocument();
    });

    it('should display capabilities and restrictions for viewers', () => {
      render(
        <TestWrapper>
          <ViewerInterface storyId={mockStoryId} storyTitle={mockStoryTitle}>
            <div>Story content</div>
          </ViewerInterface>
        </TestWrapper>
      );

      // Check capabilities
      expect(screen.getByText('What you can do:')).toBeInTheDocument();
      expect(screen.getByText('View story content as it updates')).toBeInTheDocument();
      expect(screen.getByText('React to messages')).toBeInTheDocument();

      // Check restrictions
      expect(screen.getByText('Restrictions:')).toBeInTheDocument();
      expect(screen.getByText('Cannot add story content')).toBeInTheDocument();
      expect(screen.getByText('Cannot send messages')).toBeInTheDocument();
    });

    it('should show share and bookmark buttons', () => {
      const mockOnShare = vi.fn();
      const mockOnBookmark = vi.fn();

      render(
        <TestWrapper>
          <ViewerInterface
            storyId={mockStoryId}
            storyTitle={mockStoryTitle}
            onShareStory={mockOnShare}
            onBookmarkStory={mockOnBookmark}
          >
            <div>Story content</div>
          </ViewerInterface>
        </TestWrapper>
      );

      // Check for action buttons - they may not have aria-labels but have SVG icons
      const buttons = screen.getAllByRole('button');
      const shareButton = buttons[0]; // First button is share
      const bookmarkButton = buttons[1]; // Second button is bookmark

      expect(shareButton).toBeInTheDocument();
      expect(bookmarkButton).toBeInTheDocument();

      // Test button clicks
      fireEvent.click(shareButton);
      fireEvent.click(bookmarkButton);

      expect(mockOnShare).toHaveBeenCalledTimes(1);
      expect(mockOnBookmark).toHaveBeenCalledTimes(1);
    });

    it('should show request contributor access button', () => {
      const mockOnRequestAccess = vi.fn();

      render(
        <TestWrapper>
          <ViewerInterface
            storyId={mockStoryId}
            storyTitle={mockStoryTitle}
            onRequestContributorRole={mockOnRequestAccess}
          >
            <div>Story content</div>
          </ViewerInterface>
        </TestWrapper>
      );

      const requestButton = screen.getByRole('button', { name: /request contributor access/i });
      expect(requestButton).toBeInTheDocument();

      fireEvent.click(requestButton);
      expect(mockOnRequestAccess).toHaveBeenCalledTimes(1);
    });

    it('should not render viewer interface for non-viewers', () => {
      vi.mocked(usePermissions).mockReturnValue({
        permissions: { ...viewerPermissions, role: 'contributor' },
        validation: {
          canContribute: true,
          canMessage: true,
        },
        summary: viewerSummary,
        isLoading: false,
        error: null,
        isCreator: false,
        isContributor: true,
        isViewer: false,
        isMuted: false,
        isBanned: false,
        hasAccess: true,
        canPerform: (action: string) => Boolean(viewerPermissions[action as keyof typeof viewerPermissions]),
        checkContribution: vi.fn().mockResolvedValue({ canContribute: true }),
        checkMessaging: vi.fn().mockResolvedValue({ canMessage: true }),
        refreshPermissions: vi.fn().mockResolvedValue(undefined),
        clearPermissions: vi.fn(),
      });

      render(
        <TestWrapper>
          <ViewerInterface storyId={mockStoryId} storyTitle={mockStoryTitle}>
            <div>Story content</div>
          </ViewerInterface>
        </TestWrapper>
      );

      // Should just render children without viewer interface
      expect(screen.getByText('Story content')).toBeInTheDocument();
      expect(screen.queryByText('Read-Only')).not.toBeInTheDocument();
    });
  });

  describe('ViewerMessageInput', () => {
    it('should display read-only message input with reaction info', () => {
      render(
        <TestWrapper>
          <ViewerMessageInput canReact={true} />
        </TestWrapper>
      );

      expect(screen.getByText('Read-only participant')).toBeInTheDocument();
      expect(screen.getByText('You can react to messages but cannot send your own')).toBeInTheDocument();
    });

    it('should display no interaction message when reactions disabled', () => {
      render(
        <TestWrapper>
          <ViewerMessageInput canReact={false} />
        </TestWrapper>
      );

      expect(screen.getByText('You can follow the conversation but cannot participate')).toBeInTheDocument();
    });

    it('should show request messaging access button', () => {
      const mockOnRequest = vi.fn();

      render(
        <TestWrapper>
          <ViewerMessageInput
            canReact={true}
            onRequestMessagingAccess={mockOnRequest}
          />
        </TestWrapper>
      );

      const requestButton = screen.getByRole('button', { name: /request access/i });
      expect(requestButton).toBeInTheDocument();

      fireEvent.click(requestButton);
      expect(mockOnRequest).toHaveBeenCalledTimes(1);
    });
  });

  describe('ViewerContributionPrompt', () => {
    it('should display contribution restriction message', () => {
      render(
        <TestWrapper>
          <ViewerContributionPrompt storyTitle={mockStoryTitle} />
        </TestWrapper>
      );

      expect(screen.getByText('Read-Only Access')).toBeInTheDocument();
      expect(screen.getByText(`You are currently viewing "${mockStoryTitle}" as a read-only participant`)).toBeInTheDocument();
      expect(screen.getByText('To add content to this story, you need contributor access. The story creator can upgrade your role.')).toBeInTheDocument();
    });

    it('should show request contributor access and contact creator buttons', () => {
      const mockOnRequestAccess = vi.fn();
      const mockOnContactCreator = vi.fn();

      render(
        <TestWrapper>
          <ViewerContributionPrompt
            storyTitle={mockStoryTitle}
            onRequestContributorAccess={mockOnRequestAccess}
            onContactCreator={mockOnContactCreator}
          />
        </TestWrapper>
      );

      const requestButton = screen.getByRole('button', { name: /request contributor access/i });
      const contactButton = screen.getByRole('button', { name: /contact story creator/i });

      expect(requestButton).toBeInTheDocument();
      expect(contactButton).toBeInTheDocument();

      fireEvent.click(requestButton);
      fireEvent.click(contactButton);

      expect(mockOnRequestAccess).toHaveBeenCalledTimes(1);
      expect(mockOnContactCreator).toHaveBeenCalledTimes(1);
    });
  });

  describe('ViewerReactionBar', () => {
    const mockReactions = {
      '👍': 5,
      '❤️': 3,
      '😊': 2,
    };

    const mockUserReactions = ['👍', '❤️'];

    it('should display reaction buttons with counts', () => {
      render(
        <TestWrapper>
          <ViewerReactionBar
            messageId="message-1"
            currentReactions={mockReactions}
            userReactions={mockUserReactions}
          />
        </TestWrapper>
      );

      // Check for reaction buttons
      const thumbsUpButton = screen.getByText('👍');
      const heartButton = screen.getByText('❤️');
      const smileButton = screen.getByText('😊');

      expect(thumbsUpButton).toBeInTheDocument();
      expect(heartButton).toBeInTheDocument();
      expect(smileButton).toBeInTheDocument();

      // Check counts
      expect(screen.getByText('5')).toBeInTheDocument(); // thumbs up count
      expect(screen.getByText('3')).toBeInTheDocument(); // heart count
      expect(screen.getByText('2')).toBeInTheDocument(); // smile count
    });

    it('should handle reaction clicks', () => {
      const mockOnReact = vi.fn();

      render(
        <TestWrapper>
          <ViewerReactionBar
            messageId="message-1"
            currentReactions={mockReactions}
            userReactions={mockUserReactions}
            onReact={mockOnReact}
          />
        </TestWrapper>
      );

      // Click on fire emoji (not currently reacted)
      const fireButton = screen.getByText('🔥');
      fireEvent.click(fireButton);

      expect(mockOnReact).toHaveBeenCalledWith('🔥');
    });

    it('should show reacted state for user reactions', () => {
      render(
        <TestWrapper>
          <ViewerReactionBar
            messageId="message-1"
            currentReactions={mockReactions}
            userReactions={mockUserReactions}
          />
        </TestWrapper>
      );

      // Check that reacted buttons have different styling
      const thumbsUpButton = screen.getByText('👍').closest('button');
      const fireButton = screen.getByText('🔥').closest('button');

      // Reacted button should have different classes
      expect(thumbsUpButton).toHaveClass('bg-blue-100', 'border-blue-300', 'text-blue-800');
      
      // Non-reacted button should have default classes
      expect(fireButton).toHaveClass('border-gray-300', 'text-gray-600');
    });
  });

  describe('PermissionGate', () => {
    const mockPermissions = {
      ...viewerPermissions,
      canManageParticipants: false,
    };

    beforeEach(() => {
      vi.mocked(usePermissions).mockReturnValue({
        permissions: mockPermissions,
        validation: {
          canContribute: false,
          canMessage: false,
        },
        summary: viewerSummary,
        isLoading: false,
        error: null,
        isCreator: false,
        isContributor: false,
        isViewer: true,
        isMuted: false,
        isBanned: false,
        hasAccess: true,
        canPerform: (action: string) => Boolean(mockPermissions[action as keyof typeof mockPermissions]),
        checkContribution: vi.fn().mockResolvedValue({ canContribute: false }),
        checkMessaging: vi.fn().mockResolvedValue({ canMessage: false }),
        refreshPermissions: vi.fn().mockResolvedValue(undefined),
        clearPermissions: vi.fn(),
      });
    });

    it('should render children when role requirement is met', () => {
      render(
        <TestWrapper>
          <PermissionGate storyId={mockStoryId} requireRole="viewer">
            <div>Viewer content</div>
          </PermissionGate>
        </TestWrapper>
      );

      expect(screen.getByText('Viewer content')).toBeInTheDocument();
    });

    it('should render fallback when role requirement is not met', () => {
      render(
        <TestWrapper>
          <PermissionGate
            storyId={mockStoryId}
            requireRole="creator"
            fallback={<div>Access denied</div>}
          >
            <div>Creator content</div>
          </PermissionGate>
        </TestWrapper>
      );

      expect(screen.getByText('Access denied')).toBeInTheDocument();
      expect(screen.queryByText('Creator content')).not.toBeInTheDocument();
    });

    it('should render children when permission requirement is met', () => {
      render(
        <TestWrapper>
          <PermissionGate storyId={mockStoryId} requirePermission="canViewStory">
            <div>Story content</div>
          </PermissionGate>
        </TestWrapper>
      );

      expect(screen.getByText('Story content')).toBeInTheDocument();
    });

    it('should render fallback when permission requirement is not met', () => {
      render(
        <TestWrapper>
          <PermissionGate
            storyId={mockStoryId}
            requirePermission="canManageParticipants"
            fallback={<div>Insufficient permissions</div>}
          >
            <div>Management content</div>
          </PermissionGate>
        </TestWrapper>
      );

      expect(screen.getByText('Insufficient permissions')).toBeInTheDocument();
      expect(screen.queryByText('Management content')).not.toBeInTheDocument();
    });
  });

  describe('User Experience', () => {
    it('should provide clear visual feedback for viewer limitations', () => {
      render(
        <TestWrapper>
          <ViewerInterface storyId={mockStoryId} storyTitle={mockStoryTitle}>
            <ViewerMessageInput canReact={true} />
            <ViewerContributionPrompt storyTitle={mockStoryTitle} />
          </ViewerInterface>
        </TestWrapper>
      );

      // Check for visual feedback elements
      const alertElements = screen.getAllByRole('alert');
      expect(alertElements.length).toBeGreaterThan(0);
      
      // Check for consistent messaging about read-only status
      const readOnlyElements = screen.getAllByText(/read.only|viewer|cannot/i);
      expect(readOnlyElements.length).toBeGreaterThan(2);
    });

    it('should maintain accessibility for viewer interface', () => {
      render(
        <TestWrapper>
          <ViewerInterface storyId={mockStoryId} storyTitle={mockStoryTitle}>
            <ViewerReactionBar
              messageId="message-1"
              currentReactions={{ '👍': 1 }}
              userReactions={[]}
            />
          </ViewerInterface>
        </TestWrapper>
      );

      // Check that reaction buttons are present
      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThan(0);
      
      // Check for reaction emoji button
      expect(screen.getByText('👍')).toBeInTheDocument();
    });

    it('should handle loading and error states gracefully', () => {
      vi.mocked(usePermissions).mockReturnValue({
        permissions: null,
        validation: {
          canContribute: false,
          canMessage: false,
        },
        summary: null,
        isLoading: true,
        error: null,
        isCreator: false,
        isContributor: false,
        isViewer: false,
        isMuted: false,
        isBanned: false,
        hasAccess: false,
        canPerform: () => false,
        checkContribution: vi.fn().mockResolvedValue({ canContribute: false }),
        checkMessaging: vi.fn().mockResolvedValue({ canMessage: false }),
        refreshPermissions: vi.fn().mockResolvedValue(undefined),
        clearPermissions: vi.fn(),
      });

      render(
        <TestWrapper>
          <ViewerInterface storyId={mockStoryId} storyTitle={mockStoryTitle}>
            <div>Story content</div>
          </ViewerInterface>
        </TestWrapper>
      );

      // Should render children without viewer interface when loading
      expect(screen.getByText('Story content')).toBeInTheDocument();
      expect(screen.queryByText('Read-Only')).not.toBeInTheDocument();
    });
  });
});