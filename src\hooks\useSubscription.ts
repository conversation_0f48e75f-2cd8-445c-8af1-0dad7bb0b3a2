import { useState, useEffect, useCallback } from 'react';
import { getSubscriptionStatus } from '../services/subscription';
import { supabase } from '../services/auth'; // Assuming ./auth.ts exports your Supabase client
import type { User } from '@supabase/supabase-js';

interface SubscriptionState {
  isSubscribed: boolean;
  subscriptionStatus: string | null;
  loading: boolean;
  error: Error | null;
}

export const useSubscription = () => {
  const [user, setUser] = useState<User | null>(null);
  const [state, setState] = useState<SubscriptionState>({
    isSubscribed: false,
    subscriptionStatus: null,
    loading: true,
    error: null,
  });

  const checkSubscription = useCallback(async (currentUser: User | null) => {
    if (!currentUser) {
      setState({
        isSubscribed: false,
        subscriptionStatus: null,
        loading: false,
        error: null,
      });
      return;
    }

    setState(prevState => ({ ...prevState, loading: true, error: null }));
    try {
      const { isSubscribed: currentIsSubscribed, status: currentStatus } = await getSubscriptionStatus();
      setState({
        isSubscribed: currentIsSubscribed,
        subscriptionStatus: currentStatus,
        loading: false,
        error: null,
      });
    } catch (err) {
      console.error('Error in useSubscription checkSubscription:', err);
      setState({
        isSubscribed: false,
        subscriptionStatus: null,
        loading: false,
        error: err instanceof Error ? err : new Error('Failed to check subscription status'),
      });
    }
  }, []);

  useEffect(() => {
    // Check immediately on mount with current user
    supabase.auth.getUser().then(({ data: { user: currentUser } }) => {
      setUser(currentUser);
      checkSubscription(currentUser);
    });

    // Listen for auth changes
    const { data: authListener } = supabase.auth.onAuthStateChange(async (_event, session) => {
      const currentUser = session?.user ?? null;
      setUser(currentUser);
      await checkSubscription(currentUser);
    });
    
    // Optional: Listen for subscription changes directly if your backend/webhooks update a table
    // that you can subscribe to with Supabase real-time.
    // This requires the user to have SELECT permissions on the subscriptions table for RLS.
    // const handleSubscriptionChange = async (payload: any) => {
    //   console.log('Subscription change detected:', payload);
    //   const currentUser = user; // Use the user state
    //   if (currentUser && payload.new.user_id === currentUser.id) {
    //     await checkSubscription(currentUser);
    //   }
    // };

    // const subscriptionTableListener = supabase
    //   .channel('public-subscriptions') // Ensure unique channel name
    //   .on(
    //     'postgres_changes',
    //     { event: '*', schema: 'public', table: 'subscriptions' }, 
    //     handleSubscriptionChange
    //   )
    //   .subscribe((status, err) => {
    //     if (status === 'SUBSCRIBED') {
    //       console.log('Successfully subscribed to subscriptions table changes!');
    //     } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
    //       console.error('Failed to subscribe to subscriptions table changes:', err);
    //     }
    //   });
      
    return () => {
      authListener?.unsubscribe();
      // if (subscriptionTableListener) {
      //   supabase.removeChannel(subscriptionTableListener);
      // }
    };
  }, [checkSubscription]); // checkSubscription is memoized with useCallback
  
  return state;
};
