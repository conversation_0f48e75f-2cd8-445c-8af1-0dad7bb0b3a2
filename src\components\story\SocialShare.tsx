import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON>Header,
  Di<PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Facebook,
  Twitter,
  Linkedin,
  Copy,
  Check,
  Share2,
  X,
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";

interface SocialShareProps {
  storyId: string;
  storyTitle: string;
}

const SocialShare: React.FC<SocialShareProps> = ({ storyId, storyTitle }) => {
  const [copied, setCopied] = useState(false);

  // Base URL for the website
  const baseUrl = "https://wordbywordstory.com";
  const shareUrl = `${baseUrl}/story/${storyId}`;

  // Encoded for use in social sharing URLs
  const encodedTitle = encodeURIComponent(
    `Check out "${storyTitle}" on Word by Word Story!`,
  );
  const encodedUrl = encodeURIComponent(shareUrl);

  // Social media share URLs
  const facebookShareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`;
  const twitterShareUrl = `https://twitter.com/intent/tweet?text=${encodedTitle}&url=${encodedUrl}`;
  const linkedinShareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`;

  const handleCopyLink = () => {
    navigator.clipboard
      .writeText(shareUrl)
      .then(() => {
        setCopied(true);
        toast({
          title: "Link copied!",
          description: "Story link copied to clipboard",
        });

        setTimeout(() => {
          setCopied(false);
        }, 3000);
      })
      .catch((error) => {
        console.error("Failed to copy link:", error);
        toast({
          title: "Copy failed",
          description: "Could not copy the link. Please try again.",
          variant: "destructive",
        });
      });
  };

  const handleShare = (url: string) => {
    window.open(url, "_blank", "noopener,noreferrer");
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" className="px-4">
          <Share2 className="h-4 w-4 mr-2" />
          Share
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center">Share this story</DialogTitle>
        </DialogHeader>
        <div className="flex justify-center space-x-4 py-4">
          <Button
            variant="outline"
            size="icon"
            className="bg-[#1877F2] text-white hover:bg-[#1877F2]/90 border-none rounded-full w-10 h-10"
            onClick={() => handleShare(facebookShareUrl)}
            title="Share on Facebook"
          >
            <Facebook className="h-5 w-5" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="bg-[#1DA1F2] text-white hover:bg-[#1DA1F2]/90 border-none rounded-full w-10 h-10"
            onClick={() => handleShare(twitterShareUrl)}
            title="Share on Twitter/X"
          >
            <X className="h-5 w-5" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="bg-[#0A66C2] text-white hover:bg-[#0A66C2]/90 border-none rounded-full w-10 h-10"
            onClick={() => handleShare(linkedinShareUrl)}
            title="Share on LinkedIn"
          >
            <Linkedin className="h-5 w-5" />
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <Input value={shareUrl} readOnly className="flex-1" />
          <Button
            variant="outline"
            size="icon"
            className="flex-shrink-0"
            onClick={handleCopyLink}
          >
            {copied ? (
              <Check className="h-4 w-4 text-green-500" />
            ) : (
              <Copy className="h-4 w-4" />
            )}
          </Button>
        </div>

        <p className="text-center text-sm text-muted-foreground mt-2">
          Share this story with friends and fellow writers!
        </p>
      </DialogContent>
    </Dialog>
  );
};

export default SocialShare;
