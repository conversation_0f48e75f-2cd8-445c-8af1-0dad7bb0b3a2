# Performance Optimization Guide

## Overview

This guide outlines the performance optimization strategies implemented in the Word by Word Story application, including tools, techniques, and monitoring approaches to ensure optimal user experience across all devices and network conditions.

## Performance Targets

### Core Web Vitals Thresholds

| Metric | Good | Needs Improvement | Poor |
|--------|------|------------------|------|
| **LCP** (Largest Contentful Paint) | ≤ 2.5s | 2.5s - 4.0s | > 4.0s |
| **FID** (First Input Delay) | ≤ 100ms | 100ms - 300ms | > 300ms |
| **CLS** (Cumulative Layout Shift) | ≤ 0.1 | 0.1 - 0.25 | > 0.25 |
| **TTFB** (Time to First Byte) | ≤ 800ms | 800ms - 1.8s | > 1.8s |
| **INP** (Interaction to Next Paint) | ≤ 200ms | 200ms - 500ms | > 500ms |

### Bundle Size Targets

| Bundle Type | Target Size | Maximum Size |
|-------------|-------------|--------------|
| Main JavaScript | < 300KB | 500KB |
| Vendor Libraries | < 800KB | 1MB |
| CSS Bundle | < 50KB | 100KB |
| Individual Chunks | < 150KB | 200KB |
| Total Bundle | < 1MB | 1.5MB |

### Lighthouse Score Targets

- **Performance**: ≥ 90
- **Accessibility**: ≥ 95
- **Best Practices**: ≥ 90
- **SEO**: ≥ 95

## Optimization Strategies

### 1. Code Splitting and Lazy Loading

#### Route-based Code Splitting
```typescript
// Lazy load page components
const Gallery = lazy(() => import('./pages/Gallery'))
const CreateStory = lazy(() => import('./pages/CreateStory'))
const Subscription = lazy(() => import('./pages/Subscription'))

// Use Suspense for loading states
<Suspense fallback={<LoadingSpinner />}>
  <Routes>
    <Route path="/gallery" element={<Gallery />} />
    <Route path="/create-story" element={<CreateStory />} />
    <Route path="/subscription" element={<Subscription />} />
  </Routes>
</Suspense>
```

#### Component-based Code Splitting
```typescript
// Lazy load heavy components
const PostHogTestPanel = lazy(() => import('./components/dev/PostHogTestPanel'))
const SubscriptionPlansCard = lazy(() => import('./components/Subscription/SubscriptionPlansCard'))

// Dynamic imports for conditional features
const loadStripe = () => import('@stripe/stripe-js')
const loadAnalytics = () => import('./lib/analytics')
```

#### Manual Chunk Configuration
```typescript
// vite.performance.config.ts
manualChunks: {
  vendor: ['react', 'react-dom', 'react-router-dom'],
  ui: ['@radix-ui/*', 'lucide-react'],
  data: ['@tanstack/react-query', 'react-hook-form', 'zod'],
  auth: ['@supabase/supabase-js'],
  analytics: ['posthog-js']
}
```

### 2. Asset Optimization

#### Image Optimization
```typescript
// Lazy loading images
<img 
  src={imageSrc}
  alt={altText}
  loading="lazy"
  decoding="async"
  width={width}
  height={height}
/>

// Modern image formats
<picture>
  <source srcSet="image.webp" type="image/webp" />
  <source srcSet="image.avif" type="image/avif" />
  <img src="image.jpg" alt="Description" />
</picture>

// Responsive images
<img
  srcSet="
    image-320w.jpg 320w,
    image-640w.jpg 640w,
    image-1280w.jpg 1280w
  "
  sizes="(max-width: 320px) 280px, (max-width: 640px) 600px, 1200px"
  src="image-640w.jpg"
  alt="Description"
/>
```

#### Font Optimization
```css
/* Preload critical fonts */
<link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin>

/* Font display optimization */
@font-face {
  font-family: 'Inter';
  src: url('/fonts/inter-var.woff2') format('woff2');
  font-display: swap;
  font-weight: 100 900;
}

/* Reduce font loading impact */
body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
}
```

### 3. Critical Resource Optimization

#### Resource Hints
```html
<!-- DNS prefetch for external domains -->
<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link rel="dns-prefetch" href="//supabase.co">

<!-- Preconnect for critical third-party origins -->
<link rel="preconnect" href="https://checkout.stripe.com">
<link rel="preconnect" href="https://app.posthog.com">

<!-- Preload critical resources -->
<link rel="preload" href="/api/stories" as="fetch" crossorigin>
```

#### Critical CSS Inlining
```typescript
// Extract and inline critical CSS for above-the-fold content
const criticalCSS = `
  body { margin: 0; font-family: Inter, sans-serif; }
  .header { height: 64px; background: white; }
  .hero { min-height: 400px; }
`
```

### 4. JavaScript Optimization

#### Tree Shaking
```typescript
// Use named imports to enable tree shaking
import { Button } from './components/ui/button'
import { Card, CardContent } from './components/ui/card'

// Avoid default imports of large libraries
import { format } from 'date-fns' // Good
import * as dateFns from 'date-fns' // Bad
```

#### Bundle Analysis and Optimization
```typescript
// Remove unused dependencies
npm run perf:bundle

// Identify large chunks
npm run perf:analyze

// Replace heavy libraries with lighter alternatives
// lodash → native methods or individual functions
// moment → date-fns
// entire UI libraries → individual components
```

### 5. API and Data Optimization

#### React Query Optimization
```typescript
// Prefetch critical data
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
    },
  },
})

// Implement infinite queries for large lists
const { data, fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteQuery({
  queryKey: ['stories'],
  queryFn: fetchStories,
  getNextPageParam: (lastPage) => lastPage.nextCursor,
})
```

#### Supabase Optimization
```typescript
// Select only needed columns
const { data } = await supabase
  .from('stories')
  .select('id, title, created_at, user:users(username)')
  .range(0, 9)

// Use indexes for filtered queries
const { data } = await supabase
  .from('stories')
  .select('*')
  .eq('status', 'ACTIVE')
  .order('created_at', { ascending: false })
```

### 6. Rendering Optimization

#### React Performance Patterns
```typescript
// Use React.memo for expensive components
const ExpensiveComponent = React.memo(({ data }) => {
  return <ComplexVisualization data={data} />
})

// Optimize re-renders with useMemo and useCallback
const Component = ({ items, onSelect }) => {
  const filteredItems = useMemo(
    () => items.filter(item => item.visible),
    [items]
  )
  
  const handleSelect = useCallback(
    (id) => onSelect(id),
    [onSelect]
  )
  
  return <ItemList items={filteredItems} onSelect={handleSelect} />
}

// Virtual scrolling for large lists
import { FixedSizeList as List } from 'react-window'

const VirtualizedList = ({ items }) => (
  <List
    height={600}
    itemCount={items.length}
    itemSize={50}
    itemData={items}
  >
    {Row}
  </List>
)
```

#### CSS Performance
```css
/* Use CSS containment */
.story-card {
  contain: layout style paint;
}

/* Optimize animations */
.fade-in {
  animation: fadeIn 0.3s ease-out;
  will-change: opacity;
}

/* Use transform instead of changing layout properties */
.slide-in {
  transform: translateX(100%);
  transition: transform 0.3s ease-out;
}

.slide-in.visible {
  transform: translateX(0);
}
```

## Performance Monitoring

### 1. Automated Performance Auditing

#### Lighthouse CI
```bash
# Run Lighthouse audit
npm run perf:lighthouse

# Continuous monitoring
npm run perf:analyze
```

#### Bundle Analysis
```bash
# Analyze bundle composition
npm run perf:bundle

# Build with performance config
npm run perf:build
```

### 2. Real User Monitoring (RUM)

#### Web Vitals Collection
```javascript
// Integrate web vitals monitoring
import { WEB_VITALS_CLIENT_CODE } from './scripts/performance/web-vitals-monitor.js'

// Add to HTML head
<script>{WEB_VITALS_CLIENT_CODE}</script>

// Configure analytics endpoint
window.webVitalsEndpoint = '/api/analytics/web-vitals'
```

#### PostHog Integration
```typescript
// Track performance metrics
posthog.capture('page_load_performance', {
  lcp: metrics.LCP,
  fid: metrics.FID,
  cls: metrics.CLS,
  page: window.location.pathname
})
```

### 3. Performance Regression Testing

#### CI/CD Integration
```yaml
# .github/workflows/performance.yml
name: Performance Tests
on: [push, pull_request]

jobs:
  performance:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run build
      - run: npm run perf:analyze
      - uses: treosh/lighthouse-ci-action@v9
        with:
          uploadArtifacts: true
```

## Performance Best Practices

### 1. Development Workflow
- Run performance audits before major releases
- Monitor bundle size in pull requests
- Test on low-end devices and slow networks
- Use performance budgets to prevent regressions

### 2. Code Quality
- Avoid memory leaks (clean up event listeners, intervals)
- Use efficient data structures and algorithms
- Minimize DOM manipulations
- Implement proper error boundaries

### 3. Third-party Integration
- Lazy load analytics and tracking scripts
- Use async loading for non-critical third-party resources
- Monitor third-party script impact
- Implement fallbacks for failed external resources

### 4. Caching Strategy
```typescript
// Service Worker for asset caching
self.addEventListener('fetch', (event) => {
  if (event.request.destination === 'image') {
    event.respondWith(
      caches.match(event.request).then((response) => {
        return response || fetch(event.request)
      })
    )
  }
})

// HTTP caching headers
const cacheControl = {
  'Cache-Control': 'public, max-age=31536000, immutable'
}
```

## Troubleshooting Common Issues

### Large Bundle Size
1. Analyze bundle composition with `npm run perf:bundle`
2. Identify large dependencies
3. Replace with lighter alternatives or lazy load
4. Implement code splitting

### Slow LCP
1. Optimize server response time
2. Preload critical resources
3. Optimize images above the fold
4. Remove render-blocking resources

### High CLS
1. Add size attributes to images
2. Reserve space for dynamic content
3. Avoid inserting content above existing content
4. Use CSS transforms for animations

### Poor JavaScript Performance
1. Break up long tasks
2. Use web workers for heavy computations
3. Implement virtual scrolling for large lists
4. Optimize React re-renders

## Performance Tools and Scripts

### Available Commands
```bash
# Lighthouse audit
npm run perf:lighthouse

# Bundle analysis
npm run perf:bundle

# Web vitals monitoring
npm run perf:vitals

# Performance-optimized build
npm run perf:build

# Complete performance analysis
npm run perf:analyze
```

### Tool Configuration
- **Lighthouse**: `scripts/performance/lighthouse-audit.js`
- **Bundle Analyzer**: `scripts/performance/bundle-analyzer.js`
- **Web Vitals**: `scripts/performance/web-vitals-monitor.js`
- **Vite Performance Config**: `vite.performance.config.ts`

## Resources and References

- [Web Vitals](https://web.dev/vitals/)
- [Lighthouse](https://developers.google.com/web/tools/lighthouse)
- [React Performance](https://react.dev/learn/render-and-commit)
- [Vite Performance](https://vitejs.dev/guide/performance.html)
- [Bundle Analysis](https://webpack.js.org/guides/code-splitting/)

---

*This guide should be updated regularly as new performance optimizations are implemented and new tools become available.*