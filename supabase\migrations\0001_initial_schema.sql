-- Enable UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (largely managed by Supa<PERSON> Auth, but we can add custom fields if needed later)
-- Supabase automatically creates a users table in the auth schema.
-- We will reference auth.users.id

-- Stories Table
CREATE TABLE public.stories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    author_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT NOT NULL DEFAULT 'in_progress' CHECK (status IN ('in_progress', 'completed', 'abandoned')),
    max_contributors INT NOT NULL DEFAULT 10,
    max_words_per_contribution INT NOT NULL DEFAULT 50,
    current_word_count INT NOT NULL DEFAULT 0,
    is_public BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    completed_at TIMESTAMPTZ,
    last_contribution_at TIMESTAMPTZ,
    genre TEXT,
    cover_image_url TEXT
);
COMMENT ON TABLE public.stories IS 'Stores information about each collaborative story.';
COMMENT ON COLUMN public.stories.author_id IS 'The user who initiated the story.';
COMMENT ON COLUMN public.stories.status IS 'Current status of the story (e.g., in_progress, completed).';

-- Contributions Table
CREATE TABLE public.contributions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    story_id UUID NOT NULL REFERENCES public.stories(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    word_count INT NOT NULL,
    "order" INT NOT NULL, -- "order" is a reserved keyword, so quoted
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now(),
    CONSTRAINT unique_story_order UNIQUE (story_id, "order")
);
COMMENT ON TABLE public.contributions IS 'Stores individual contributions to stories.';
COMMENT ON COLUMN public.contributions.word_count IS 'Number of words in this specific contribution.';
COMMENT ON COLUMN public.contributions."order" IS 'Sequential order of the contribution within the story.';


-- Votes Table
CREATE TABLE public.votes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    story_id UUID NOT NULL REFERENCES public.stories(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    vote_type TEXT NOT NULL CHECK (vote_type IN ('upvote', 'downvote')),
    created_at TIMESTAMPTZ DEFAULT now(),
    CONSTRAINT unique_user_story_vote UNIQUE (user_id, story_id)
);
COMMENT ON TABLE public.votes IS 'Tracks user votes on stories.';

-- Subscriptions Table (for story notifications)
CREATE TABLE public.subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    story_id UUID NOT NULL REFERENCES public.stories(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT now(),
    CONSTRAINT unique_user_story_subscription UNIQUE (user_id, story_id)
);
COMMENT ON TABLE public.subscriptions IS 'Tracks user subscriptions to stories for notifications.';

-- Payments Table (for Stripe subscriptions)
CREATE TABLE public.payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    stripe_customer_id TEXT UNIQUE,
    stripe_subscription_id TEXT UNIQUE,
    subscription_status TEXT, -- e.g., active, canceled, past_due
    current_period_start TIMESTAMPTZ,
    current_period_end TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT now(),
    updated_at TIMESTAMPTZ DEFAULT now()
);
COMMENT ON TABLE public.payments IS 'Stores information related to user payment subscriptions via Stripe.';

-- Indexes for performance
CREATE INDEX idx_stories_author_id ON public.stories(author_id);
CREATE INDEX idx_stories_status ON public.stories(status);
CREATE INDEX idx_contributions_story_id ON public.contributions(story_id);
CREATE INDEX idx_contributions_user_id ON public.contributions(user_id);
CREATE INDEX idx_votes_story_id ON public.votes(story_id);
CREATE INDEX idx_votes_user_id ON public.votes(user_id);
CREATE INDEX idx_subscriptions_story_id ON public.subscriptions(story_id);
CREATE INDEX idx_subscriptions_user_id ON public.subscriptions(user_id);
CREATE INDEX idx_payments_user_id ON public.payments(user_id);

-- Function to update updated_at column
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER set_stories_timestamp
BEFORE UPDATE ON public.stories
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_contributions_timestamp
BEFORE UPDATE ON public.contributions
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_payments_timestamp
BEFORE UPDATE ON public.payments
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- RLS Policies will be handled in a separate task/subtask (e.g., 3.2) 