[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [avatar](../README.md) / AvatarImage

# Variable: AvatarImage

> `const` **AvatarImage**: `ForwardRefExoticComponent`\<`Omit`\<`AvatarImageProps` & `RefAttributes`\<`HTMLImageElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLImageElement`\>\>

Defined in: [src/components/ui/avatar.tsx:21](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/avatar.tsx#L21)
