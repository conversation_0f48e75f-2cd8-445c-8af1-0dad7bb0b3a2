# Subscription Management UI Components

This directory contains React components for managing Stripe-based ad-free subscriptions.

## Components

### `SubscriptionPage`
Main container component that handles the subscription management flow.

**Props:** None (uses hooks internally)

**Features:**
- Uses `useSubscription()` hook to get current subscription status
- Displays `CurrentSubscriptionCard` for subscribed users
- Displays `SubscriptionPlansCard` for non-subscribed users
- <PERSON>les checkout session creation and subscription management

### `CurrentSubscriptionCard`
Displays subscription status and management options for subscribed users.

**Props:**
- `subscriptionStatus: string | null` - Current subscription status from Stripe
- `isSubscribed: boolean` - Whether user has an active subscription
- `currentPlan?: PricingPlan` - Optional plan information to display
- `onManage: () => Promise<void>` - Callback to open Stripe Customer Portal

**Features:**
- Status-specific styling and messaging
- Subscription management button
- Loading states
- Error handling for different subscription states

### `SubscriptionPlansCard`
Displays available subscription plans for non-subscribed users.

**Props:**
- `plans: PricingPlan[]` - Array of available pricing plans
- `onSubscribe: (priceId: string) => Promise<void>` - Callback to start checkout
- `isLoading?: boolean` - Optional loading state

**Features:**
- Responsive grid layout
- "Best Value" highlighting
- Individual loading states per plan
- Feature lists with checkmarks
- Secure payment messaging

## Types

### `PricingPlan`
```typescript
interface PricingPlan {
  id: string;        // Stripe Price ID
  name: string;      // Display name
  description: string;
  price: string;     // Formatted price (e.g., "$10/month")
  features: string[];
  isCurrentPlan?: boolean;
}
```

## Setup Requirements

### 1. Environment Variables
Add to your `.env` file:
```env
VITE_STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key_here
```

### 2. Stripe Price IDs
Replace placeholder price IDs in `SubscriptionPage.tsx`:
- `YOUR_STRIPE_PRICE_ID_MONTHLY` → actual monthly price ID
- `YOUR_STRIPE_PRICE_ID_YEARLY` → actual yearly price ID

### 3. Routes
Add these routes to your React Router setup:
```typescript
import { PaymentSuccess, PaymentCancelled } from '../pages';

// In your router:
<Route path="/payment-success" element={<PaymentSuccess />} />
<Route path="/payment-cancelled" element={<PaymentCancelled />} />
<Route path="/subscription" element={<SubscriptionPage />} />
```

### 4. Backend Requirements
Ensure these Supabase Edge Functions are deployed and configured:
- `create-checkout-session` - Creates Stripe checkout sessions
- `stripe-webhook` - Handles Stripe webhook events
- `create-customer-portal-session` - Creates customer portal sessions

### 5. Database Schema
Ensure your `subscriptions` table has these columns:
```sql
CREATE TABLE subscriptions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) NOT NULL,
  stripe_subscription_id TEXT UNIQUE,
  stripe_customer_id TEXT,
  status TEXT NOT NULL,
  current_period_start TIMESTAMPTZ,
  current_period_end TIMESTAMPTZ,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## Usage Example

```typescript
import { SubscriptionPage } from '../components/Subscription';

function App() {
  return (
    <Routes>
      <Route path="/subscription" element={<SubscriptionPage />} />
      {/* other routes */}
    </Routes>
  );
}
```

## Customization

### Styling
Components use Tailwind CSS classes. Customize by:
- Modifying existing classes
- Adding custom CSS classes
- Using CSS-in-JS solutions

### Plan Configuration
Update `availablePlans` in `SubscriptionPage.tsx` or fetch from your backend:
```typescript
// Fetch from backend
useEffect(() => {
  fetchPricingPlans().then(setPlans);
}, []);
```

### Success/Cancel URLs
Customize redirect URLs in `handleSubscribe`:
```typescript
const successUrl = `${window.location.origin}/custom-success`;
const cancelUrl = `${window.location.origin}/custom-cancel`;
```

## Error Handling
Components include basic error handling with `alert()` calls. Consider replacing with:
- Toast notifications (react-hot-toast, react-toastify)
- Modal dialogs
- Inline error messages

## Testing
Test the subscription flow with Stripe test cards:
- `****************` - Successful payment
- `****************` - Declined payment
- `****************` - Insufficient funds

Remember to use test mode API keys during development!