import { useEffect } from 'react';
import { usePostHog } from 'posthog-js/react';
import { analytics, trackUserEvent } from '../lib/posthog';

// Custom hook for PostHog integration with authentication
export const usePostHogAuth = (user: any) => {
  const posthog = usePostHog();

  useEffect(() => {
    if (!posthog) return;

    if (user) {
      // User is logged in - identify them with PostHog
      analytics.identify(user.id, {
        email: user.email,
        created_at: user.created_at,
        // Add any other user properties you want to track
      });

      // Track login event (only if this is a fresh login)
      // You might want to add logic to prevent duplicate login events
      const lastLogin = sessionStorage.getItem('posthog_last_login');
      const currentLoginTime = Date.now().toString();
      
      if (lastLogin !== currentLoginTime) {
        trackUserEvent.userLoggedIn(user.id, 'supabase_auth');
        sessionStorage.setItem('posthog_last_login', currentLoginTime);
      }
    } else {
      // User is logged out - reset PostHog identity
      analytics.reset();
      sessionStorage.removeItem('posthog_last_login');
    }
  }, [user, posthog]);

  return posthog;
};

// Hook for tracking page views with PostHog
export const usePostHogPageView = (pageName?: string) => {
  const posthog = usePostHog();

  useEffect(() => {
    if (posthog && pageName) {
      analytics.pageView(pageName);
    }
  }, [posthog, pageName]);
};

// Hook for tracking time spent on a page/component
export const usePostHogTimeTracking = (sectionName: string) => {
  const posthog = usePostHog();

  useEffect(() => {
    if (!posthog) return;

    const startTime = Date.now();

    return () => {
      const timeSpent = Math.round((Date.now() - startTime) / 1000);
      if (timeSpent > 1) { // Only track if user spent more than 1 second
        analytics.track('time_spent', {
          section: sectionName,
          time_seconds: timeSpent,
        });
      }
    };
  }, [posthog, sectionName]);
};

export { usePostHog };