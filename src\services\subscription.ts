import { supabase } from './auth'; // Assuming ./auth.ts exports your Supabase client
import { loadStripe } from '@stripe/stripe-js';

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLIC_KEY!);

export const createCheckoutSession = async (priceId: string, successUrl: string, cancelUrl: string) => {
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    throw new Error('You must be logged in to subscribe');
  }
  
  const { data, error } = await supabase.functions.invoke('create-checkout-session', {
    body: { 
      user_id: user.id,
      price_id: priceId,
      success_url: successUrl,
      cancel_url: cancelUrl
    }
  });
  
  if (error) {
    console.error('Error invoking create-checkout-session function:', error);
    throw error;
  }
  
  const stripe = await stripePromise;
  if (!stripe) {
    throw new Error('Stripe.js has not loaded yet.');
  }

  if (!data || !data.sessionId) {
    console.error('No sessionId returned from create-checkout-session function', data);
    throw new Error('Could not retrieve checkout session ID.');
  }

  const { error: stripeError } = await stripe.redirectToCheckout({
    sessionId: data.sessionId
  });
  
  if (stripeError) {
    console.error('Stripe redirectToCheckout error:', stripeError);
    throw stripeError;
  }
};

export const getSubscriptionStatus = async () => {
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) return { isSubscribed: false, status: null };
  
  try {
    const { data, error } = await supabase
      .from('subscriptions') // Your subscriptions table
      .select('status')
      .eq('user_id', user.id)
      // .in('status', ['active', 'trialing']) // Consider what statuses mean \"subscribed\"
      .maybeSingle(); // Use maybeSingle to handle null if no record found gracefully
    
    if (error && error.code !== 'PGRST116') { // PGRST116: "Searched item was not found"
        console.error('Error fetching subscription status:', error);
        throw error;
    }
    
    if (!data) return { isSubscribed: false, status: null };
    
    return { 
        isSubscribed: data.status === 'active' || data.status === 'trialing', 
        status: data.status 
    };

  } catch (err) {
    console.error('Exception in getSubscriptionStatus:', err);
    return { isSubscribed: false, status: null };
  }
};

export const manageSubscription = async () => {
  // This usually involves redirecting to a Stripe-hosted customer portal
  // You'll need to create a Supabase function that generates a Stripe Billing Portal session
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    throw new Error('You must be logged in to manage your subscription.');
  }

  // First, retrieve the Stripe customer ID from your database
  let stripeCustomerId: string | null = null;
  try {
    const { data: customerData, error: customerError } = await supabase
      .from('subscriptions') // Or a 'customers' or 'users' table if you store it there
      .select('stripe_customer_id')
      .eq('user_id', user.id)
      .neq('stripe_customer_id', null)
      .limit(1)
      .single();

    if (customerError && customerError.code !== 'PGRST116') throw customerError;
    if (customerData && customerData.stripe_customer_id) {
      stripeCustomerId = customerData.stripe_customer_id;
    } else {
      // Fallback: attempt to get from users table if you store it there
      const { data: userData, error: userDbError } = await supabase
        .from('users') // Assuming a public users table with stripe_customer_id
        .select('stripe_customer_id')
        .eq('id', user.id)
        .single();
      if (userDbError && userDbError.code !== 'PGRST116') throw userDbError;
      if (userData && userData.stripe_customer_id) {
        stripeCustomerId = userData.stripe_customer_id;
      }
    }

    if (!stripeCustomerId) {
        console.warn('Stripe customer ID not found for user. Cannot open portal.');
        // Optionally, you could try to create a customer in Stripe here if one doesn't exist,
        // but typically the webhook for checkout.session.completed handles customer creation and storage.
        throw new Error('Stripe customer ID not found.');
    }

  } catch (dbError) {
    console.error('Error fetching Stripe customer ID:', dbError);
    throw new Error('Could not retrieve customer details to manage subscription.');
  }

  const { data, error } = await supabase.functions.invoke('create-customer-portal-session', {
      body: { customer_id: stripeCustomerId }
  });

  if (error) {
    console.error('Error invoking create-customer-portal-session:', error);
    throw error;
  }
  if (data && data.url) {
      window.location.href = data.url;
  } else {
      console.error('No URL returned from create-customer-portal-session', data);
      throw new Error('Could not retrieve customer portal URL.');
  }
};
