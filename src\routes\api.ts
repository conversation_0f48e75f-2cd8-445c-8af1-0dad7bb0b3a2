import express from 'express';
import { ContributionController } from '@/controllers/contributionController';
import {
  completeStory,
  archiveStory,
  getStoryState,
  joinStory,
  updateUserSession,
  getStoryUserSessions,
  subscribeToStoryUpdates
} from '@/api/story-contributions';

const router = express.Router();

// Existing story routes
router.post('/stories', ContributionController.createStory);
router.get('/stories/:storyId', ContributionController.getStory);
router.post('/stories/:storyId/contribute', ContributionController.submitContribution);

// Story state and management routes
router.get('/stories/:storyId/contributions', getStoryState);
router.post('/stories/:storyId/participants', joinStory);
router.post('/stories/:storyId/sessions', updateUserSession);
router.get('/stories/:storyId/sessions', getStoryUserSessions);

// Story completion and archival
router.post('/stories/:storyId/complete', completeStory);
router.post('/stories/:storyId/archive', archiveStory);

// Real-time updates (SSE)
router.get('/stories/:storyId/subscribe', subscribeToStoryUpdates);

export default router;
