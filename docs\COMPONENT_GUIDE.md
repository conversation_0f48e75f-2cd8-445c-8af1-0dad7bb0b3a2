# Component Guide

## Overview

This guide provides comprehensive documentation for all React components in the Word by Word Story application. Components are organized by category and include usage examples, props documentation, and best practices.

## Component Architecture

### Design System

The application follows a structured component architecture:

- **UI Components** (`/src/components/ui/`): Reusable, low-level components based on Radix UI
- **Feature Components** (`/src/components/`): Business logic components
- **Layout Components** (`/src/components/layout/`): Page structure components
- **Page Components** (`/src/pages/`): Route-level components

### Component Naming Convention

- **PascalCase** for component names and files
- **kebab-case** for CSS classes and data attributes
- **camelCase** for props and variables

## UI Components

### Button

A versatile button component with multiple variants and states.

**File:** `src/components/ui/button.tsx`

#### Props

```typescript
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  asChild?: boolean
}
```

#### Usage Examples

```typescript
import { Button } from '@/components/ui/button'
import { Plus, Download } from 'lucide-react'

// Basic button
<Button>Click me</Button>

// Button variants
<Button variant="destructive">Delete</Button>
<Button variant="outline">Cancel</Button>
<Button variant="ghost">Subtle</Button>

// Button sizes
<Button size="sm">Small</Button>
<Button size="lg">Large</Button>
<Button size="icon"><Plus className="h-4 w-4" /></Button>

// With icons
<Button>
  <Download className="mr-2 h-4 w-4" />
  Download
</Button>

// As link wrapper
<Button asChild>
  <Link to="/stories">View Stories</Link>
</Button>

// Disabled state
<Button disabled>Loading...</Button>
```

#### Accessibility

- Supports keyboard navigation
- Screen reader compatible
- Focus indicators
- ARIA attributes included

### Card

A flexible container component for organizing content.

**File:** `src/components/ui/card.tsx`

#### Components

- `Card`: Main container
- `CardHeader`: Header section
- `CardTitle`: Title text
- `CardDescription`: Subtitle/description
- `CardContent`: Main content area
- `CardFooter`: Footer section with actions

#### Usage Examples

```typescript
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

// Basic card
<Card>
  <CardHeader>
    <CardTitle>Story Title</CardTitle>
    <CardDescription>A collaborative writing adventure</CardDescription>
  </CardHeader>
  <CardContent>
    <p>Story content goes here...</p>
  </CardContent>
  <CardFooter>
    <Button>Join Story</Button>
  </CardFooter>
</Card>

// Card with custom styling
<Card className="w-96 hover:shadow-lg transition-shadow">
  <CardContent className="p-6">
    <p>Simple card content</p>
  </CardContent>
</Card>
```

### Input

Form input component with validation support.

**File:** `src/components/ui/input.tsx`

#### Props

```typescript
interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: string
  label?: string
  helperText?: string
}
```

#### Usage Examples

```typescript
import { Input } from '@/components/ui/input'

// Basic input
<Input placeholder="Enter your name" />

// With label and validation
<Input
  label="Email Address"
  type="email"
  placeholder="<EMAIL>"
  error="Please enter a valid email address"
  helperText="We'll never share your email"
/>

// Controlled input
const [value, setValue] = useState('')
<Input
  value={value}
  onChange={(e) => setValue(e.target.value)}
  placeholder="Type here..."
/>
```

### Badge

Small status indicators and tags.

**File:** `src/components/ui/badge.tsx`

#### Props

```typescript
interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'secondary' | 'destructive' | 'outline'
}
```

#### Usage Examples

```typescript
import { Badge } from '@/components/ui/badge'

// Basic badges
<Badge>Default</Badge>
<Badge variant="secondary">Draft</Badge>
<Badge variant="destructive">Error</Badge>
<Badge variant="outline">Pending</Badge>

// Custom styling
<Badge className="bg-green-100 text-green-800">
  Active
</Badge>
```

### Dialog

Modal dialog component for overlays and forms.

**File:** `src/components/ui/dialog.tsx`

#### Components

- `Dialog`: Root component
- `DialogTrigger`: Button that opens dialog
- `DialogContent`: Modal content container
- `DialogHeader`: Header section
- `DialogTitle`: Dialog title
- `DialogDescription`: Dialog description
- `DialogFooter`: Footer with actions
- `DialogClose`: Close button

#### Usage Examples

```typescript
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'

<Dialog>
  <DialogTrigger asChild>
    <Button variant="outline">Edit Profile</Button>
  </DialogTrigger>
  <DialogContent className="sm:max-w-[425px]">
    <DialogHeader>
      <DialogTitle>Edit profile</DialogTitle>
      <DialogDescription>
        Make changes to your profile here. Click save when you're done.
      </DialogDescription>
    </DialogHeader>
    <div className="grid gap-4 py-4">
      {/* Form content */}
    </div>
    <DialogFooter>
      <Button type="submit">Save changes</Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

## Feature Components

### StoryCard

Displays story information in the gallery.

**File:** `src/components/StoryCard.tsx`

#### Props

```typescript
interface StoryCardProps {
  story: {
    id: string
    title: string
    description: string
    status: 'ACTIVE' | 'COMPLETED' | 'PAUSED'
    contribution_mode: string
    current_contribution_count: number
    max_contributions: number
    creator: {
      username: string
      avatar_url?: string
    }
    participants_count: number
    created_at: string
    latest_contribution?: {
      text: string
      user: { username: string }
      created_at: string
    }
  }
  onJoin?: (storyId: string) => void
  onView?: (storyId: string) => void
  className?: string
}
```

#### Usage Examples

```typescript
import { StoryCard } from '@/components/StoryCard'

const stories = [
  {
    id: '1',
    title: 'The Mysterious Forest',
    description: 'A collaborative adventure...',
    status: 'ACTIVE',
    // ... other story properties
  }
]

<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {stories.map((story) => (
    <StoryCard
      key={story.id}
      story={story}
      onJoin={(id) => handleJoinStory(id)}
      onView={(id) => navigate(`/story/${id}`)}
    />
  ))}
</div>
```

### ContributionInput

Input component for adding story contributions.

**File:** `src/components/ContributionInput.tsx`

#### Props

```typescript
interface ContributionInputProps {
  storyId: string
  contributionMode: 'word' | 'sentence' | 'paragraph'
  wordsPerContribution: number
  onContribute: (text: string) => Promise<void>
  disabled?: boolean
  placeholder?: string
  maxLength?: number
}
```

#### Usage Examples

```typescript
import { ContributionInput } from '@/components/ContributionInput'

<ContributionInput
  storyId="story-123"
  contributionMode="word"
  wordsPerContribution={1}
  onContribute={async (text) => {
    await addContribution(storyId, text)
  }}
  placeholder="Add your word..."
  maxLength={50}
/>
```

### UserProfile

User profile display component.

**File:** `src/components/UserProfile.tsx`

#### Props

```typescript
interface UserProfileProps {
  user: {
    id: string
    username: string
    email?: string
    avatar_url?: string
    bio?: string
    created_at: string
    stats?: {
      stories_created: number
      contributions_made: number
      stories_participated: number
    }
  }
  isCurrentUser?: boolean
  onEdit?: () => void
  onFollow?: () => void
  className?: string
}
```

### Notifications

Real-time notification component.

**File:** `src/components/Notifications.tsx`

#### Props

```typescript
interface NotificationsProps {
  notifications: Array<{
    id: string
    type: 'story_update' | 'new_follower' | 'story_completed'
    title: string
    message: string
    created_at: string
    read: boolean
    action_url?: string
  }>
  onMarkAsRead: (id: string) => void
  onMarkAllAsRead: () => void
  onNotificationClick: (notification: Notification) => void
}
```

## Layout Components

### Header

Main application header with navigation.

**File:** `src/components/layout/Header.tsx`

#### Features

- Responsive navigation menu
- User authentication state
- Search functionality
- Dark mode toggle
- Mobile menu

### Sidebar

Collapsible sidebar for dashboard pages.

**File:** `src/components/layout/Sidebar.tsx`

#### Features

- Collapsible design
- Navigation items with icons
- User profile section
- Active state indication

### Footer

Application footer component.

**File:** `src/components/layout/Footer.tsx`

#### Features

- Links to important pages
- Social media links
- Copyright information
- Newsletter signup

## Form Components

### StoryCreationForm

Form for creating new stories.

**File:** `src/components/forms/StoryCreationForm.tsx`

#### Props

```typescript
interface StoryCreationFormProps {
  onSubmit: (data: StoryFormData) => Promise<void>
  initialData?: Partial<StoryFormData>
  isLoading?: boolean
}

interface StoryFormData {
  title: string
  description: string
  contribution_mode: 'word' | 'sentence' | 'paragraph'
  words_per_contribution: number
  max_contributions: number
  is_public: boolean
  tags: string[]
}
```

### UserSettingsForm

Form for updating user profile and settings.

**File:** `src/components/forms/UserSettingsForm.tsx`

## Component Best Practices

### Props and TypeScript

1. **Always define TypeScript interfaces** for component props
2. **Use optional props with default values** when appropriate
3. **Extend HTML element props** when creating wrapper components

```typescript
// Good: Extend existing HTML props
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary'
  loading?: boolean
}

// Good: Optional props with defaults
interface CardProps {
  title: string
  description?: string
  actions?: React.ReactNode
}

const Card: React.FC<CardProps> = ({ 
  title, 
  description, 
  actions = null 
}) => {
  // Component implementation
}
```

### State Management

1. **Use local state for component-specific data**
2. **Use React Query for server state**
3. **Use Context for global application state**

```typescript
// Local state for form inputs
const [title, setTitle] = useState('')

// Server state with React Query
const { data: stories, isLoading } = useQuery({
  queryKey: ['stories'],
  queryFn: fetchStories
})

// Global state with Context
const { user, isAuthenticated } = useAuth()
```

### Error Handling

1. **Use Error Boundaries** for component error handling
2. **Display user-friendly error messages**
3. **Provide fallback UI** for failed states

```typescript
const StoryCard: React.FC<StoryCardProps> = ({ story }) => {
  if (!story) {
    return (
      <Card className="p-6">
        <p className="text-muted-foreground">Story not found</p>
      </Card>
    )
  }

  return (
    <Card>
      {/* Story content */}
    </Card>
  )
}
```

### Accessibility

1. **Use semantic HTML elements**
2. **Include ARIA labels and roles**
3. **Ensure keyboard navigation**
4. **Maintain color contrast ratios**

```typescript
// Good: Semantic HTML with ARIA labels
<button
  aria-label="Join story"
  aria-describedby="story-description"
  onClick={handleJoin}
>
  Join
</button>

// Good: Proper heading hierarchy
<h1>Story Gallery</h1>
<h2>Featured Stories</h2>
<h3>Story Title</h3>
```

### Performance

1. **Use React.memo for expensive components**
2. **Implement proper key props in lists**
3. **Lazy load components when appropriate**

```typescript
// Memoize expensive components
const StoryCard = React.memo<StoryCardProps>(({ story, onJoin }) => {
  // Component implementation
})

// Proper keys in lists
{stories.map((story) => (
  <StoryCard key={story.id} story={story} />
))}

// Lazy loading
const StoryEditor = React.lazy(() => import('@/components/StoryEditor'))
```

### Testing

1. **Write unit tests for component logic**
2. **Test user interactions**
3. **Test accessibility features**

```typescript
// Component test example
describe('StoryCard', () => {
  const mockStory = {
    id: '1',
    title: 'Test Story',
    description: 'Test description',
    // ... other props
  }

  it('renders story information correctly', () => {
    render(<StoryCard story={mockStory} />)
    
    expect(screen.getByText('Test Story')).toBeInTheDocument()
    expect(screen.getByText('Test description')).toBeInTheDocument()
  })

  it('calls onJoin when join button is clicked', () => {
    const onJoin = jest.fn()
    render(<StoryCard story={mockStory} onJoin={onJoin} />)
    
    fireEvent.click(screen.getByText('Join'))
    expect(onJoin).toHaveBeenCalledWith('1')
  })
})
```

## Component Lifecycle

### Component Development Process

1. **Design Phase**
   - Define component purpose and API
   - Create wireframes or mockups
   - Identify reusable patterns

2. **Implementation Phase**
   - Create component with TypeScript interfaces
   - Implement core functionality
   - Add error handling and loading states

3. **Documentation Phase**
   - Create Storybook stories
   - Write component documentation
   - Add usage examples

4. **Testing Phase**
   - Write unit tests
   - Test accessibility features
   - Perform cross-browser testing

5. **Integration Phase**
   - Integrate with parent components
   - Test real-world usage
   - Optimize performance

### Component Maintenance

1. **Regular Reviews**
   - Check for unused props
   - Update dependencies
   - Refactor for better performance

2. **Version Updates**
   - Document breaking changes
   - Provide migration guides
   - Update Storybook examples

3. **Performance Monitoring**
   - Profile component render times
   - Monitor bundle size impact
   - Optimize re-renders

## Troubleshooting

### Common Issues

#### Component Not Rendering

**Problem:** Component appears blank or doesn't render
**Solution:** Check for:
- Missing return statement
- Conditional rendering logic
- CSS display properties
- Error in component tree

#### Props Not Updating

**Problem:** Component doesn't re-render when props change
**Solution:** Check for:
- Object/array references (use deep comparison)
- Missing dependencies in useEffect
- Incorrect memoization

#### Styling Issues

**Problem:** Styles not applying correctly
**Solution:** Check for:
- CSS class name conflicts
- Tailwind class ordering
- CSS specificity issues
- Missing CSS imports

#### Performance Issues

**Problem:** Component renders slowly or causes lag
**Solution:** Check for:
- Unnecessary re-renders
- Heavy computations in render
- Large lists without virtualization
- Memory leaks in useEffect

---

For more detailed information about specific components, refer to the Storybook documentation or the individual component files in the codebase.