openapi: 3.0.0
info:
  title: Word-by-Word Story API
  description: API for collaborative storytelling with multiple contribution modes
  version: 1.0.0
servers:
  - url: /api/v1
    description: Default API server
paths:
  /stories:
    post:
      summary: Create a new story
      description: Create a new collaborative story with specified settings
      tags:
        - Stories
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateStoryRequest'
      responses:
        '201':
          description: Story created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Story'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    get:
      summary: Get all stories
      description: Retrieve a list of all stories with optional filters
      tags:
        - Stories
      parameters:
        - name: status
          in: query
          description: Filter by story status
          schema:
            type: string
            enum: [DRAFT, ACTIVE, COMPLETED, PUBLISHED, ARCHIVED]
        - name: userId
          in: query
          description: Filter by creator user ID
          schema:
            type: string
        - name: page
          in: query
          description: Page number for pagination
          schema:
            type: integer
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: List of stories
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Story'
                  pagination:
                    type: object
                    properties:
                      total:
                        type: integer
                      page:
                        type: integer
                      limit:
                        type: integer
                      pages:
                        type: integer
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /stories/{storyId}:
    get:
      summary: Get a story by ID
      description: Retrieve a specific story by its ID
      tags:
        - Stories
      parameters:
        - name: storyId
          in: path
          required: true
          description: ID of the story to retrieve
          schema:
            type: string
      responses:
        '200':
          description: Story details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Story'
        '404':
          description: Story not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    patch:
      summary: Update a story
      description: Update a story's details
      tags:
        - Stories
      parameters:
        - name: storyId
          in: path
          required: true
          description: ID of the story to update
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateStoryRequest'
      responses:
        '200':
          description: Story updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Story'
        '404':
          description: Story not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /stories/{storyId}/contributions:
    get:
      summary: Get story with all contributions
      description: Retrieve a story with all its contributions
      tags:
        - Contributions
      parameters:
        - name: storyId
          in: path
          required: true
          description: ID of the story
          schema:
            type: string
      responses:
        '200':
          description: Story with contributions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StoryWithContributions'
        '404':
          description: Story not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    post:
      summary: Submit a contribution
      description: Add a new contribution to a story
      tags:
        - Contributions
      parameters:
        - name: storyId
          in: path
          required: true
          description: ID of the story
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateContributionRequest'
      responses:
        '201':
          description: Contribution added successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContributionResponse'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '403':
          description: Not your turn to contribute
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Story not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /stories/{storyId}/participants:
    post:
      summary: Join a story
      description: Add a user as a participant to a story
      tags:
        - Participants
      parameters:
        - name: storyId
          in: path
          required: true
          description: ID of the story
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - userId
              properties:
                userId:
                  type: string
                  description: ID of the user joining the story
      responses:
        '200':
          description: Successfully joined the story
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Story'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Story or user not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  
  /stories/{storyId}/sessions:
    get:
      summary: Get active user sessions
      description: Retrieve all active user sessions for a story
      tags:
        - Sessions
      parameters:
        - name: storyId
          in: path
          required: true
          description: ID of the story
          schema:
            type: string
      responses:
        '200':
          description: List of active user sessions
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserSession'
        '404':
          description: Story not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
    
    post:
      summary: Update user session status
      description: Update a user's session status (typing, online)
      tags:
        - Sessions
      parameters:
        - name: storyId
          in: path
          required: true
          description: ID of the story
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSessionRequest'
      responses:
        '200':
          description: Session updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserSession'
        '400':
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '404':
          description: Story or user not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

components:
  schemas:
    CreateStoryRequest:
      type: object
      required:
        - title
        - userId
      properties:
        title:
          type: string
          description: Title of the story
        description:
          type: string
          description: Description of the story
        contributionMode:
          type: string
          enum: [WORD, MULTI_WORD, SENTENCE, PARAGRAPH]
          default: WORD
          description: Mode of contribution for the story
        wordsPerContribution:
          type: integer
          description: Maximum number of words per contribution (for MULTI_WORD mode)
        userId:
          type: string
          description: ID of the user creating the story
    
    UpdateStoryRequest:
      type: object
      properties:
        title:
          type: string
          description: Title of the story
        description:
          type: string
          description: Description of the story
        status:
          type: string
          enum: [DRAFT, ACTIVE, COMPLETED, PUBLISHED, ARCHIVED]
          description: Status of the story
        contributionMode:
          type: string
          enum: [WORD, MULTI_WORD, SENTENCE, PARAGRAPH]
          description: Mode of contribution for the story
        wordsPerContribution:
          type: integer
          description: Maximum number of words per contribution (for MULTI_WORD mode)
        currentTurn:
          type: string
          description: User ID whose turn it is to contribute
    
    CreateContributionRequest:
      type: object
      required:
        - content
        - userId
      properties:
        content:
          type: string
          description: Content of the contribution
        userId:
          type: string
          description: ID of the contributing user
        type:
          type: string
          enum: [WORD, MULTI_WORD, SENTENCE, PARAGRAPH]
          description: Type of contribution
    
    UpdateSessionRequest:
      type: object
      required:
        - userId
      properties:
        userId:
          type: string
          description: ID of the user
        isTyping:
          type: boolean
          description: Whether the user is currently typing
        isOnline:
          type: boolean
          description: Whether the user is online
    
    Story:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the story
        title:
          type: string
          description: Title of the story
        description:
          type: string
          description: Description of the story
        created_by:
          type: string
          description: ID of the user who created the story
        creator:
          $ref: '#/components/schemas/User'
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
        status:
          type: string
          enum: [DRAFT, ACTIVE, COMPLETED, PUBLISHED, ARCHIVED]
          description: Status of the story
        participants:
          type: array
          items:
            $ref: '#/components/schemas/User'
        likes:
          type: integer
          description: Number of likes
        views:
          type: integer
          description: Number of views
        tags:
          type: array
          items:
            type: string
          description: Tags associated with the story
        is_published:
          type: boolean
          description: Whether the story is published
        votes:
          type: integer
          description: Number of votes
        word_count:
          type: integer
          description: Total word count
        current_turn:
          type: string
          description: User ID whose turn it is to contribute
        contribution_mode:
          type: string
          enum: [WORD, MULTI_WORD, SENTENCE, PARAGRAPH]
          description: Mode of contribution for the story
        words_per_contribution:
          type: integer
          description: Maximum number of words per contribution (for MULTI_WORD mode)
        last_nudged_at:
          type: string
          format: date-time
          description: Timestamp of the last nudge
    
    StoryWithContributions:
      allOf:
        - $ref: '#/components/schemas/Story'
        - type: object
          properties:
            contributions:
              type: array
              items:
                $ref: '#/components/schemas/Contribution'
    
    Contribution:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the contribution
        story_id:
          type: string
          description: ID of the story
        user_id:
          type: string
          description: ID of the contributing user
        user:
          $ref: '#/components/schemas/User'
        content:
          type: string
          description: Content of the contribution
        position:
          type: integer
          description: Position in the story
        type:
          type: string
          enum: [WORD, MULTI_WORD, SENTENCE, PARAGRAPH]
          description: Type of contribution
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
        metadata:
          type: object
          description: Additional metadata
    
    ContributionResponse:
      type: object
      properties:
        contribution:
          $ref: '#/components/schemas/Contribution'
        nextTurn:
          type: string
          description: User ID whose turn is next
        wordCount:
          type: integer
          description: Updated word count for the story
    
    UserSession:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the session
        user_id:
          type: string
          description: ID of the user
        user:
          $ref: '#/components/schemas/User'
        story_id:
          type: string
          description: ID of the story
        last_active:
          type: string
          format: date-time
          description: Timestamp of last activity
        is_typing:
          type: boolean
          description: Whether the user is currently typing
        is_online:
          type: boolean
          description: Whether the user is online
        created_at:
          type: string
          format: date-time
          description: Creation timestamp
        updated_at:
          type: string
          format: date-time
          description: Last update timestamp
    
    User:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the user
        username:
          type: string
          description: Username
        avatar_url:
          type: string
          description: URL to user's avatar
        is_admin:
          type: boolean
          description: Whether the user is an admin
        email:
          type: string
          description: User's email address
        tier:
          type: string
          enum: [free, microtier, wordsmith, storyteller, authors-guild, premium, pro]
          description: User's subscription tier
        credits:
          type: integer
          description: User's credit balance
    
    Error:
      type: object
      properties:
        error:
          type: object
          properties:
            code:
              type: string
              description: Error code
            message:
              type: string
              description: Error message
            details:
              type: string
              description: Additional error details
