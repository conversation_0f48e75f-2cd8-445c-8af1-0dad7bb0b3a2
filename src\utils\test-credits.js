// test-credits.js - Utility script to assign credits to test users and set up AI logging
// Run this script directly with Node.js to assign credits to a test user
import { safeJsonParse } from "../lib/utils";

// Mock implementation for browser environments
function assignTestUserCredits() {
  const TEST_EMAIL = "<EMAIL>";
  const CREDITS_AMOUNT = 100;

  // Look for user in local storage
  const userKey = "oneWordStoryUser";
  let user = localStorage.getItem(userKey);

  if (user) {
    try {
      user = safeJsonParse(user, null);

      if (!user) return false;

      // Check if this is our test user
      if (user.email === TEST_EMAIL) {
        console.log(
          `Assigning ${CREDITS_AMOUNT} credits to test user: ${TEST_EMAIL}`,
        );

        // Update credits
        user.credits = CREDITS_AMOUNT;

        // Save back to localStorage
        localStorage.setItem(userKey, JSON.stringify(user));

        console.log("Credits assigned successfully!");
        return true;
      } else {
        console.log(
          `Current user (${user.email}) is not the test user. No credits assigned.`,
        );
        return false;
      }
    } catch (error) {
      console.error("Error parsing user from localStorage:", error);
      return false;
    }
  } else {
    console.log("No user found in localStorage. Please log in first.");
    return false;
  }
}

// Function to create a test user with credits
// This would be used on the backend in a real application
function createTestUser() {
  const TEST_EMAIL = "<EMAIL>";
  const TEST_USER_ID = "test-user-123";
  const CREDITS_AMOUNT = 100;

  // Create a new test user
  const testUser = {
    id: TEST_USER_ID,
    email: TEST_EMAIL,
    username: "TestUser",
    // Either don't set a profile picture or use the correct path
    // profilePicture: '/placeholder.svg',
    tier: "free",
    credits: CREDITS_AMOUNT,
    achievements: {
      storiesCreated: 0,
      storiesParticipated: 0,
      wordsContributed: 0,
      votesReceived: 0,
      badges: [],
    },
  };

  // Save to local storage
  localStorage.setItem("oneWordStoryUser", JSON.stringify(testUser));
  localStorage.setItem("simulatedUser", JSON.stringify(testUser));

  console.log(`Created test user ${TEST_EMAIL} with ${CREDITS_AMOUNT} credits`);
  return testUser;
}

// Execute in browser context
if (typeof window !== "undefined") {
  // Auto-execute when imported
  setTimeout(() => {
    const success = assignTestUserCredits();
    if (!success) {
      // If user doesn't exist, create it
      createTestUser();
    }
  }, 1000); // Small delay to ensure DOM is loaded
}

// Export functions for direct use
export { assignTestUserCredits, createTestUser };
