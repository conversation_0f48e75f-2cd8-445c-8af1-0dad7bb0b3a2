import React from "react";
import { <PERSON> } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>,
  CardFooter,
} from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, Zap, Shield, Check, Star } from "lucide-react";

const PricingSection: React.FC = () => {
  const { isAuthenticated } = useAuth();

  return (
    <div className="bg-literary-paper py-16">
      <div className="container mx-auto">
        <h2 className="text-3xl font-serif font-bold text-center mb-4">
          Free to Write, Pay for What You Need
        </h2>
        <p className="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
          Start writing for free and enhance your experience with premium
          features
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5 text-literary-burgundy" />
                Core Features
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <p className="text-sm font-medium">Always Free:</p>
              <ul className="space-y-2">
                <li className="flex items-center text-sm">
                  <Check className="h-4 w-4 text-green-500 mr-2" />
                  Create and join stories
                </li>
                <li className="flex items-center text-sm">
                  <Check className="h-4 w-4 text-green-500 mr-2" />
                  Basic word contributions
                </li>
                <li className="flex items-center text-sm">
                  <Check className="h-4 w-4 text-green-500 mr-2" />
                  Collaborate with friends
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              <Link
                to={isAuthenticated ? "/dashboard" : "/register"}
                className="w-full"
              >
                <Button variant="outline" className="w-full">
                  Get Started Free
                </Button>
              </Link>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-literary-gold" />
                Premium Add-ons
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <p className="text-sm font-medium">Pay-as-you-go:</p>
              <ul className="space-y-2">
                <li className="flex items-center text-sm">
                  <Star className="h-4 w-4 text-literary-gold mr-2" />
                  AI word suggestions
                </li>
                <li className="flex items-center text-sm">
                  <Star className="h-4 w-4 text-literary-gold mr-2" />
                  Story cover art generation
                </li>
                <li className="flex items-center text-sm">
                  <Star className="h-4 w-4 text-literary-gold mr-2" />
                  Advanced writing tools
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              <Link to="/subscription#credit-options" className="w-full">
                <Button className="w-full bg-literary-gold hover:bg-literary-gold/90">
                  View Add-ons
                </Button>
              </Link>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-green-600" />
                Enhanced Experience
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-lg font-semibold text-green-700">
                  $5.99/mo
                </span>
              </div>
              <p className="text-sm font-medium">
                Includes 100 credits/mo &amp; ad-free experience
              </p>
              <ul className="space-y-2">
                <li className="flex items-center text-sm">
                  <Check className="h-4 w-4 text-green-500 mr-2" />
                  Ad-free experience
                </li>
                <li className="flex items-center text-sm">
                  <Check className="h-4 w-4 text-green-500 mr-2" />
                  100 credits deposited to your account each month
                </li>
                <li className="flex items-center text-sm">
                  <Check className="h-4 w-4 text-green-500 mr-2" />
                  Users can add extra credits anytime
                </li>
              </ul>
            </CardContent>
            <CardFooter>
              <Link to="/ad-free" className="w-full">
                <Button
                  variant="outline"
                  className="w-full border-green-200 hover:bg-green-50"
                >
                  Remove Ads
                </Button>
              </Link>
            </CardFooter>
          </Card>
        </div>

        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600">
            All purchases are protected by our 14-day money-back guarantee
          </p>
        </div>
      </div>
    </div>
  );
};

export default PricingSection;
