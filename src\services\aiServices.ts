// Adding AI credit costs
export const AI_CREDIT_COSTS = {
  gotchaWord: 1,
  wordSuggestion: 1,
  sentenceGeneration: 5,
  paragraphGeneration: 10,
  titleGeneration: 2,
  descriptionGeneration: 3,
  nudge: 1,
  coverArt: {
    basic: 5,
    premium: 10,
  },
};

// Import the AI logger
import aiLogger from "@/utils/ai-logger";

// DeepInfra API configuration
const DEEPINFRA_API_URL = "https://api.deepinfra.com/v1/inference";
// Ensure HTTPS is used to avoid CORS issues with HTTP->HTTPS redirects
const CORS_PROXY = "/api/ai-proxy"; // Use a relative URL to go through the express server
const DEEPINFRA_API_KEY =
  import.meta.env.VITE_DEEPINFRA_API_KEY || "ATpyHKztK8CFLeJwHn7IewW8ifIgHwWX";
const DEFAULT_MODEL = "meta-llama/Llama-3-8b-chat-hf";

// Helper function to get the current user ID
const getCurrentUserId = (): string => {
  try {
    const userStr = localStorage.getItem("oneWordStoryUser");
    if (userStr) {
      const user = JSON.parse(userStr);
      return user.id || "unknown-user";
    }
  } catch (error) {
    console.error("Error getting current user ID:", error);
  }
  return "unknown-user";
};

// Helper function to call DeepInfra API
const callDeepInfraAPI = async (
  prompt: string,
  model = DEFAULT_MODEL,
  maxTokens = 100,
): Promise<string> => {
  try {
    console.log(`Calling DeepInfra API with model: ${model}`);

    // Track API call start time for performance logging
    const startTime = Date.now();

    // Use the CORS proxy to avoid CORS issues
    const response = await fetch(CORS_PROXY, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${DEEPINFRA_API_KEY}`,
      },
      body: JSON.stringify({
        model,
        input: {
          prompt,
          max_tokens: maxTokens,
          temperature: 0.7,
        },
      }),
    });

    // Calculate API call duration
    const duration = Date.now() - startTime;

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`DeepInfra API error: ${response.status}`, errorText);

      // Log failed API call
      aiLogger.logOperation({
        type: "api-call-failed",
        userId: getCurrentUserId(),
        model,
        prompt,
        maxTokens,
        duration,
        error: `${response.status}: ${errorText}`,
        creditsUsed: 0,
        tokensUsed: aiLogger.estimateTokens(prompt),
      });

      throw new Error(`DeepInfra API error: ${response.status}`);
    }

    const data = await response.json();
    const result = data.output.text.trim();

    // Log successful API call
    aiLogger.logOperation({
      type: "api-call-success",
      userId: getCurrentUserId(),
      model,
      promptLength: prompt.length,
      resultLength: result.length,
      maxTokens,
      duration,
      creditsUsed: 0, // Will be updated by specific operation logs
      tokensUsed:
        data.output.stats?.tokens ||
        aiLogger.estimateTokens(prompt) + aiLogger.estimateTokens(result),
    });

    return result;
  } catch (error) {
    console.error("Error calling DeepInfra API:", error);
    throw new Error("Failed to generate AI content. Please try again later.");
  }
};

// Implementation for AI word suggestions
export const generateAiWordSuggestions = async (
  storyId: string,
  context: string,
): Promise<{ suggestions: string[]; creditsUsed: number }> => {
  console.log(
    `Generating AI word suggestions for story ${storyId} with context: ${context}`,
  );
  const creditsUsed = AI_CREDIT_COSTS.wordSuggestion;

  try {
    const prompt = `
You are an assistant helping with a collaborative story writing app where users contribute one word at a time.
Given the following context from a story, suggest 5 interesting words that could continue the story well.
The words should be varied in type (verbs, nouns, adjectives, adverbs) and tone.
Return ONLY a JSON array of 5 single words with no explanation or other text.

STORY CONTEXT:
${context}`;

    const result = await callDeepInfraAPI(prompt, DEFAULT_MODEL, 50);

    // Parse the result - handle both JSON format and plain text lists
    let suggestions: string[] = [];
    try {
      // Try to parse as JSON first
      suggestions = JSON.parse(result.replace(/```json|```/g, "").trim());
    } catch (err) {
      // If JSON parsing fails, try to extract words from text format
      const words = result.split(/[\s,\n]+/).filter((w) => w.length > 0);
      suggestions = words.slice(0, 5);
    }

    // Ensure we have exactly 5 suggestions and they're all single words
    suggestions = suggestions
      .filter((word) => word && word.trim().length > 0)
      .map((word) => word.trim().replace(/[^a-zA-Z'-]/g, ""))
      .slice(0, 5);

    // If we don't have enough suggestions, add some generic ones
    const fallbackSuggestions = [
      "mysterious",
      "quickly",
      "forever",
      "silently",
      "ancient",
    ];
    while (suggestions.length < 5) {
      suggestions.push(fallbackSuggestions[suggestions.length]);
    }

    // Log the word suggestion operation
    aiLogger.logWordSuggestion(
      getCurrentUserId(),
      storyId,
      context,
      suggestions,
      creditsUsed,
    );

    return { suggestions, creditsUsed };
  } catch (error) {
    console.error("Error generating word suggestions:", error);

    // Fallback to mock suggestions in case of API failure
    const suggestions = [
      "mysterious",
      "quickly",
      "forever",
      "silently",
      "ancient",
    ];

    // Log the failed operation
    aiLogger.logOperation({
      type: "word-suggestion-failed",
      userId: getCurrentUserId(),
      storyId,
      context,
      error: error instanceof Error ? error.message : "Unknown error",
      creditsUsed: 0, // No credits used on failure
    });

    return { suggestions, creditsUsed: 0 }; // No credits used for fallback
  }
};

// Real implementation of AI sentence generation
export interface SentenceResponse {
  sentences: string[];
  creditsUsed: number;
}

export const generateAiSentences = async (
  storyId: string,
  context: string,
): Promise<SentenceResponse> => {
  console.log(
    `Generating AI sentences for story ${storyId} with context: ${context}`,
  );
  const creditsUsed = AI_CREDIT_COSTS.sentenceGeneration;

  try {
    const prompt = `
You are an assistant for a collaborative storytelling app where users build stories together one contribution at a time.
Given the following story context, generate 5 compelling and varied sentences that could continue the narrative.
Each sentence should:
- Flow naturally from the existing story
- Have different tones and styles (mysterious, action-packed, emotional, descriptive, dialogue)
- Be complete sentences that advance the plot or develop characters
- Maintain narrative consistency with the established context
- Be engaging enough to inspire further contributions

STORY CONTEXT:
${context}

Return ONLY a JSON array of 5 sentences with no explanation or other text.
`;

    const result = await callDeepInfraAPI(prompt, DEFAULT_MODEL, 200);

    // Parse the result - handle both JSON format and plain text lists
    let sentences: string[] = [];
    try {
      // Try to parse as JSON first
      sentences = JSON.parse(result.replace(/```json|```/g, "").trim());
    } catch (err) {
      // If JSON parsing fails, try to extract sentences from text format
      const lines = result
        .split(/[.\n]+/)
        .filter((line) => line.trim().length > 10)
        .map((line) => line.trim() + (line.trim().endsWith('.') ? '' : '.'));
      sentences = lines.slice(0, 5);
    }

    // Ensure we have exactly 5 sentences
    sentences = sentences
      .filter((sentence) => sentence && sentence.trim().length > 10)
      .map((sentence) => sentence.trim())
      .slice(0, 5);

    // If we don't have enough sentences, add some contextual fallbacks
    const fallbackSentences = [
      "The mysterious figure disappeared into the fog without a trace.",
      "She quickly realized that the decision would change everything forever.",
      "As the sun set behind the mountains, a new journey was about to begin.",
      "He couldn't believe what he was seeing; it defied all explanation.",
      "The ancient book contained secrets that were never meant to be discovered.",
    ];

    while (sentences.length < 5) {
      sentences.push(fallbackSentences[sentences.length]);
    }

    // Log the sentence generation operation
    aiLogger.logOperation({
      type: "sentence-generation-success",
      userId: getCurrentUserId(),
      storyId,
      context: context.substring(0, 100) + "...", // Truncate for logging
      resultLength: sentences.join(" ").length,
      creditsUsed,
      tokensUsed: aiLogger.estimateTokens(prompt) + aiLogger.estimateTokens(sentences.join(" ")),
    });

    return { sentences, creditsUsed };
  } catch (error) {
    console.error("Error generating sentences:", error);

    // Fallback to contextually relevant sentences in case of API failure
    const sentences = [
      "The mysterious figure disappeared into the fog without a trace.",
      "She quickly realized that the decision would change everything forever.",
      "As the sun set behind the mountains, a new journey was about to begin.",
      "He couldn't believe what he was seeing; it defied all explanation.",
      "The ancient book contained secrets that were never meant to be discovered.",
    ];

    // Log the failed operation
    aiLogger.logOperation({
      type: "sentence-generation-failed",
      userId: getCurrentUserId(),
      storyId,
      context: context.substring(0, 100) + "...",
      error: error instanceof Error ? error.message : "Unknown error",
      creditsUsed: 0, // No credits used on failure
    });

    return { sentences, creditsUsed: 0 }; // No credits used for fallback
  }
};

// Real implementation of AI paragraph generation
export interface ParagraphResponse {
  paragraphs: string[];
  creditsUsed: number;
}

export const generateAiParagraphs = async (
  storyId: string,
  context: string,
): Promise<ParagraphResponse> => {
  console.log(
    `Generating AI paragraphs for story ${storyId} with context: ${context}`,
  );
  const creditsUsed = AI_CREDIT_COSTS.paragraphGeneration;

  try {
    const prompt = `
You are an assistant for a collaborative storytelling app where users build stories together one contribution at a time.
Given the following story context, generate 3 compelling and varied paragraphs that could continue the narrative.
Each paragraph should:
- Be 3-5 sentences long with rich, vivid descriptions
- Flow naturally from the existing story while introducing new elements
- Have different narrative focuses (character development, setting description, action/plot advancement)
- Maintain consistency with the established tone and style
- Include sensory details and emotional depth
- Be engaging enough to inspire further collaborative contributions

STORY CONTEXT:
${context}

Return ONLY a JSON array of 3 paragraphs with no explanation or other text.
`;

    const result = await callDeepInfraAPI(prompt, DEFAULT_MODEL, 400);

    // Parse the result - handle both JSON format and plain text
    let paragraphs: string[] = [];
    try {
      // Try to parse as JSON first
      paragraphs = JSON.parse(result.replace(/```json|```/g, "").trim());
    } catch (err) {
      // If JSON parsing fails, try to extract paragraphs from text format
      const sections = result
        .split(/\n\s*\n/)
        .filter((section) => section.trim().length > 50)
        .map((section) => section.trim());
      paragraphs = sections.slice(0, 3);
    }

    // Ensure we have exactly 3 paragraphs with sufficient content
    paragraphs = paragraphs
      .filter((paragraph) => paragraph && paragraph.trim().length > 50)
      .map((paragraph) => paragraph.trim())
      .slice(0, 3);

    // If we don't have enough paragraphs, add contextually rich fallbacks
    const fallbackParagraphs = [
      "The old house stood at the end of the lane, its windows dark and shutters hanging loose. Years of neglect had left it in a state of disrepair, yet there was something about it that called to her. Perhaps it was the rumors of hidden treasures within its walls, or maybe it was simply the challenge of bringing something forgotten back to life. The creaking of the front gate seemed to whisper secrets of the past.",

      "They traversed the dense forest with nothing but a compass and determination. The canopy above blocked most of the sunlight, creating an eerie twilight even at midday. Strange sounds echoed around them – some familiar, others completely alien. Every step forward was a victory against the wilderness, yet each footfall seemed to awaken something ancient in the shadows.",

      "The market square bustled with activity as vendors called out their wares. Colorful fabrics fluttered in the gentle breeze, while the scent of exotic spices and freshly baked bread filled the air. Children darted between stalls, their laughter adding to the symphony of sounds that defined this vibrant community. In the distance, church bells chimed the hour, reminding everyone that time, like the stories being woven around them, never truly stopped.",
    ];

    while (paragraphs.length < 3) {
      paragraphs.push(fallbackParagraphs[paragraphs.length]);
    }

    // Log the paragraph generation operation
    aiLogger.logOperation({
      type: "paragraph-generation-success",
      userId: getCurrentUserId(),
      storyId,
      context: context.substring(0, 100) + "...", // Truncate for logging
      resultLength: paragraphs.join(" ").length,
      creditsUsed,
      tokensUsed: aiLogger.estimateTokens(prompt) + aiLogger.estimateTokens(paragraphs.join(" ")),
    });

    return { paragraphs, creditsUsed };
  } catch (error) {
    console.error("Error generating paragraphs:", error);

    // Fallback to rich, contextual paragraphs in case of API failure
    const paragraphs = [
      "The old house stood at the end of the lane, its windows dark and shutters hanging loose. Years of neglect had left it in a state of disrepair, yet there was something about it that called to her. Perhaps it was the rumors of hidden treasures within its walls, or maybe it was simply the challenge of bringing something forgotten back to life.",

      "They traversed the dense forest with nothing but a compass and determination. The canopy above blocked most of the sunlight, creating an eerie twilight even at midday. Strange sounds echoed around them – some familiar, others completely alien. Every step forward was a victory against the wilderness.",

      "The market square bustled with activity as vendors called out their wares. Colorful fabrics fluttered in the gentle breeze, while the scent of exotic spices and freshly baked bread filled the air. Children darted between stalls, their laughter adding to the symphony of sounds that defined this vibrant community.",
    ];

    // Log the failed operation
    aiLogger.logOperation({
      type: "paragraph-generation-failed",
      userId: getCurrentUserId(),
      storyId,
      context: context.substring(0, 100) + "...",
      error: error instanceof Error ? error.message : "Unknown error",
      creditsUsed: 0, // No credits used on failure
    });

    return { paragraphs, creditsUsed: 0 }; // No credits used for fallback
  }
};

// Implementation for AI titles generation
export const generateAiTitles = async (
  userInput: string = "",
): Promise<{ titles: string[]; creditsUsed: number }> => {
  const creditsUsed = AI_CREDIT_COSTS.titleGeneration;

  try {
    // Build the prompt based on whether user has provided initial input
    const prompt = userInput.trim()
      ? `
You are an assistant for a collaborative storytelling app.
User has started typing this title: "${userInput.trim()}"

Generate 5 captivating and extremely engaging story titles based on this input.
The titles should expand on the user's idea, be varied in style, and have emotional impact.
Make the titles compelling enough to inspire contributors to join the story.
Return ONLY a JSON array of 5 titles with no explanation or other text.
`
      : `
You are an assistant for a collaborative storytelling app.
Generate 5 captivating and extremely engaging story titles that would attract writers to contribute.
Make the titles emotionally resonant and thought-provoking.
Include titles from different genres (fantasy, mystery, sci-fi, romance, adventure) that appeal to different audiences.
The titles should be original, creative, and make people want to write stories around them.
Return ONLY a JSON array of 5 titles with no explanation or other text.
`;

    const result = await callDeepInfraAPI(prompt, DEFAULT_MODEL, 120);

    // Parse the result - handle both JSON format and plain text lists
    let titles: string[] = [];
    try {
      // Try to parse as JSON first
      titles = JSON.parse(result.replace(/```json|```/g, "").trim());
    } catch (err) {
      // If JSON parsing fails, try to extract titles from text format
      const lines = result
        .split("\n")
        .filter((line) => line.trim().length > 0 && !line.includes(":"));
      titles = lines
        .slice(0, 5)
        .map((line) => line.replace(/^\d+[.)-]\s*/, "").trim());
    }

    // Ensure we have exactly 5 titles
    titles = titles
      .filter((title) => title && title.trim().length > 0)
      .map((title) => title.trim())
      .slice(0, 5);

    // If we don't have enough titles, add some generic ones
    const fallbackTitles = [
      "Shadows of Forgotten Memories",
      "The Last Whisper",
      "Beyond the Horizon",
      "Echoes in the Void",
      "The Unexpected Journey",
    ];

    while (titles.length < 5) {
      titles.push(fallbackTitles[titles.length]);
    }

    // If user provided input, make sure at least one title incorporates their input
    if (userInput.trim() && titles.length > 0) {
      let hasUserInput = false;

      for (const title of titles) {
        if (title.toLowerCase().includes(userInput.toLowerCase().trim())) {
          hasUserInput = true;
          break;
        }
      }

      if (!hasUserInput) {
        // Replace one title with a version that incorporates the user's input
        const userWords = userInput.trim().split(/\s+/);
        const mainWord = userWords[userWords.length - 1]; // Take the last word if multiple
        titles[0] = `The ${mainWord.charAt(0).toUpperCase() + mainWord.slice(1)}'s ${fallbackTitles[0].split(" ").slice(1).join(" ")}`;
      }
    }

    // Log the title generation operation
    aiLogger.logTitleGeneration(getCurrentUserId(), titles, creditsUsed);

    return { titles, creditsUsed };
  } catch (error) {
    console.error("Error generating titles:", error);

    // Fallback to mock titles in case of API failure
    const titles = [
      "Shadows of Forgotten Memories",
      "The Last Whisper",
      "Beyond the Horizon",
      "Echoes in the Void",
      "The Unexpected Journey",
    ];

    // Log the failed operation
    aiLogger.logOperation({
      type: "title-generation-failed",
      userId: getCurrentUserId(),
      error: error instanceof Error ? error.message : "Unknown error",
      creditsUsed: 0, // No credits used on failure
    });

    return { titles, creditsUsed: 0 }; // No credits used for fallback
  }
};

// Implementation for AI descriptions generation
export const generateAiDescriptions = async (
  title: string,
  userDescription: string = "",
): Promise<{ descriptions: string[]; creditsUsed: number }> => {
  console.log(
    `Generating AI descriptions based on title: ${title} and user input: ${userDescription}`,
  );
  const creditsUsed = AI_CREDIT_COSTS.descriptionGeneration;

  try {
    // Create a different prompt based on whether the user has provided any description text
    const prompt = userDescription.trim()
      ? `
You are an assistant for a collaborative storytelling app where users build stories together one contribution at a time.
The user has provided a story title: "${title}"
The user has started writing this description: "${userDescription.trim()}"

Please generate 3 captivating and compelling expanded story descriptions based on the title and the user's input.
Each description should be 2-4 sentences, emotionally engaging, and draw readers in with vivid imagery and intrigue.
The descriptions should have different tones and styles (e.g., mysterious, adventurous, reflective).
Include hooks that make readers want to contribute to this collaborative story.
Make sure to incorporate elements from the user's initial description and expand them with creative details.
Each description should include the title in quotes.

Return ONLY a JSON array of 3 story descriptions with no explanation or other text.
`
      : `
You are an assistant for a collaborative storytelling app where users build stories together one contribution at a time.
Generate 3 captivating and compelling story descriptions based on this title: "${title}"

Each description should:
- Be 2-4 sentences long with vivid imagery and emotional hooks
- Have different tones and styles (e.g., mysterious, adventurous, reflective)
- Include specific, intriguing story elements that spark imagination
- Hint at possible plot directions without being too prescriptive
- Include the title in quotes
- End with a subtle invitation for others to contribute

The descriptions should be compelling enough that readers will be excited to contribute to the narrative.
Return ONLY a JSON array of 3 descriptions with no explanation or other text.
`;

    const result = await callDeepInfraAPI(prompt, DEFAULT_MODEL, 350);

    // Parse the result - handle both JSON format and plain text
    let descriptions: string[] = [];
    try {
      // Try to parse as JSON first
      descriptions = JSON.parse(result.replace(/```json|```/g, "").trim());
    } catch (err) {
      // If JSON parsing fails, try to extract descriptions from text format
      const lines = result
        .split("\n\n")
        .filter((line) => line.trim().length > 0);
      descriptions = lines
        .slice(0, 3)
        .map((line) => line.replace(/^\d+[.)-]\s*/, "").trim());
    }

    // Ensure we have exactly 3 descriptions
    descriptions = descriptions
      .filter((desc) => desc && desc.trim().length > 0)
      .map((desc) => desc.trim())
      .slice(0, 3);

    // Make sure each description includes the title in quotes if it doesn't already
    descriptions = descriptions.map((desc) => {
      if (!desc.includes(`"${title}"`) && !desc.includes(`'${title}'`)) {
        return `${desc} "${title}" is waiting for your contribution.`;
      }
      return desc;
    });

    // If we don't have enough descriptions, add some generic ones
    const fallbackDescriptions = [
      `A captivating tale that explores the boundaries between reality and imagination. "${title}" takes readers on an unforgettable journey through landscapes of wonder and peril. What secrets will be uncovered as the story unfolds?`,
      `In the world of "${title}", nothing is quite as it seems. This collaborative story weaves together multiple perspectives into a tapestry of intrigue and discovery. Every word brings us closer to the truth hiding beneath the surface.`,
      `"${title}" invites contributors to build a narrative that challenges conventions and explores the depths of human experience. Join us in crafting a story that will resonate across time and space, echoing with emotional truth.`,
    ];

    while (descriptions.length < 3) {
      descriptions.push(fallbackDescriptions[descriptions.length]);
    }

    // If user provided input, make sure we've incorporated it
    if (userDescription.trim() && descriptions.length > 0) {
      // Extract key terms from user input
      const userTerms = userDescription
        .trim()
        .toLowerCase()
        .replace(/[^\w\s]/g, "")
        .split(/\s+/)
        .filter(
          (word) =>
            word.length > 3 &&
            ![
              "this",
              "that",
              "with",
              "from",
              "have",
              "what",
              "where",
              "when",
            ].includes(word),
        );

      if (userTerms.length > 0) {
        // Check if any description contains any of the key terms
        let hasUserTerms = false;
        for (const desc of descriptions) {
          for (const term of userTerms) {
            if (desc.toLowerCase().includes(term)) {
              hasUserTerms = true;
              break;
            }
          }
          if (hasUserTerms) break;
        }

        // If no description contains user terms, modify the first one
        if (!hasUserTerms && userTerms.length > 0) {
          const term = userTerms[Math.floor(Math.random() * userTerms.length)];
          descriptions[0] = `As ${term} emerges as a central theme, "${title}" weaves a tale of extraordinary circumstances and unexpected connections. ${descriptions[0].split(". ").slice(1).join(". ")}`;
        }
      }
    }

    // Log the description generation operation
    aiLogger.logDescriptionGeneration(
      getCurrentUserId(),
      title,
      descriptions,
      creditsUsed,
    );

    return { descriptions, creditsUsed };
  } catch (error) {
    console.error("Error generating descriptions:", error);

    // Fallback to mock descriptions in case of API failure
    const descriptions = [
      `A captivating tale that explores the boundaries between reality and imagination. "${title}" takes readers on an unforgettable journey through landscapes of wonder and peril.`,
      `In the world of "${title}", nothing is quite as it seems. This collaborative story weaves together multiple perspectives into a tapestry of intrigue and discovery.`,
      `"${title}" invites contributors to build a narrative that challenges conventions and explores the depths of human experience. Join us in crafting a story that will resonate for generations.`,
    ];

    // Log the failed operation
    aiLogger.logOperation({
      type: "description-generation-failed",
      userId: getCurrentUserId(),
      title,
      error: error instanceof Error ? error.message : "Unknown error",
      creditsUsed: 0, // No credits used on failure
    });

    return { descriptions, creditsUsed: 0 }; // No credits used for fallback
  }
};
