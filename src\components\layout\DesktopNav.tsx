import React from "react";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts/auth";

const DesktopNav = () => {
  const { isAuthenticated, isAdmin } = useAuth();
  const location = useLocation();

  return (
    <nav className="hidden md:flex items-center gap-6">
      {[
        { path: "/", label: "Home" },
        { path: "/gallery", label: "Gallery" },
        { path: "/pricing", label: "Pricing" },
        ...(isAuthenticated
          ? [
              { path: "/dashboard", label: "Dashboard" },
              { path: "/create-story", label: "Create Story" },
            ]
          : []),
        ...(isAdmin() ? [{ path: "/admin", label: "Admin Panel" }] : []),
      ].map((item) => (
        <Link
          key={item.path}
          to={item.path}
          className={`
            relative group transition-colors 
            ${
              location.pathname === item.path
                ? "text-literary-gold"
                : "text-white/80 hover:text-literary-gold"
            }
          `}
        >
          <span className="relative">
            {item.label}
            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-literary-gold transition-all duration-300 group-hover:w-full"></span>
          </span>
        </Link>
      ))}
    </nav>
  );
};

export default DesktopNav;
