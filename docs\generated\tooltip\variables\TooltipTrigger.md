[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [tooltip](../README.md) / TooltipTrigger

# Variable: TooltipTrigger

> `const` **TooltipTrigger**: `ForwardRefExoticComponent`\<`TooltipTriggerProps` & `RefAttributes`\<`HTMLButtonElement`\>\> = `TooltipPrimitive.Trigger`

Defined in: [src/components/ui/tooltip.tsx:10](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/tooltip.tsx#L10)
