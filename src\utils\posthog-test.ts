/**
 * PostHog Integration Test Utilities
 * 
 * These utilities help verify that PostHog is properly configured and tracking events.
 * Use these functions in development to test the analytics integration.
 */

import { posthog, analytics, trackStoryEvent, trackUserEvent, trackSubscriptionEvent, trackEngagementEvent } from '../lib/posthog';

export interface PostHogTestResult {
  test: string;
  success: boolean;
  error?: string;
  data?: any;
}

/**
 * Test basic PostHog initialization
 */
export const testPostHogInit = (): PostHogTestResult => {
  try {
    if (typeof window === 'undefined') {
      return {
        test: 'PostHog Initialization',
        success: false,
        error: 'Not running in browser environment',
      };
    }

    if (!posthog || typeof posthog.capture !== 'function') {
      return {
        test: 'PostHog Initialization',
        success: false,
        error: 'PostHog not properly initialized',
      };
    }

    return {
      test: 'PostHog Initialization',
      success: true,
      data: {
        hasCapture: typeof posthog.capture === 'function',
        hasIdentify: typeof posthog.identify === 'function',
        hasReset: typeof posthog.reset === 'function',
      },
    };
  } catch (error) {
    return {
      test: 'PostHog Initialization',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * Test analytics utility functions
 */
export const testAnalyticsUtils = (): PostHogTestResult => {
  try {
    const tests = {
      hasTrack: typeof analytics.track === 'function',
      hasIdentify: typeof analytics.identify === 'function',
      hasReset: typeof analytics.reset === 'function',
      hasSetUserProperties: typeof analytics.setUserProperties === 'function',
      hasPageView: typeof analytics.pageView === 'function',
      hasIsFeatureEnabled: typeof analytics.isFeatureEnabled === 'function',
    };

    const allPassed = Object.values(tests).every(Boolean);

    return {
      test: 'Analytics Utility Functions',
      success: allPassed,
      data: tests,
      error: allPassed ? undefined : 'Some analytics functions are missing',
    };
  } catch (error) {
    return {
      test: 'Analytics Utility Functions',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * Test story event tracking functions
 */
export const testStoryEventTracking = (): PostHogTestResult => {
  try {
    const tests = {
      hasStoryCreated: typeof trackStoryEvent.storyCreated === 'function',
      hasStoryContribution: typeof trackStoryEvent.storyContribution === 'function',
      hasStoryCompleted: typeof trackStoryEvent.storyCompleted === 'function',
      hasStoryViewed: typeof trackStoryEvent.storyViewed === 'function',
      hasStoryVoted: typeof trackStoryEvent.storyVoted === 'function',
    };

    const allPassed = Object.values(tests).every(Boolean);

    return {
      test: 'Story Event Tracking Functions',
      success: allPassed,
      data: tests,
      error: allPassed ? undefined : 'Some story tracking functions are missing',
    };
  } catch (error) {
    return {
      test: 'Story Event Tracking Functions',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * Test user event tracking functions
 */
export const testUserEventTracking = (): PostHogTestResult => {
  try {
    const tests = {
      hasUserRegistered: typeof trackUserEvent.userRegistered === 'function',
      hasUserLoggedIn: typeof trackUserEvent.userLoggedIn === 'function',
      hasUserLoggedOut: typeof trackUserEvent.userLoggedOut === 'function',
    };

    const allPassed = Object.values(tests).every(Boolean);

    return {
      test: 'User Event Tracking Functions',
      success: allPassed,
      data: tests,
      error: allPassed ? undefined : 'Some user tracking functions are missing',
    };
  } catch (error) {
    return {
      test: 'User Event Tracking Functions',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * Test subscription event tracking functions
 */
export const testSubscriptionEventTracking = (): PostHogTestResult => {
  try {
    const tests = {
      hasSubscriptionStarted: typeof trackSubscriptionEvent.subscriptionStarted === 'function',
      hasSubscriptionCancelled: typeof trackSubscriptionEvent.subscriptionCancelled === 'function',
      hasPaymentSuccessful: typeof trackSubscriptionEvent.paymentSuccessful === 'function',
      hasPaymentFailed: typeof trackSubscriptionEvent.paymentFailed === 'function',
    };

    const allPassed = Object.values(tests).every(Boolean);

    return {
      test: 'Subscription Event Tracking Functions',
      success: allPassed,
      data: tests,
      error: allPassed ? undefined : 'Some subscription tracking functions are missing',
    };
  } catch (error) {
    return {
      test: 'Subscription Event Tracking Functions',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * Test engagement event tracking functions
 */
export const testEngagementEventTracking = (): PostHogTestResult => {
  try {
    const tests = {
      hasFeatureUsed: typeof trackEngagementEvent.featureUsed === 'function',
      hasTimeSpent: typeof trackEngagementEvent.timeSpent === 'function',
      hasSearchPerformed: typeof trackEngagementEvent.searchPerformed === 'function',
      hasErrorEncountered: typeof trackEngagementEvent.errorEncountered === 'function',
    };

    const allPassed = Object.values(tests).every(Boolean);

    return {
      test: 'Engagement Event Tracking Functions',
      success: allPassed,
      data: tests,
      error: allPassed ? undefined : 'Some engagement tracking functions are missing',
    };
  } catch (error) {
    return {
      test: 'Engagement Event Tracking Functions',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * Test environment variables
 */
export const testEnvironmentConfig = (): PostHogTestResult => {
  try {
    const config = {
      hasPostHogKey: Boolean(import.meta.env.VITE_POSTHOG_KEY),
      hasPostHogHost: Boolean(import.meta.env.VITE_POSTHOG_HOST),
      disableInDev: import.meta.env.VITE_POSTHOG_DISABLE_IN_DEV,
      isDevelopment: import.meta.env.DEV,
    };

    const isProperlyConfigured = config.hasPostHogKey && config.hasPostHogHost;

    return {
      test: 'Environment Configuration',
      success: isProperlyConfigured,
      data: {
        ...config,
        postHogKey: config.hasPostHogKey ? 'Set (hidden)' : 'Not set',
        postHogHost: import.meta.env.VITE_POSTHOG_HOST || 'Not set',
      },
      error: isProperlyConfigured ? undefined : 'PostHog environment variables not properly configured',
    };
  } catch (error) {
    return {
      test: 'Environment Configuration',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * Send test events to verify tracking is working
 */
export const sendTestEvents = async (): Promise<PostHogTestResult[]> => {
  const results: PostHogTestResult[] = [];

  try {
    // Test basic tracking
    analytics.track('test_event', {
      test_type: 'basic_tracking',
      timestamp: new Date().toISOString(),
    });
    results.push({
      test: 'Basic Event Tracking',
      success: true,
    });
  } catch (error) {
    results.push({
      test: 'Basic Event Tracking',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }

  try {
    // Test story event
    trackStoryEvent.storyViewed('test-story-id', {
      test_mode: true,
      story_title: 'Test Story',
    });
    results.push({
      test: 'Story Event Tracking',
      success: true,
    });
  } catch (error) {
    results.push({
      test: 'Story Event Tracking',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }

  try {
    // Test engagement event
    trackEngagementEvent.featureUsed('test_feature', {
      test_mode: true,
    });
    results.push({
      test: 'Engagement Event Tracking',
      success: true,
    });
  } catch (error) {
    results.push({
      test: 'Engagement Event Tracking',
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }

  return results;
};

/**
 * Run all PostHog integration tests
 */
export const runAllPostHogTests = async (): Promise<{
  results: PostHogTestResult[];
  summary: {
    total: number;
    passed: number;
    failed: number;
    successRate: number;
  };
}> => {
  const results: PostHogTestResult[] = [];

  // Run all tests
  results.push(testPostHogInit());
  results.push(testAnalyticsUtils());
  results.push(testStoryEventTracking());
  results.push(testUserEventTracking());
  results.push(testSubscriptionEventTracking());
  results.push(testEngagementEventTracking());
  results.push(testEnvironmentConfig());

  // Run test events if basic setup is working
  const basicTest = results.find(r => r.test === 'PostHog Initialization');
  if (basicTest && basicTest.success) {
    const eventTests = await sendTestEvents();
    results.push(...eventTests);
  }

  // Calculate summary
  const total = results.length;
  const passed = results.filter(r => r.success).length;
  const failed = total - passed;
  const successRate = total > 0 ? (passed / total) * 100 : 0;

  return {
    results,
    summary: {
      total,
      passed,
      failed,
      successRate,
    },
  };
};

/**
 * Console-friendly test runner for development
 */
export const runPostHogDiagnostics = async () => {
  console.group('🔬 PostHog Integration Diagnostics');
  
  const { results, summary } = await runAllPostHogTests();
  
  console.log(`📊 Test Summary: ${summary.passed}/${summary.total} passed (${summary.successRate.toFixed(1)}%)`);
  console.log('');
  
  results.forEach(result => {
    const icon = result.success ? '✅' : '❌';
    console.log(`${icon} ${result.test}`);
    
    if (result.error) {
      console.error(`   Error: ${result.error}`);
    }
    
    if (result.data) {
      console.log(`   Data:`, result.data);
    }
  });
  
  console.log('');
  
  if (summary.successRate === 100) {
    console.log('🎉 All tests passed! PostHog integration is working correctly.');
  } else if (summary.successRate >= 80) {
    console.log('⚠️  Most tests passed, but there are some issues to address.');
  } else {
    console.log('🚨 Many tests failed. PostHog integration needs attention.');
  }
  
  console.groupEnd();
  
  return { results, summary };
};

// Development helper: run diagnostics automatically in dev mode
if (import.meta.env.DEV && typeof window !== 'undefined') {
  // Add to window for easy access in dev tools
  (window as any).posthogDiagnostics = runPostHogDiagnostics;
  console.log('🔧 PostHog diagnostics available: window.posthogDiagnostics()');
}