// Common types used across the application

export interface User {
  id: string;
  username?: string; // Make username optional to match auth/types
  email: string;
  profilePicture?: string;
  credits?: number;
  tier?:
    | "free"
    | "microtier"
    | "wordsmith"
    | "storyteller"
    | "authors-guild"
    | "premium"
    | "pro";
}

export interface ClickObserver {
  recordClick: (type: string, value: string) => void;
}

export interface Session {
  token: string;
  expiresAt: number;
}

// Type for stories
export interface Story {
  id: string;
  title: string;
  description: string;
  createdBy: User;
  createdAt: string;
  updatedAt: string;
  status: string;
  participants: User[];
  contributions: Contribution[];
  likes: number;
  views: number;
  tags: string[];
  isPublished: boolean;
  votes: number; // Added votes property
  words: Word[]; // Added words property
  wordCount: number;
  typingInfo?: TypingInfo;
  lastNudged?: LastNudged;
  currentTurn: string;
  contributionMode: StoryContributionMode;
  wordsPerContribution?: number;
}

export interface Word {
  id: string;
  word: string;
  userId: string;
  createdAt: string;
}

export interface TypingInfo {
  userId: string;
  content: string;
  timestamp: string;
}

export interface LastNudged {
  nudgedBy: string;
  timestamp: string;
}

export interface Contribution {
  id: string;
  userId: string;
  username: string;
  word: string;
  createdAt: string;
}

// Adding ContributionMode enum for CreateStory
export enum ContributionMode {
  PUBLIC = "public",
  PRIVATE = "private",
  INVITE_ONLY = "invite_only",
}

// Contribution modes for different story types
export type StoryContributionMode = "word" | "words" | "sentence" | "paragraph";

// AI response types
export interface AIWordSuggestionResponse {
  suggestions: string[];
  creditsUsed: number;
}

export interface AISentenceResponse {
  sentences: string[];
  creditsUsed: number;
}

export interface AIParagraphResponse {
  paragraphs: string[];
  creditsUsed: number;
}

// Props for AiWordSuggestions component
export interface AiWordSuggestionsProps {
  storyId?: string;
  currentWord?: string;
  words?: Word[];
  onSuggestionClick?: (word: string) => void;
  onSelectWord?: (word: string) => void;
  observer?: ClickObserver;
}

// Props for AiContributionSuggestions component
export interface AiContributionSuggestionsProps {
  storyId: string;
  mode: "sentence" | "paragraph";
  words: Word[];
  onSelectContribution: (contribution: string) => void;
}
