-- supabase/migrations/0006_create_subscriptions_table.sql

-- Attempt to drop the table first to ensure a clean slate for this definition
DROP TABLE IF EXISTS public.subscriptions CASCADE;

-- Now create it with your specific schema
CREATE TABLE public.subscriptions (
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    user_id uuid NOT NULL,
    stripe_customer_id text NULL,
    stripe_subscription_id text NULL,
    status text NULL, -- e.g., active, trialing, past_due, canceled, unpaid
    current_period_start timestamptz NULL,
    current_period_end timestamptz NULL,
    price_id text NULL, -- Stripe Price ID (e.g., price_xxxxxxxx)
    cancel_at_period_end boolean NULL DEFAULT false,
    canceled_at timestamptz NULL,
    ended_at timestamptz NULL,
    trial_start timestamptz NULL,
    trial_end timestamptz NULL,
    created_at timestamptz NOT NULL DEFAULT now(),
    updated_at timestamptz NOT NULL DEFAULT now(),
    CONSTRAINT subscriptions_pkey PRIMARY KEY (id),
    CONSTRAINT subscriptions_stripe_subscription_id_key UNIQUE (stripe_subscription_id),
    CONSTRAINT subscriptions_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE
);

-- Optional: Add an index on user_id for faster lookups
CREATE INDEX idx_subscriptions_user_id ON public.subscriptions USING btree (user_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;

-- Policy: Users can view their own subscriptions.
CREATE POLICY "Users can view their own subscriptions"
ON public.subscriptions
FOR SELECT USING (auth.uid() = user_id);

-- Policy: Allow service_role to perform all actions (needed for Edge Functions)
-- CREATE POLICY "Allow service_role full access on subscriptions" 
-- ON public.subscriptions FOR ALL USING (true) WITH CHECK (true); 
-- Note: Service role bypasses RLS by default, so this might not be strictly needed
-- but can be explicit if desired or if RLS is ever enabled for service_role.

-- Add comments to table and columns
COMMENT ON TABLE public.subscriptions IS 'Stores user subscription data synced from Stripe.';
COMMENT ON COLUMN public.subscriptions.user_id IS 'FK to auth.users.id';
COMMENT ON COLUMN public.subscriptions.stripe_customer_id IS 'Stripe Customer ID';
COMMENT ON COLUMN public.subscriptions.stripe_subscription_id IS 'Stripe Subscription ID (unique)';
COMMENT ON COLUMN public.subscriptions.status IS 'Subscription status from Stripe (active, trialing, etc.)';
COMMENT ON COLUMN public.subscriptions.current_period_start IS 'Start of the current billing period';
COMMENT ON COLUMN public.subscriptions.current_period_end IS 'End of the current billing period';
COMMENT ON COLUMN public.subscriptions.price_id IS 'Stripe Price ID for the subscribed plan';
COMMENT ON COLUMN public.subscriptions.cancel_at_period_end IS 'True if subscription is set to cancel at period end';
COMMENT ON COLUMN public.subscriptions.canceled_at IS 'Timestamp when the subscription was canceled immediately';
COMMENT ON COLUMN public.subscriptions.ended_at IS 'Timestamp when the subscription actually ended';
COMMENT ON COLUMN public.subscriptions.trial_start IS 'Timestamp for trial start';
COMMENT ON COLUMN public.subscriptions.trial_end IS 'Timestamp for trial end';

-- Trigger to update "updated_at" timestamp on row update
CREATE OR REPLACE FUNCTION public.handle_subscription_updated_at() -- Renamed to be specific
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER on_subscriptions_update
BEFORE UPDATE ON public.subscriptions
FOR EACH ROW
EXECUTE FUNCTION public.handle_subscription_updated_at(); -- Use specific function name