-- Migration: Add notifications table and RLS policies

CREATE TABLE public.notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE, -- Recipient
    story_id UUID REFERENCES public.stories(id) ON DELETE CASCADE,      -- Related story (optional)
    contribution_id UUID REFERENCES public.contributions(id) ON DELETE CASCADE, -- Related contribution (optional)
    actor_id UUID REFERENCES auth.users(id) ON DELETE CASCADE, -- User who performed the action (optional)
    type TEXT NOT NULL, -- e.g., 'new_contribution', 'story_completed', 'mention'
    content JSONB NOT NULL, -- Can store structured data: { message: "UserX added to Story<PERSON>", actor_username: "<PERSON><PERSON><PERSON>", story_title: "Story Title" }
    is_read BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    read_at TIMESTAMPTZ
);
COMMENT ON TABLE public.notifications IS 'Stores user notifications for various events.';

CREATE INDEX idx_notifications_user_id_is_read ON public.notifications(user_id, is_read);
CREATE INDEX idx_notifications_user_id_created_at ON public.notifications(user_id, created_at DESC);

ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read their own notifications"
ON public.notifications FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own notifications (mark as read/unread)"
ON public.notifications FOR UPDATE USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id AND (is_read = TRUE OR is_read = FALSE) AND (read_at IS NULL OR read_at IS NOT NULL)); -- Allow updating is_read and read_at

-- Function to handle new contribution notifications
CREATE OR REPLACE FUNCTION public.handle_new_contribution_notification()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public -- Important for SECURITY DEFINER functions
AS $$
DECLARE
    v_story_author_id UUID;
    v_actor_username TEXT;
    v_story_title TEXT;
    recipient RECORD;
BEGIN
    -- Get story author
    SELECT author_id, title INTO v_story_author_id, v_story_title
    FROM public.stories WHERE id = NEW.story_id;

    -- Get actor's username (assuming a public.users or similar table, or from auth.users directly if possible)
    -- For simplicity, we might need to adjust how username is fetched or pass it if available
    -- Supabase auth.users is not directly queryable in PL/pgSQL in the same way as public tables without specific setup.
    -- Let's assume there is a way to get this, or it might be part of a user_profiles table linked to auth.users.
    -- For now, let's try getting it from auth.users raw_user_meta_data, which is tricky in PL/pgSQL.
    -- A simpler approach for PG functions is to have a `user_profiles` table that mirrors auth.users and includes username.
    -- As a placeholder, we will use the actor's ID if username is not easily retrievable here.
    SELECT COALESCE(u.raw_user_meta_data->>'username', u.email, NEW.user_id::TEXT) INTO v_actor_username
    FROM auth.users u WHERE u.id = NEW.user_id;

    -- Notify story author (if not the one who made the contribution)
    IF v_story_author_id IS NOT NULL AND v_story_author_id <> NEW.user_id THEN
        INSERT INTO public.notifications (user_id, story_id, contribution_id, actor_id, type, content)
        VALUES (v_story_author_id, NEW.story_id, NEW.id, NEW.user_id, 'new_contribution',
                jsonb_build_object(
                    'message', v_actor_username || ' added a new part to your story \"' || v_story_title || '\".',
                    'actor_username', v_actor_username,
                    'story_title', v_story_title
                ));
    END IF;

    -- Notify other contributors (excluding the actor and the story author if already notified)
    FOR recipient IN 
        SELECT DISTINCT c.user_id 
        FROM public.contributions c 
        WHERE c.story_id = NEW.story_id 
          AND c.user_id <> NEW.user_id 
          AND c.user_id <> v_story_author_id -- Avoid double notifying author if they also contributed
    LOOP
        INSERT INTO public.notifications (user_id, story_id, contribution_id, actor_id, type, content)
        VALUES (recipient.user_id, NEW.story_id, NEW.id, NEW.user_id, 'new_contribution',
                jsonb_build_object(
                    'message', v_actor_username || ' added a new part to \"' || v_story_title || '\", a story you contributed to.',
                    'actor_username', v_actor_username,
                    'story_title', v_story_title
                ));
    END LOOP;

    RETURN NEW;
END;
$$;

-- Trigger to execute the function after a new contribution
CREATE TRIGGER trigger_new_contribution_notification
AFTER INSERT ON public.contributions
FOR EACH ROW EXECUTE FUNCTION public.handle_new_contribution_notification(); 