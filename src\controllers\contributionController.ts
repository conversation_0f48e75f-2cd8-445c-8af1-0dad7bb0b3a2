import { Request, Response } from 'express';
import { DatabaseAdapter } from '@/lib/db-adapter';
import { ContributionMode, RealTimeService } from '@/services/realTimeService';
import { ContributionValidator } from '@/services/contributionValidator';

// Get the RealTimeService instance
const realTimeService = RealTimeService.getInstance();

/**
 * Controller for handling story creation and contributions
 */
export class ContributionController {
  /**
   * Create a new story
   * @param req Express request
   * @param res Express response
   */
  static async createStory(req: Request, res: Response) {
    try {
      const { title, description, contributionMode, wordsPerContribution, userId, username } = req.body;
      
      if (!title || !userId) {
        return res.status(400).json({ error: "Title and userId are required" });
      }
      
      const storyData = {
        title,
        description,
        created_by: userId,
        contribution_mode: contributionMode || ContributionMode.WORD,
        words_per_contribution: wordsPerContribution,
        participants: [userId],
        current_turn: userId, // Initially, creator's turn
      };
      
      const result = await DatabaseAdapter.createStory(storyData);
      
      if (result.error) {
        return res.status(500).json({ error: result.error.message });
      }
      
      // Register the creator as the first participant in the real-time service
      const storyId = result.data.id;
      realTimeService.registerUserSession(storyId, userId, username);
      
      res.status(201).json(result.data);
    } catch (error) {
      console.error('Error creating story:', error);
      res.status(500).json({ error: 'Failed to create story' });
    }
  }

  /**
   * Get a story by ID with all contributions
   * @param req Express request
   * @param res Express response
   */
  static async getStory(req: Request, res: Response) {
    try {
      const { storyId } = req.params;
      
      if (!storyId) {
        return res.status(400).json({ error: "Story ID is required" });
      }
      
      const result = await DatabaseAdapter.getStoryWithContributions(storyId);
      
      if (result.error) {
        return res.status(404).json({ error: result.error.message });
      }
      
      // Get active user sessions from real-time service
      try {
        const activeSessions = await realTimeService.getActiveUserSessions(storyId);
        // Add active sessions to the response
        result.data.activeSessions = activeSessions;
      } catch (sessionError) {
        console.error("Error getting active sessions:", sessionError);
        // Continue even if we can't get active sessions
      }
      
      res.status(200).json(result.data);
    } catch (error) {
      console.error('Error getting story:', error);
      res.status(500).json({ error: 'Failed to get story' });
    }
  }

  /**
   * Submit a contribution to a story
   * @param req Express request
   * @param res Express response
   */
  static async submitContribution(req: Request, res: Response) {
    try {
      const { storyId } = req.params;
      const { content, userId, mode, username } = req.body;
      
      if (!storyId || !content || !userId) {
        return res.status(400).json({ error: "Story ID, content, and userId are required" });
      }
      
      // Get story to check if it's user's turn and determine position
      const storyResult = await DatabaseAdapter.getStory(storyId);
      
      if (storyResult.error) {
        return res.status(404).json({ error: "Story not found" });
      }
      
      const story = storyResult.data;
      
      // Check if it's the user's turn
      if (story.current_turn !== userId) {
        return res.status(403).json({ error: "It's not your turn to contribute" });
      }
      
      // Determine the contribution mode
      const contributionMode = mode || story.contribution_mode;
      
      // Validate the contribution based on its mode
      const validation = ContributionValidator.validate(
        content,
        contributionMode,
        story.words_per_contribution
      );
      
      if (!validation.isValid) {
        return res.status(400).json({ error: validation.error });
      }
      
      // Process the contribution based on its mode
      const contributions = [];
      let totalWordCount = 0;
      
      // Get the latest contribution to determine position
      const latestContributionResult = await DatabaseAdapter.getLatestContribution(storyId);
      const position = latestContributionResult?.data ? latestContributionResult.data.position + 1 : 0;
      
      switch (contributionMode) {
        case ContributionMode.WORD:
          // Single word contribution
          contributions.push({
            story_id: storyId,
            user_id: userId,
            content,
            position,
            type: ContributionMode.WORD,
          });
          totalWordCount = 1;
          break;
          
        case ContributionMode.MULTI_WORD: {
          // Split multi-word contribution into tokens
          const tokens = ContributionValidator.tokenizeMultiWord(content);
          
          // Create a contribution for each token
          tokens.forEach((token, index) => {
            contributions.push({
              story_id: storyId,
              user_id: userId,
              content: token,
              position: position + index,
              type: ContributionMode.MULTI_WORD,
            });
          });
          
          totalWordCount = tokens.length;
          break;
        }
          
        case ContributionMode.SENTENCE:
          // Store the entire sentence as a single contribution
          contributions.push({
            story_id: storyId,
            user_id: userId,
            content,
            position,
            type: ContributionMode.SENTENCE,
          });
          totalWordCount = content.trim().split(/\s+/).length;
          break;
          
        case ContributionMode.PARAGRAPH:
          // Store the entire paragraph as a single contribution
          contributions.push({
            story_id: storyId,
            user_id: userId,
            content,
            position,
            type: ContributionMode.PARAGRAPH,
          });
          totalWordCount = content.trim().split(/\s+/).length;
          break;
      }
      
      // Save all contributions
      const savedContributions = [];
      for (const contribution of contributions) {
        const result = await DatabaseAdapter.createContribution(contribution);
        if (result.error) {
          return res.status(500).json({ error: result.error.message });
        }
        savedContributions.push(result.data);
      }
      
      // Update story with new word count and next turn
      const participants = story.participants;
      
      // Find next participant's turn
      const currentIndex = participants.findIndex(p => p.id === userId);
      const nextIndex = (currentIndex + 1) % participants.length;
      const nextTurn = participants[nextIndex].id;
      
      await DatabaseAdapter.updateStory(storyId, {
        word_count: story.word_count + totalWordCount,
        current_turn: nextTurn,
        updated_at: new Date(),
      });
      
      // Broadcast the contribution to all connected clients
      for (const contribution of savedContributions) {
        realTimeService.broadcastContribution(storyId, {
          id: contribution.id,
          content: contribution.content,
          author: {
            id: userId,
            username: username || 'Anonymous'
          },
          created_at: new Date().toISOString(),
          position: contribution.position,
          storyId: storyId,
        });
      }
      
      // Return the contributions with updated story info
      res.status(201).json({
        contributions: savedContributions,
        nextTurn,
        wordCount: story.word_count + totalWordCount,
      });
    } catch (error) {
      console.error('Error submitting contribution:', error);
      res.status(500).json({ error: 'Failed to submit contribution' });
    }
  }
}
