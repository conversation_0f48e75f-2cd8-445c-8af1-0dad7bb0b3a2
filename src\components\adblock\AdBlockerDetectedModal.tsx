import React, { useState, useEffect } from 'react';
import { Alert<PERSON>riangle, Shield, Heart, RefreshCw, X } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Card, CardContent } from '@/components/ui/card';
import { dismissAdBlockMessage, detectAdBlocker, type AdBlockDetectionResult } from '@/utils/adblock-detector';

interface AdBlockerDetectedModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  detectionResult: AdBlockDetectionResult;
}

export const AdBlockerDetectedModal: React.FC<AdBlockerDetectedModalProps> = ({
  isOpen,
  onOpenChange,
  detectionResult,
}) => {
  const [isRetesting, setIsRetesting] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  const handleRetest = async () => {
    setIsRetesting(true);
    try {
      // Wait a moment for user to disable ad blocker
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Retest ad blocker detection
      const newResult = await detectAdBlocker();
      
      if (!newResult.isBlocked) {
        // Ad blocker disabled, close modal
        onOpenChange(false);
        window.location.reload(); // Refresh to ensure everything works
      }
    } catch (error) {
      console.error('Error retesting ad blocker:', error);
    } finally {
      setIsRetesting(false);
    }
  };

  const handleDismissTemporarily = () => {
    dismissAdBlockMessage(5/60); // Dismiss for 5 minutes (5/60 hours)
    onOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Shield className="h-6 w-6 text-orange-500" />
            Ad Blocker Detected
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Main Message */}
          <Card className="border-orange-200 bg-orange-50">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <Heart className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-900">
                    We noticed you're using an ad blocker
                  </p>
                  <p className="text-sm text-gray-700">
                    <strong>Word-by-Word Story</strong> is completely free to use, but we rely on ads 
                    and analytics to keep the lights on and continue improving your storytelling experience.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* What this means */}
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="p-4">
              <div className="space-y-2">
                <p className="text-sm font-medium text-blue-900">
                  What this means:
                </p>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Your stories and progress are safe</li>
                  <li>• Analytics help us improve the app</li>
                  <li>• Ads support our small team of developers</li>
                  <li>• Some features may not work optimally</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          {/* Instructions */}
          <div className="space-y-3">
            <p className="text-sm font-medium text-gray-900">
              To support us and get the best experience:
            </p>
            
            <div className="space-y-2 text-sm text-gray-700">
              <div className="flex items-start gap-2">
                <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mt-0.5">1</span>
                <span>Disable your ad blocker for <code className="bg-gray-100 px-1 rounded">{window.location.hostname}</code></span>
              </div>
              <div className="flex items-start gap-2">
                <span className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mt-0.5">2</span>
                <span>Click "I've Disabled It" below to test</span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col gap-2">
            <Button 
              onClick={handleRetest}
              disabled={isRetesting}
              className="w-full"
            >
              {isRetesting ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Checking...
                </>
              ) : (
                "I've Disabled It - Test Again"
              )}
            </Button>
            
            <Button 
              variant="outline"
              onClick={handleDismissTemporarily}
              className="w-full text-xs"
            >
              Remind me in 5 minutes
            </Button>
          </div>

          {/* Additional Details Toggle */}
          <div className="pt-2 border-t">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              {showDetails ? 'Hide' : 'Show'} technical details
            </Button>
            
            {showDetails && (
              <Card className="mt-2 border-gray-200">
                <CardContent className="p-3">
                  <div className="text-xs text-gray-600 space-y-1">
                    <p><strong>Detected:</strong> {detectionResult.detectedAt.toLocaleString()}</p>
                    <p><strong>User agent:</strong> {navigator.userAgent.slice(0, 50)}...</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AdBlockerDetectedModal; 