import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Switch } from '@/components/ui/switch';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Loader2, User, Globe, Image as ImageIcon, Check, X } from 'lucide-react';
import { useAvatar } from '@/hooks/useAvatar';
import { useToast } from '@/hooks/use-toast';
import { AvatarPreference } from '@/services/gravatarService';

interface AvatarSelectionProps {
  className?: string;
}

export const AvatarSelection: React.FC<AvatarSelectionProps> = ({ className }) => {
  const { avatarUrl, avatarOptions, preferences, updatePreferences, isLoading, loadAvatarOptions } = useAvatar();
  const { toast } = useToast();
  
  const [customUrl, setCustomUrl] = useState(preferences.customUrl || '');
  const [isValidatingUrl, setIsValidatingUrl] = useState(false);
  const [customUrlValid, setCustomUrlValid] = useState<boolean | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  const handleSourceChange = async (source: 'gravatar' | 'custom' | 'default') => {
    setIsSaving(true);
    try {
      const success = await updatePreferences({ source });
      if (success) {
        toast({
          title: 'Avatar Updated',
          description: 'Your avatar preference has been saved.',
        });
      } else {
        toast({
          title: 'Update Failed',
          description: 'Failed to update avatar preference. Please try again.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating avatar source:', error);
      toast({
        title: 'Update Failed',
        description: 'An error occurred while updating your avatar.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleGravatarToggle = async (useGravatar: boolean) => {
    setIsSaving(true);
    try {
      const success = await updatePreferences({ useGravatar });
      if (success) {
        toast({
          title: 'Gravatar Preference Updated',
          description: useGravatar 
            ? 'Gravatar integration enabled.' 
            : 'Gravatar integration disabled.',
        });
      } else {
        toast({
          title: 'Update Failed',
          description: 'Failed to update Gravatar preference.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating Gravatar preference:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCustomUrlChange = (url: string) => {
    setCustomUrl(url);
    setCustomUrlValid(null);
  };

  const validateCustomUrl = async () => {
    if (!customUrl.trim()) {
      setCustomUrlValid(false);
      return;
    }

    setIsValidatingUrl(true);
    setCustomUrlValid(null);

    try {
      const response = await fetch(customUrl, { method: 'HEAD' });
      const contentType = response.headers.get('content-type');
      const isValidImage = response.status === 200 && contentType?.startsWith('image/');
      
      setCustomUrlValid(isValidImage);
      
      if (isValidImage) {
        const success = await updatePreferences({ 
          customUrl: customUrl.trim(),
          source: 'custom' 
        });
        
        if (success) {
          toast({
            title: 'Custom Avatar Set',
            description: 'Your custom avatar has been saved.',
          });
        }
      } else {
        toast({
          title: 'Invalid Image URL',
          description: 'The URL does not point to a valid image.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error validating custom URL:', error);
      setCustomUrlValid(false);
      toast({
        title: 'Invalid URL',
        description: 'Failed to validate the image URL.',
        variant: 'destructive',
      });
    } finally {
      setIsValidatingUrl(false);
    }
  };

  const getPreviewUrl = (source: string) => {
    switch (source) {
      case 'gravatar':
        return avatarOptions?.gravatar.url || avatarUrl;
      case 'initials':
        return avatarOptions?.initials || avatarUrl;
      case 'custom':
        return customUrl || avatarUrl;
      default:
        return avatarUrl;
    }
  };

  const getCurrentInitials = () => {
    // Extract initials from the current avatar URL if it's an initials avatar
    if (avatarOptions?.initials) {
      const match = avatarOptions.initials.match(/name=([^&]+)/);
      return match ? decodeURIComponent(match[1]) : 'U';
    }
    return 'U';
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="w-5 h-5" />
          Avatar Settings
        </CardTitle>
        <CardDescription>
          Choose how your profile picture appears to other users
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Current Avatar Preview */}
        <div className="flex items-center gap-4">
          <div className="relative">
            <Avatar className="w-16 h-16">
              <AvatarImage src={avatarUrl} />
              <AvatarFallback>
                {isLoading ? <Loader2 className="w-6 h-6 animate-spin" /> : getCurrentInitials()}
              </AvatarFallback>
            </Avatar>
            {isLoading && (
              <div className="absolute inset-0 flex items-center justify-center bg-black/20 rounded-full">
                <Loader2 className="w-4 h-4 animate-spin text-white" />
              </div>
            )}
          </div>
          <div>
            <p className="font-medium">Current Avatar</p>
            <p className="text-sm text-muted-foreground">
              Source: {preferences.source === 'gravatar' ? 'Gravatar' : 
                      preferences.source === 'custom' ? 'Custom URL' : 'Default'}
            </p>
          </div>
        </div>

        {/* Gravatar Integration Toggle */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label className="text-base">Enable Gravatar</Label>
            <p className="text-sm text-muted-foreground">
              Use Gravatar for automatic avatar detection based on your email
            </p>
          </div>
          <Switch
            checked={preferences.useGravatar}
            onCheckedChange={handleGravatarToggle}
            disabled={isSaving}
          />
        </div>

        {/* Avatar Source Selection */}
        <div className="space-y-3">
          <Label className="text-base">Avatar Source</Label>
          <RadioGroup
            value={preferences.source}
            onValueChange={handleSourceChange}
            disabled={isSaving}
            className="space-y-3"
          >
            {/* Gravatar Option */}
            <div className="flex items-center space-x-3 p-3 border rounded-lg">
              <RadioGroupItem value="gravatar" id="gravatar" disabled={!preferences.useGravatar} />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <Globe className="w-4 h-4" />
                  <Label htmlFor="gravatar">Gravatar</Label>
                  {avatarOptions?.gravatar.available && (
                    <Badge variant="outline" className="text-xs">Available</Badge>
                  )}
                  {!preferences.useGravatar && (
                    <Badge variant="secondary" className="text-xs">Disabled</Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground">
                  {preferences.useGravatar 
                    ? 'Use your Gravatar profile picture' 
                    : 'Enable Gravatar integration to use this option'}
                </p>
                {preferences.useGravatar && (
                  <div className="mt-2">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={getPreviewUrl('gravatar')} />
                      <AvatarFallback>
                        <User className="w-4 h-4" />
                      </AvatarFallback>
                    </Avatar>
                  </div>
                )}
              </div>
            </div>

            {/* Custom URL Option */}
            <div className="flex items-center space-x-3 p-3 border rounded-lg">
              <RadioGroupItem value="custom" id="custom" />
              <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                  <ImageIcon className="w-4 h-4" />
                  <Label htmlFor="custom">Custom Image URL</Label>
                </div>
                <p className="text-sm text-muted-foreground">
                  Use a custom image from any URL
                </p>
                <div className="flex gap-2">
                  <Input
                    placeholder="https://example.com/avatar.jpg"
                    value={customUrl}
                    onChange={(e) => handleCustomUrlChange(e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={validateCustomUrl}
                    disabled={!customUrl.trim() || isValidatingUrl}
                  >
                    {isValidatingUrl ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : customUrlValid === true ? (
                      <Check className="w-4 h-4 text-green-600" />
                    ) : customUrlValid === false ? (
                      <X className="w-4 h-4 text-red-600" />
                    ) : (
                      'Test'
                    )}
                  </Button>
                </div>
                {customUrl && (
                  <div className="mt-2">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={getPreviewUrl('custom')} />
                      <AvatarFallback>
                        <ImageIcon className="w-4 h-4" />
                      </AvatarFallback>
                    </Avatar>
                  </div>
                )}
              </div>
            </div>

            {/* Initials/Default Option */}
            <div className="flex items-center space-x-3 p-3 border rounded-lg">
              <RadioGroupItem value="default" id="default" />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <User className="w-4 h-4" />
                  <Label htmlFor="default">Generated Avatar</Label>
                </div>
                <p className="text-sm text-muted-foreground">
                  Use an automatically generated avatar based on your initials
                </p>
                <div className="mt-2">
                  <Avatar className="w-8 h-8">
                    <AvatarImage src={getPreviewUrl('initials')} />
                    <AvatarFallback>
                      {getCurrentInitials()}
                    </AvatarFallback>
                  </Avatar>
                </div>
              </div>
            </div>
          </RadioGroup>
        </div>

        {/* Refresh Button */}
        <div className="pt-4 border-t">
          <Button
            variant="outline"
            onClick={loadAvatarOptions}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Refreshing...
              </>
            ) : (
              'Refresh Avatar Options'
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}; 