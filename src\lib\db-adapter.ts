import { supabase } from './supabase';
import { StoryStatus } from "@prisma/client"; // This was imported in story-contributions.ts, check if needed or if there's an equivalent

// Define common types based on usage in story-contributions.ts
// These might need refinement based on your actual schema / Supabase types
interface StoryData {
  title: string;
  description?: string;
  created_by: string; // Assuming this is user ID
  contribution_mode?: string; // e.g., ContributionMode.WORD
  words_per_contribution?: number;
  participants?: string[]; // Array of user IDs
  current_turn?: string; // User ID
  word_count?: number;
  status?: StoryStatus | string; // Allow string for flexibility if StoryStatus isn't directly from @prisma/client here
  updated_at?: Date;
  // Add any other fields your 'stories' table has which are used by the adapter
}

interface ContributionData {
  story_id: string;
  user_id: string;
  content: string;
  position: number;
  type?: string; // e.g., ContributionMode.WORD
  // Add any other fields your 'contributions' table has
}

interface UserSessionData {
  user_id: string;
  story_id: string;
  is_typing?: boolean;
  is_online?: boolean;
  last_active?: Date;
  // Add any other fields for user_session table
}

// This will be the main export used by story-contributions.ts
export const DatabaseAdapter = {
  async createStory(storyData: StoryData) {
    const { data, error } = await supabase
      .from('stories')
      .insert(storyData)
      .select()
      .single();
    return { data, error };
  },

  async getStory(storyId: string) {
    const { data, error } = await supabase
      .from('stories')
      .select('*')
      .eq('id', storyId)
      .single();
    return { data, error };
  },

  async getStoryWithContributions(storyId: string) {
    // Assuming 'contributions' is a related table and RLS allows fetching them.
    // Adjust the select if your Supabase types (Database['public']['Tables']['stories']['Row'] & contributions) are set up.
    const { data, error } = await supabase
      .from('stories')
      .select('*, contributions(*)') 
      .eq('id', storyId)
      .single();
    return { data, error };
  },
  
  async getLatestContribution(storyId: string) {
    const { data, error } = await supabase
      .from('contributions')
      .select('*')
      .eq('story_id', storyId)
      .order('position', { ascending: false }) // Or order by 'created_at'
      .limit(1)
      .maybeSingle(); // Use maybeSingle() if it's okay for no contribution to exist
    return { data, error };
  },

  async createContribution(contributionData: ContributionData) {
    const { data, error } = await supabase
      .from('contributions')
      .insert(contributionData)
      .select()
      .single();
    return { data, error };
  },
  
  async updateStory(storyId: string, updateData: Partial<StoryData>) {
     const { data, error } = await supabase
      .from('stories')
      .update(updateData)
      .eq('id', storyId)
      .select()
      .single();
    return { data, error };
  },

  async addParticipantToStory(storyId: string, userId: string) {
    // This implementation assumes 'participants' is an array column on the 'stories' table
    // that stores user IDs. Adjust if your schema is different (e.g., a join table).
    const { data: story, error: fetchError } = await supabase
      .from('stories')
      .select('participants')
      .eq('id', storyId)
      .single();

    if (fetchError || !story) {
      return { data: null, error: fetchError || { message: 'Story not found for adding participant', details: '' } };
    }

    // Ensure participants array exists and user is not already a participant
    const currentParticipants = story.participants || [];
    if (currentParticipants.includes(userId)) {
      return { data: story, error: null }; // User already a participant, treat as success
    }
    
    const updatedParticipants = [...currentParticipants, userId];
    
    const { data, error } = await supabase
      .from('stories')
      .update({ participants: updatedParticipants })
      .eq('id', storyId)
      .select() // Select what you need to return
      .single();
      
    return { data, error };
  },

  async updateUserSession(sessionData: UserSessionData) {
    // Assuming you have a 'user_sessions' table or similar (like 'presence' table we created earlier)
    // Let's use the 'presence' table for this, as it fits the fields.
    // 'upsert' is good here: inserts if not exist, updates if exists (based on primary key or constraint)
    // Ensure your 'presence' table has a primary key or unique constraint on (story_id, user_id) for upsert.
    const { data, error } = await supabase
      .from('presence') // Using the 'presence' table
      .upsert(sessionData, { onConflict: 'story_id, user_id' }) // Assuming unique_user_story_presence constraint
      .select()
      .single(); // Assuming upsert on conflict returns the row
    return { data, error };
  },
  
  async getStoryUserSessions(storyId: string) {
    // Fetching from the 'presence' table
    const { data, error } = await supabase
      .from('presence')
      .select('*')
      .eq('story_id', storyId);
    return { data, error };
  }
}; 