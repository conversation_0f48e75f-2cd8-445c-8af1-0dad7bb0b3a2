import React, { useState, use<PERSON>allback, useEffect, useMemo } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Ta<PERSON>,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Search,
  X,
  Filter,
  Clock,
  User,
  Zap,
  RotateCcw,
  Crown,
  Calendar,
  Hash,
  ArrowRight,
} from 'lucide-react';
import { format, startOfDay, endOfDay, subDays, subWeeks, subMonths } from 'date-fns';

export interface SearchableContribution {
  id: string;
  content: string;
  author: {
    id: string;
    username: string;
    avatar_url?: string;
  };
  created_at: string;
  position: number;
  special_type?: 'gotcha' | 'reverse' | 'golden' | null;
  token_cost?: number;
}

export interface SearchResult {
  contribution: SearchableContribution;
  matchedText: string;
  contextBefore: string;
  contextAfter: string;
  matchScore: number;
}

export interface StorySearchOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  contributions: SearchableContribution[];
  onNavigateToContribution: (contributionId: string) => void;
  className?: string;
}

type DateRange = 'all' | 'today' | 'week' | 'month' | 'custom';
type SortBy = 'relevance' | 'position' | 'recent' | 'author';

const specialTypeConfig = {
  gotcha: {
    icon: Zap,
    label: 'Gotcha Word',
    color: 'text-yellow-600 dark:text-yellow-400',
  },
  reverse: {
    icon: RotateCcw,
    label: 'Reverse Move',
    color: 'text-purple-600 dark:text-purple-400',
  },
  golden: {
    icon: Crown,
    label: 'Golden Contribution',
    color: 'text-amber-600 dark:text-amber-400',
  },
};

export const StorySearchOverlay: React.FC<StorySearchOverlayProps> = ({
  isOpen,
  onClose,
  contributions,
  onNavigateToContribution,
  className,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedAuthor, setSelectedAuthor] = useState<string>('all');
  const [selectedSpecialType, setSelectedSpecialType] = useState<string>('all');
  const [dateRange, setDateRange] = useState<DateRange>('all');
  const [sortBy, setSortBy] = useState<SortBy>('relevance');
  const [activeTab, setActiveTab] = useState('search');

  // Get unique authors
  const authors = useMemo(() => {
    const uniqueAuthors = Array.from(new Set(contributions.map(c => c.author.id)))
      .map(authorId => contributions.find(c => c.author.id === authorId)?.author)
      .filter(Boolean);
    return uniqueAuthors;
  }, [contributions]);

  // Search function with highlighting
  const searchContributions = useCallback((query: string, contribs: SearchableContribution[]): SearchResult[] => {
    if (!query.trim()) return [];

    const searchTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 0);
    
    return contribs
      .map(contribution => {
        const content = contribution.content.toLowerCase();
        let totalScore = 0;
        let matchedText = '';
        let matchIndex = -1;

        // Calculate match score and find best match
        for (const term of searchTerms) {
          const index = content.indexOf(term);
          if (index !== -1) {
            totalScore += term.length;
            if (matchIndex === -1) {
              matchIndex = index;
              matchedText = term;
            }
          }
        }

        if (totalScore === 0) return null;

        // Calculate context
        const contextLength = 50;
        const start = Math.max(0, matchIndex - contextLength);
        const end = Math.min(contribution.content.length, matchIndex + matchedText.length + contextLength);
        
        const contextBefore = contribution.content.slice(start, matchIndex);
        const contextAfter = contribution.content.slice(matchIndex + matchedText.length, end);

        return {
          contribution,
          matchedText: contribution.content.slice(matchIndex, matchIndex + matchedText.length),
          contextBefore: start > 0 ? '...' + contextBefore : contextBefore,
          contextAfter: end < contribution.content.length ? contextAfter + '...' : contextAfter,
          matchScore: totalScore,
        };
      })
      .filter(result => result !== null) as SearchResult[];
  }, []);

  // Apply filters
  const filteredResults = useMemo(() => {
    let filtered = contributions;

    // Author filter
    if (selectedAuthor !== 'all') {
      filtered = filtered.filter(c => c.author.id === selectedAuthor);
    }

    // Special type filter
    if (selectedSpecialType !== 'all') {
      if (selectedSpecialType === 'none') {
        filtered = filtered.filter(c => !c.special_type);
      } else {
        filtered = filtered.filter(c => c.special_type === selectedSpecialType);
      }
    }

    // Date range filter
    if (dateRange !== 'all') {
      const now = new Date();
      let startDate: Date;

      switch (dateRange) {
        case 'today':
          startDate = startOfDay(now);
          break;
        case 'week':
          startDate = subWeeks(now, 1);
          break;
        case 'month':
          startDate = subMonths(now, 1);
          break;
        default:
          startDate = new Date(0);
      }

      filtered = filtered.filter(c => new Date(c.created_at) >= startDate);
    }

    // Search and sort
    if (searchQuery.trim()) {
      const results = searchContributions(searchQuery, filtered);
      
      return results.sort((a, b) => {
        switch (sortBy) {
          case 'relevance':
            return b.matchScore - a.matchScore;
          case 'position':
            return a.contribution.position - b.contribution.position;
          case 'recent':
            return new Date(b.contribution.created_at).getTime() - new Date(a.contribution.created_at).getTime();
          case 'author':
            return a.contribution.author.username.localeCompare(b.contribution.author.username);
          default:
            return b.matchScore - a.matchScore;
        }
      });
    }

    // If no search query, return filtered contributions as results
    return filtered
      .map(contribution => ({
        contribution,
        matchedText: '',
        contextBefore: '',
        contextAfter: '',
        matchScore: 0,
      }))
      .sort((a, b) => {
        switch (sortBy) {
          case 'position':
            return a.contribution.position - b.contribution.position;
          case 'recent':
            return new Date(b.contribution.created_at).getTime() - new Date(a.contribution.created_at).getTime();
          case 'author':
            return a.contribution.author.username.localeCompare(b.contribution.author.username);
          default:
            return a.contribution.position - b.contribution.position;
        }
      });
  }, [searchQuery, selectedAuthor, selectedSpecialType, dateRange, sortBy, contributions, searchContributions]);

  // Handle contribution click
  const handleContributionClick = useCallback((contributionId: string) => {
    onNavigateToContribution(contributionId);
    onClose();
  }, [onNavigateToContribution, onClose]);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setSearchQuery('');
    setSelectedAuthor('all');
    setSelectedSpecialType('all');
    setDateRange('all');
    setSortBy('relevance');
  }, []);

  // Highlight search terms in text
  const highlightText = useCallback((text: string, query: string) => {
    if (!query.trim()) return text;

    const searchTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 0);
    let highlightedText = text;

    searchTerms.forEach(term => {
      const regex = new RegExp(`(${term})`, 'gi');
      highlightedText = highlightedText.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>');
    });

    return highlightedText;
  }, []);

  // Reset state when dialog closes
  useEffect(() => {
    if (!isOpen) {
      setActiveTab('search');
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={cn('max-w-4xl max-h-[80vh] p-0', className)}>
        <DialogHeader className="p-6 border-b">
          <DialogTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            Search Story
            <Badge variant="secondary" className="ml-auto">
              {filteredResults.length} result{filteredResults.length !== 1 ? 's' : ''}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="flex flex-col h-full">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
            <div className="p-4 border-b">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="search">Search</TabsTrigger>
                <TabsTrigger value="filters">Filters</TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="search" className="mt-0 p-4 space-y-4">
              {/* Search Input */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search story content..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-10"
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setSearchQuery('')}
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                )}
              </div>

              {/* Quick Sort Options */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Sort by:</span>
                <div className="flex gap-1">
                  {[
                    { value: 'relevance', label: 'Relevance' },
                    { value: 'position', label: 'Position' },
                    { value: 'recent', label: 'Recent' },
                    { value: 'author', label: 'Author' },
                  ].map((option) => (
                    <Button
                      key={option.value}
                      variant={sortBy === option.value ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSortBy(option.value as SortBy)}
                    >
                      {option.label}
                    </Button>
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="filters" className="mt-0 p-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Author Filter */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Author</label>
                  <Select value={selectedAuthor} onValueChange={setSelectedAuthor}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Authors</SelectItem>
                      {authors.map((author) => (
                        <SelectItem key={author!.id} value={author!.id}>
                          <div className="flex items-center gap-2">
                            <Avatar className="w-4 h-4">
                              <AvatarImage src={author!.avatar_url} />
                              <AvatarFallback className="text-xs">
                                {author!.username.slice(0, 1).toUpperCase()}
                              </AvatarFallback>
                            </Avatar>
                            {author!.username}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Special Type Filter */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Special Actions</label>
                  <Select value={selectedSpecialType} onValueChange={setSelectedSpecialType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="none">Regular Contributions</SelectItem>
                      <SelectItem value="gotcha">Gotcha Words</SelectItem>
                      <SelectItem value="reverse">Reverse Moves</SelectItem>
                      <SelectItem value="golden">Golden Contributions</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Date Range Filter */}
                <div>
                  <label className="text-sm font-medium mb-2 block">Date Range</label>
                  <Select value={dateRange} onValueChange={(value) => setDateRange(value as DateRange)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="week">Past Week</SelectItem>
                      <SelectItem value="month">Past Month</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Clear Filters */}
                <div className="flex items-end">
                  <Button variant="outline" onClick={clearFilters} className="w-full">
                    Clear All Filters
                  </Button>
                </div>
              </div>
            </TabsContent>
          </Tabs>

          {/* Results */}
          <ScrollArea className="flex-1 border-t">
            <div className="p-4">
              {filteredResults.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>No contributions found matching your criteria</p>
                  <p className="text-sm mt-1">Try adjusting your search or filters</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredResults.map((result) => {
                    const { contribution } = result;
                    const specialConfig = contribution.special_type 
                      ? specialTypeConfig[contribution.special_type] 
                      : null;

                    return (
                      <div
                        key={contribution.id}
                        className="p-4 border rounded-lg hover:bg-muted/50 cursor-pointer transition-colors group"
                        onClick={() => handleContributionClick(contribution.id)}
                      >
                        <div className="flex items-start gap-3">
                          <Avatar className="w-8 h-8 flex-shrink-0">
                            <AvatarImage src={contribution.author.avatar_url} />
                            <AvatarFallback className="text-xs">
                              {contribution.author.username.slice(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-2">
                              <span className="font-medium">{contribution.author.username}</span>
                              <Badge variant="outline" className="text-xs">
                                #{contribution.position}
                              </Badge>
                              {specialConfig && (
                                <Badge variant="secondary" className="text-xs">
                                  <specialConfig.icon className="w-3 h-3 mr-1" />
                                  {specialConfig.label}
                                </Badge>
                              )}
                              <span className="text-xs text-muted-foreground ml-auto">
                                {format(new Date(contribution.created_at), 'MMM d, h:mm a')}
                              </span>
                            </div>

                            {/* Content with highlighting */}
                            <div className="text-sm leading-relaxed">
                              {searchQuery ? (
                                <p
                                  dangerouslySetInnerHTML={{
                                    __html: `${result.contextBefore}<strong>${highlightText(result.matchedText, searchQuery)}</strong>${result.contextAfter}`
                                  }}
                                />
                              ) : (
                                <p>{contribution.content}</p>
                              )}
                            </div>

                            {/* Navigate button */}
                            <div className="flex items-center gap-2 mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                              <Button variant="ghost" size="sm" className="h-7 text-xs">
                                <ArrowRight className="w-3 h-3 mr-1" />
                                Go to contribution
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default StorySearchOverlay; 