[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [radio-group](../README.md) / RadioGroupItem

# Variable: RadioGroupItem

> `const` **RadioGroupItem**: `ForwardRefExoticComponent`\<`Omit`\<`RadioGroupItemProps` & `RefAttributes`\<`HTMLButtonElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLButtonElement`\>\>

Defined in: [src/components/ui/radio-group.tsx:21](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/radio-group.tsx#L21)
