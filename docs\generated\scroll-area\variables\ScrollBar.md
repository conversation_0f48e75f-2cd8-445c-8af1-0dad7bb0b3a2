[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [scroll-area](../README.md) / ScrollBar

# Variable: ScrollBar

> `const` **ScrollBar**: `ForwardRefExoticComponent`\<`Omit`\<`ScrollAreaScrollbarProps` & `RefAttributes`\<`HTMLDivElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLDivElement`\>\>

Defined in: [src/components/ui/scroll-area.tsx:24](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/scroll-area.tsx#L24)
