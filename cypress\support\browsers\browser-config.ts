// Browser and device testing configuration

export const BROWSERS = {
  chrome: {
    name: 'chrome',
    family: 'chromium',
    displayName: 'Chrome',
    version: 'latest',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
  },
  firefox: {
    name: 'firefox',
    family: 'firefox',
    displayName: 'Firefox',
    version: 'latest',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0'
  },
  edge: {
    name: 'edge',
    family: 'chromium',
    displayName: 'Microsoft Edge',
    version: 'latest',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0'
  },
  webkit: {
    name: 'webkit',
    family: 'webkit',
    displayName: 'Safari/WebKit',
    version: 'latest',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Safari/605.1.15'
  }
}

export const VIEWPORTS = {
  // Mobile devices
  iphone_se: { width: 375, height: 667, deviceName: 'iPhone SE' },
  iphone_12: { width: 390, height: 844, deviceName: 'iPhone 12' },
  iphone_14_pro: { width: 393, height: 852, deviceName: 'iPhone 14 Pro' },
  samsung_s20: { width: 360, height: 800, deviceName: 'Samsung Galaxy S20' },
  samsung_s22: { width: 384, height: 854, deviceName: 'Samsung Galaxy S22' },
  
  // Tablets
  ipad: { width: 768, height: 1024, deviceName: 'iPad' },
  ipad_air: { width: 820, height: 1180, deviceName: 'iPad Air' },
  ipad_pro: { width: 1024, height: 1366, deviceName: 'iPad Pro' },
  samsung_tab: { width: 800, height: 1280, deviceName: 'Samsung Galaxy Tab' },
  
  // Desktop
  laptop: { width: 1366, height: 768, deviceName: 'Laptop' },
  desktop: { width: 1920, height: 1080, deviceName: 'Desktop' },
  desktop_large: { width: 2560, height: 1440, deviceName: 'Large Desktop' },
  desktop_4k: { width: 3840, height: 2160, deviceName: '4K Desktop' }
}

export const DEVICE_CATEGORIES = {
  mobile: ['iphone_se', 'iphone_12', 'iphone_14_pro', 'samsung_s20', 'samsung_s22'],
  tablet: ['ipad', 'ipad_air', 'ipad_pro', 'samsung_tab'],
  desktop: ['laptop', 'desktop', 'desktop_large', 'desktop_4k']
}

export const CRITICAL_FEATURES = [
  'navigation',
  'authentication',
  'story_creation',
  'story_contribution',
  'story_gallery',
  'subscription',
  'payments',
  'responsive_layout',
  'forms',
  'buttons',
  'modals',
  'notifications'
]

export const PERFORMANCE_THRESHOLDS = {
  // Lighthouse thresholds
  performance: 90,
  accessibility: 95,
  bestPractices: 90,
  seo: 95,
  
  // Core Web Vitals
  lcp: 2500, // Largest Contentful Paint (ms)
  fid: 100,  // First Input Delay (ms)
  cls: 0.1,  // Cumulative Layout Shift
  
  // Custom metrics
  pageLoadTime: 3000, // ms
  firstInteractive: 2000, // ms
  totalBlockingTime: 300 // ms
}