import { useState } from "react";
import {
  moderateContent,
  reportContent,
  checkRateLimit,
} from "@/services/moderationService";
import { useAuth } from "@/contexts/auth";
import { useToast } from "./use-toast";

export const useContentModeration = () => {
  const [isChecking, setIsChecking] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  const validateContent = async (
    content: string,
  ): Promise<{
    isValid: boolean;
    moderatedContent?: string;
  }> => {
    setIsChecking(true);

    try {
      // Check rate limiting
      if (user?.id) {
        const notRateLimited = checkRateLimit(user.id);
        if (!notRateLimited) {
          toast({
            title: "Slow down",
            description:
              "You're contributing too quickly. Please wait a moment.",
            variant: "destructive",
          });
          return { isValid: false };
        }
      }

      // Check content
      const { moderated, wasModified, isRejected } = moderateContent(content);

      if (isRejected) {
        toast({
          title: "Content rejected",
          description:
            "Your contribution contains inappropriate content that isn't allowed on our platform.",
          variant: "destructive",
        });
        return { isValid: false };
      }

      if (wasModified) {
        toast({
          title: "Content modified",
          description:
            "Some words in your contribution were modified to comply with our guidelines.",
        });
      }

      return {
        isValid: true,
        moderatedContent: moderated,
      };
    } finally {
      setIsChecking(false);
    }
  };

  const reportInappropriateContent = async (
    contentId: string,
    contentType: "story" | "contribution" | "comment",
    reason: string,
  ) => {
    if (!user?.id) {
      toast({
        title: "Authentication required",
        description: "You must be logged in to report content.",
        variant: "destructive",
      });
      return false;
    }

    return reportContent(contentId, contentType, reason, user.id);
  };

  return {
    validateContent,
    reportInappropriateContent,
    isChecking,
  };
};
