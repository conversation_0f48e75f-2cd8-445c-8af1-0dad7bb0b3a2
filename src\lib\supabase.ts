import { createClient } from '@supabase/supabase-js';
// import { Database } from '@/types/supabase'; // Temporarily comment out if types are not generated yet

// Initialize the Supabase client
// Use Vite's import.meta.env for client-side, and process.env for potential server-side use (e.g., in server.mjs via tsx)
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL || '';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing Supabase URL or Anon Key. Ensure VITE_SUPABASE_URL & VITE_SUPABASE_ANON_KEY (for client) or SUPABASE_URL & SUPABASE_ANON_KEY (for server) are set in your .env file.');
  // Optionally, you could throw an error here to halt execution if Supabase is critical
  // throw new Error('Supabase URL and Anon Key are required.'); 
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
});

// Helper function to test Supabase connectivity
export const testSupabaseConnectivity = async (): Promise<boolean> => {
  try {
    const { data, error } = await supabase.from('system_settings').select('*').limit(1);
    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Supabase connectivity test failed:', error);
    return false;
  }
};

// Export Supabase realtime functionality
export const realtime = {
  /**
   * Subscribe to realtime changes on a table
   * @param table The table to subscribe to
   * @param callback Function to call when changes occur
   * @param filter Optional filter for the subscription
   * @returns Subscription object with unsubscribe method
   */
  subscribe: (table: string, callback: (payload: any) => void, filter?: any) => {
    const channel = supabase
      .channel(`${table}-changes`)
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table, filter },
        (payload) => {
          callback(payload);
        }
      )
      .subscribe();

    return {
      unsubscribe: () => {
        supabase.removeChannel(channel);
      },
    };
  },

  /**
   * Subscribe to a specific story's realtime events
   * @param storyId ID of the story to subscribe to
   * @param callback Function to call when changes occur
   * @returns Subscription object with unsubscribe method
   */
  subscribeToStory: (storyId: string, callback: (event: { type: string; data: any }) => void) => {
    // Subscribe to contributions for this story
    const contributionsChannel = supabase
      .channel(`story-${storyId}-contributions`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'contributions',
          filter: `story_id=eq.${storyId}`,
        },
        (payload) => {
          callback({
            type: 'contribution_added',
            data: payload.new,
          });
        }
      )
      .subscribe();

    // Subscribe to user sessions for this story
    const sessionsChannel = supabase
      .channel(`story-${storyId}-sessions`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_session',
          filter: `story_id=eq.${storyId}`,
        },
        (payload) => {
          const eventType = payload.eventType === 'INSERT' 
            ? 'user_joined' 
            : payload.eventType === 'DELETE' 
              ? 'user_left' 
              : 'user_updated';
          
          callback({
            type: eventType,
            data: payload.new || payload.old,
          });
        }
      )
      .subscribe();

    // Return unsubscribe function that cleans up both channels
    return {
      unsubscribe: () => {
        supabase.removeChannel(contributionsChannel);
        supabase.removeChannel(sessionsChannel);
      },
    };
  },

  /**
   * Broadcast a user's typing status
   * @param storyId ID of the story
   * @param userId ID of the user
   * @param isTyping Whether the user is typing
   */
  broadcastTypingStatus: async (storyId: string, userId: string, isTyping: boolean) => {
    const { error } = await supabase
      .from('user_session')
      .update({ is_typing: isTyping, last_active: new Date().toISOString() })
      .match({ story_id: storyId, user_id: userId });

    if (error) {
      console.error('Error broadcasting typing status:', error);
    }
  },
};
