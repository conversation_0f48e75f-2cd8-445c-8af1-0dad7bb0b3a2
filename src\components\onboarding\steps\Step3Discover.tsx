import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, BookText, Users } from "lucide-react";

interface Step3DiscoverProps {
  onContinue: () => void;
}

export const Step3Discover: React.FC<Step3DiscoverProps> = ({ onContinue }) => {
  // Rich demo featured stories
  const featuredStories = [
    {
      id: "1",
      title: "Lost in the Mist",
      contributorsCount: 23,
      preview:
        "The fog rolled in silently, swallowing the city whole. <PERSON> took a deep breath and stepped forward, knowing her choices tonight would shape her destiny forever...",
    },
    {
      id: "2",
      title: "Beneath Silver Skies",
      contributorsCount: 17,
      preview:
        "The moon hung low, illuminating the campfire where legends were spun into life. Every eye sparkled with dreams, but only one dared to chase the wind that night...",
    },
    {
      id: "3",
      title: "The Final Voyage",
      contributorsCount: 31,
      preview:
        "Captain <PERSON><PERSON><PERSON> gripped the wheel as waves battered the hull. “We chart a course for the unknown,” he declared, as the motley crew readied for adventure...",
    },
    {
      id: "4",
      title: "Garden of Whispers",
      contributorsCount: 12,
      preview:
        "No one knew who planted the mysterious blossoms in the abandoned lot, but rumors spoke of secret messages in their petals, waiting to be discovered...",
    },
  ];

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold mb-2">Discover Popular Stories</h2>
        <p className="text-muted-foreground">
          Get inspired by exploring what others in the community have created.
          <br />
          <span className="text-xs italic text-gray-400">
            (You can only join <b>public</b> stories – private stories aren’t
            open to everyone.)
          </span>
        </p>
      </div>

      {/* Removed Explore Stories button */}

      <div className="space-y-4">
        <h3 className="text-base font-medium flex items-center gap-2">
          <Sparkles className="h-4 w-4 text-amber-500" />
          Featured Stories
        </h3>

        <div className="grid gap-3">
          {featuredStories.map((story) => (
            <Card key={story.id} className="overflow-hidden hover:shadow-lg">
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  <div className="bg-primary/10 h-12 w-12 rounded-md flex items-center justify-center flex-shrink-0">
                    <BookText className="h-6 w-6 text-primary" />
                  </div>
                  <div className="space-y-1">
                    <h4 className="font-medium">{story.title}</h4>
                    <p className="text-xs text-muted-foreground flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {story.contributorsCount} contributors
                    </p>
                    <p className="text-sm line-clamp-3">{story.preview}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};
