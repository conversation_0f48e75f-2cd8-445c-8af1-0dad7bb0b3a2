import { useState, useCallback } from "react";
import { supabase as db } from "@/lib/supabase";
import { User } from "@/contexts/auth/types";
import { BadgeTier } from "@/components/ui/achievement-badge";
import { adminService } from "@/services/adminService";

export const useUserProfile = () => {
  const [user, setUser] = useState<User | null>(null);

  const loadUserProfile = useCallback(async (userId: string) => {
    try {
      // Load user profile data from your database
      const { data, error } = await db
        .from("user_profiles")
        .select("id, username, avatar_url, is_admin, display_name, terms_accepted")
        .eq("id", userId)
        .single();

      if (error) {
        console.error("Error loading user profile:", error);
        return;
      }

      if (data) {
        // Update user state with profile data
        setUser((prevUser) => {
          if (!prevUser) return null;
          return {
            ...prevUser,
            username: data.username,
            profilePicture: data.avatar_url,
            isAdmin: data.is_admin === true, // Add admin status
            displayName: data.display_name,
            termsAccepted: data.terms_accepted === true, // Add terms acceptance status
            // Add other profile fields as needed
            tier: "free", // Mock tier value, replace with actual data
            credits: 10, // Mock credits value, replace with actual data
            achievements: {
              storiesCreated: 0,
              storiesParticipated: 0,
              wordsContributed: 0,
              votesReceived: 0,
              badges: [],
            },
          };
        });
      }
    } catch (error) {
      console.error("Failed to load user profile:", error);
    }
  }, []); // No dependencies needed since we're using functional updates

  const updateProfile = useCallback(async (data: Partial<User>) => {
    try {
      if (!user?.id) {
        throw new Error("No user ID available");
      }

      // Prepare the update data for the database
      const updateData: any = {};

      if (data.username !== undefined) updateData.username = data.username;
      if (data.profilePicture !== undefined) updateData.avatar_url = data.profilePicture;
      if (data.termsAccepted !== undefined) updateData.terms_accepted = data.termsAccepted;

      // Update the database
      const { error } = await db
        .from("user_profiles")
        .update(updateData)
        .eq("id", user.id);

      if (error) {
        console.error("Error updating user profile:", error);
        throw error;
      }

      // Update local state
      setUser((prevUser) => {
        if (!prevUser) return null;
        return {
          ...prevUser,
          ...data,
        };
      });

    } catch (error) {
      console.error("Failed to update user profile:", error);
      throw error;
    }
  }, [user?.id]);

  const updateAchievements = useCallback(
    async (achievements: Partial<User["achievements"]>) => {
      // Implementation placeholder
    },
    [],
  );

  const addBadge = useCallback(async (badge: BadgeTier) => {
    // Implementation placeholder
  }, []);

  const isAdmin = useCallback(() => {
    // Check if user has admin flag loaded from profile
    return user?.isAdmin === true;
  }, [user?.isAdmin]);

  return {
    user,
    setUser,
    loadUserProfile,
    updateProfile,
    updateAchievements,
    addBadge,
    isAdmin,
  };
};
