import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';

interface SimpleAvatarProps {
  user?: {
    id?: string;
    email?: string;
    username?: string;
    avatar_url?: string;
    avatarUrl?: string;
    profilePicture?: string;
  };
  email?: string;
  username?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

const sizeClasses = {
  sm: 'w-6 h-6',
  md: 'w-8 h-8',
  lg: 'w-12 h-12',
  xl: 'w-16 h-16',
};

export const SimpleAvatar: React.FC<SimpleAvatarProps> = ({
  user,
  email: directEmail,
  username: directUsername,
  size = 'md',
  className,
}) => {
  const userEmail = directEmail || user?.email;
  const userName = directUsername || user?.username;
  const existingAvatarUrl = user?.avatar_url || user?.avatarUrl || user?.profilePicture;

  // Get initials for fallback
  const getInitials = () => {
    if (userName) {
      const words = userName.trim().split(/\s+/);
      if (words.length === 1) {
        return words[0].substring(0, 2).toUpperCase();
      }
      return words
        .slice(0, 2)
        .map(word => word.charAt(0).toUpperCase())
        .join('');
    }
    
    if (userEmail) {
      return userEmail.charAt(0).toUpperCase();
    }
    
    return 'U';
  };

  return (
    <Avatar className={cn(sizeClasses[size], className)}>
      {existingAvatarUrl && (
        <AvatarImage 
          src={existingAvatarUrl} 
          alt={userName || userEmail || 'User'} 
        />
      )}      <AvatarFallback className="bg-literary-navy/10 text-literary-navy">        {getInitials()}      </AvatarFallback>    </Avatar>  );}; 