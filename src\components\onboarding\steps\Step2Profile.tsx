import React, { useState } from "react";
import { useAuth } from "@/contexts/auth";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { User } from "lucide-react";

interface Step2ProfileProps {
  onContinue: () => void;
}

export const Step2Profile: React.FC<Step2ProfileProps> = ({ onContinue }) => {
  const { user, updateProfile } = useAuth();
  const [username, setUsername] = useState(user?.username || "");
  const [isUpdating, setIsUpdating] = useState(false);

  const handleUpdateProfile = async () => {
    if (!username.trim()) return;

    setIsUpdating(true);

    try {
      if (user) {
        await updateProfile({
          ...user,
          username: username.trim(),
        });
      }

      onContinue();
    } catch (error) {
      console.error("Error updating profile:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase();
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col items-center space-y-4">
        <Avatar className="h-24 w-24">
          <AvatarImage
            src={user?.profilePicture || "/placeholder.svg"}
            alt={user?.username || "User"}
          />
          <AvatarFallback className="text-lg">
            {user?.username ? getInitials(user.username) : <User />}
          </AvatarFallback>
        </Avatar>
        {/* Avatar changes not yet implemented */}
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="username">Username</Label>
          <Input
            id="username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            placeholder="Choose a username"
          />
          <p className="text-sm text-muted-foreground">
            This is how other users will see you in stories and comments
          </p>
        </div>

        <div className="pt-4">
          <Button
            onClick={handleUpdateProfile}
            disabled={!username.trim() || isUpdating}
            className="w-full"
          >
            Save Profile
          </Button>
        </div>
      </div>
    </div>
  );
};
