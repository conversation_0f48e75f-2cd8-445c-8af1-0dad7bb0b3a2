import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_ANON_KEY for unsubscribe-story function.');
}

async function getAuthenticatedUser(req: Request, supabase: SupabaseClient) {
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    console.error('Auth error or no user for unsubscribe-story:', authError);
    throw { message: 'User not authenticated or auth error.', status: 401 };
  }
  return user;
}

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  if (req.method !== 'DELETE') {
    return new Response(JSON.stringify({ error: 'Method not allowed. Use DELETE.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 405,
    });
  }

  try {
    const supabase = createClient(supabaseUrl!, supabaseAnonKey!, {
      global: { headers: { Authorization: req.headers.get('Authorization')! } },
      auth: { persistSession: false }
    });

    const user = await getAuthenticatedUser(req, supabase);

    const url = new URL(req.url);
    const storyId = url.searchParams.get('story_id');

    if (!storyId) {
      return new Response(JSON.stringify({ error: 'story_id query parameter is required.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }
    
    // Optional: Check if story exists. While FK constraint would catch invalid story_id on subscribe,
    // it's good practice for a consistent API. For unsubscribe, it's less critical than subscribe.
    // We can let the delete attempt and its RLS handle it.

    const { error, count } = await supabase
      .from('subscriptions')
      .delete({ count: 'exact' }) // Request count of deleted rows
      .eq('story_id', storyId)
      .eq('user_id', user.id); // RLS also enforces this

    if (error) {
      console.error('Supabase delete subscription error:', error);
      return new Response(JSON.stringify({ error: error.message, details: error.details, hint: error.hint }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: (error as any).status || 500,
      });
    }

    if (count === 0) {
      // Subscription not found for this user and story, or RLS prevented delete
      return new Response(JSON.stringify({ error: 'Subscription not found or delete not permitted.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 404,
      });
    }

    return new Response(null, { // 204 No Content
      headers: { ...corsHeaders },
      status: 204,
    });

  } catch (err) {
    console.error('Error processing unsubscribe-story request:', err);
    const statusCode = typeof err.status === 'number' ? err.status : 500;
    return new Response(JSON.stringify({ error: err.message || 'An unexpected error occurred.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: statusCode,
    });
  }
}); 