import React from "react";
import { Badge } from "@/components/ui/badge";
import {
  <PERSON><PERSON><PERSON>,
  BookText,
  Crown,
  Award,
  Feather,
  Pencil,
  Star,
} from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export type BadgeTier =
  | "wordsmith"
  | "storyteller"
  | "authors-guild"
  | "novice"
  | "apprentice"
  | "master";

interface AchievementBadgeProps {
  tier: BadgeTier;
  size?: "sm" | "md" | "lg";
  showLabel?: boolean;
}

export const getBadgeDetails = (tier: BadgeTier) => {
  switch (tier) {
    case "wordsmith":
      return {
        icon: <Pencil />,
        label: "Wordsmith",
        description: "Created at least 5 stories or contributed 100+ words",
        color: "bg-blue-100 text-blue-700 border-blue-200",
      };
    case "storyteller":
      return {
        icon: <BookText />,
        label: "Storyteller",
        description: "Created at least 10 stories or contributed 500+ words",
        color: "bg-purple-100 text-purple-700 border-purple-200",
      };
    case "authors-guild":
      return {
        icon: <Crown />,
        label: "Authors Guild",
        description: "Created 20+ stories or contributed 1000+ words",
        color: "bg-literary-gold/20 text-literary-gold border-literary-gold/30",
      };
    case "novice":
      return {
        icon: <Feather />,
        label: "Novice Writer",
        description: "Created first story",
        color: "bg-green-100 text-green-700 border-green-200",
      };
    case "apprentice":
      return {
        icon: <Award />,
        label: "Apprentice",
        description: "Contributed to 5+ stories",
        color: "bg-amber-100 text-amber-700 border-amber-200",
      };
    case "master":
      return {
        icon: <Star />,
        label: "Master Wordsmith",
        description: "Received 50+ votes on stories",
        color: "bg-rose-100 text-rose-800 border-rose-200",
      };
    default:
      return {
        icon: <BookOpen />,
        label: "Unknown",
        description: "",
        color: "bg-gray-100 text-gray-700 border-gray-200",
      };
  }
};

export const AchievementBadge: React.FC<AchievementBadgeProps> = ({
  tier,
  size = "md",
  showLabel = false,
}) => {
  const { icon, label, description, color } = getBadgeDetails(tier);

  const sizeClasses = {
    sm: "text-xs px-2 py-0.5",
    md: "text-sm px-2.5 py-0.5",
    lg: "text-base px-3 py-1",
  };

  const iconSizes = {
    sm: 12,
    md: 14,
    lg: 16,
  };

  const IconComponent = React.cloneElement(icon, {
    size: iconSizes[size],
    className: "mr-1 inline-block",
  });

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant="outline"
            className={`${color} ${sizeClasses[size]} inline-flex items-center rounded-full font-medium`}
          >
            {IconComponent}
            {showLabel && <span>{label}</span>}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p className="font-medium">{label}</p>
          <p className="text-xs text-muted-foreground">{description}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export const BadgeList: React.FC<{
  badges: BadgeTier[];
  size?: "sm" | "md" | "lg";
  showLabels?: boolean;
}> = ({ badges, size = "md", showLabels = false }) => {
  if (!badges || badges.length === 0) return null;

  return (
    <div className="flex flex-wrap gap-1">
      {badges.map((badge, index) => (
        <AchievementBadge
          key={`${badge}-${index}`}
          tier={badge}
          size={size}
          showLabel={showLabels}
        />
      ))}
    </div>
  );
};
