import { Router } from 'express';
import { PrismaClient } from '@prisma/client';

const router = Router();
const prisma = new PrismaClient();

// Create a new user
router.post('/', async (req, res) => {
  try {
    const { id, username, email, avatar_url, is_admin } = req.body;
    
    // Check if user already exists
    const existingUser = await prisma.user_profiles.findUnique({
      where: { id }
    });
    
    if (existingUser) {
      return res.status(409).json({
        error: 'User already exists',
        userId: id
      });
    }
    
    // Create new user
    const user = await prisma.user_profiles.create({
      data: {
        id,
        username,
        email,
        avatar_url,
        is_admin: is_admin || false
      }
    });
    
    res.status(201).json(user);
  } catch (error) {
    console.error('Error creating user:', error);
    res.status(500).json({
      error: 'Failed to create user',
      details: error.message
    });
  }
});

export default router;
