import { AI_CREDIT_COSTS } from "@/services/aiServices";

export interface WordSuggestionResponse {
  suggestions: string[];
  creditsUsed: number;
}

export const getWordSuggestions = async (
  currentWord: string,
): Promise<string[]> => {
  // This would call an API endpoint in a real implementation
  console.log(`Getting AI suggestions for word: ${currentWord}`);

  // Simulate API call delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Return mock suggestions based on the current word
  const suggestions = [
    "eloquently",
    "quickly",
    "mysteriously",
    "suddenly",
    "gracefully",
  ];

  return suggestions;
};
