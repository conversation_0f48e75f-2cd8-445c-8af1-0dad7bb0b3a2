-- supabase/migrations/0001a_create_user_profiles.sql

CREATE TABLE public.user_profiles (
    id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE, -- This is the FK to auth.users and also PK
    username text NULL,
    avatar_url text NULL,
    is_admin boolean DEFAULT false,
    -- stripe_customer_id, tier, is_ad_free will be added by a later migration (0008)
    created_at timestamptz NOT NULL DEFAULT now(), -- Good practice to have created_at
    updated_at timestamptz NOT NULL DEFAULT now(),
    CONSTRAINT user_profiles_pkey PRIMARY KEY (id),
    CONSTRAINT username_unique UNIQUE (username) -- Use<PERSON><PERSON> should probably be unique
);

COMMENT ON TABLE public.user_profiles IS 'Stores public-facing user profile information, extending auth.users.';
COMMENT ON COLUMN public.user_profiles.id IS 'Links to auth.users.id and is the primary key.';

-- Trigger to update "updated_at" timestamp on row update
-- Assuming trigger_set_timestamp() function is created in 0001_initial_schema.sql
-- If not, or if you want a specific one:
CREATE OR REPLACE FUNCTION public.handle_user_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER on_user_profiles_update
BEFORE UPDATE ON public.user_profiles
FOR EACH ROW
EXECUTE FUNCTION public.handle_user_profiles_updated_at();

-- Enable RLS for user_profiles
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_profiles (basic examples)
CREATE POLICY "Users can view all user profiles"
ON public.user_profiles FOR SELECT USING (true); -- Or restrict as needed

CREATE POLICY "Users can insert their own profile"
ON public.user_profiles FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
ON public.user_profiles FOR UPDATE USING (auth.uid() = id)
WITH CHECK (auth.uid() = id);

-- Optional: You might not want users to delete their profiles directly
-- CREATE POLICY "Users can delete their own profile"
-- ON public.user_profiles FOR DELETE USING (auth.uid() = id);
