import React from "react";
import { Outlet } from "react-router-dom";
import Navbar from "./Navbar";
import MobileNav from "./MobileNav";
import Footer from "./Footer";
import GoogleAd from "@/components/ads/GoogleAd";

const Layout: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-literary-navy text-white px-6 py-3 shadow-lg border-b border-literary-gold/20 sticky top-0 z-50 transition-all duration-300 hover:shadow-xl">
        <Navbar />
        <GoogleAd
          placement="header"
          slot={import.meta.env.VITE_ADSENSE_SLOT_HEADER}
          className="mx-auto mt-2"
        />
      </header>

      <main className="flex-grow container mx-auto px-4 py-6 mb-16 md:mb-0">
        <Outlet />
      </main>

      <MobileNav />
      <Footer />
    </div>
  );
};

export default Layout;
