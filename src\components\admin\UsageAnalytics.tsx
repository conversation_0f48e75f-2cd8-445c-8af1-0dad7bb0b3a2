import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ooter,
} from "@/components/ui/card";
import {
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  <PERSON><PERSON><PERSON>,
  Bar,
} from "recharts";
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ArrowDown,
  ArrowUp,
  BookOpen,
  Users,
  Clock,
  Calendar,
  Activity,
  Layers,
} from "lucide-react";

// Mock data for charts
const usageData = [
  { date: "2023-05-01", stories: 130, words: 5200, users: 180 },
  { date: "2023-05-02", stories: 142, words: 5680, users: 195 },
  { date: "2023-05-03", stories: 152, words: 6080, users: 205 },
  { date: "2023-05-04", stories: 136, words: 5440, users: 198 },
  { date: "2023-05-05", stories: 160, words: 6400, users: 220 },
  { date: "2023-05-06", stories: 172, words: 6880, users: 230 },
  { date: "2023-05-07", stories: 185, words: 7400, users: 245 },
  { date: "2023-05-08", stories: 199, words: 7960, users: 260 },
  { date: "2023-05-09", stories: 215, words: 8600, users: 280 },
  { date: "2023-05-10", stories: 210, words: 8400, users: 275 },
];

const weeklyActivity = [
  { day: "Mon", stories: 125, users: 220 },
  { day: "Tue", stories: 140, users: 230 },
  { day: "Wed", stories: 155, users: 245 },
  { day: "Thu", stories: 135, users: 210 },
  { day: "Fri", stories: 160, users: 260 },
  { day: "Sat", stories: 180, users: 280 },
  { day: "Sun", stories: 170, users: 270 },
];

const storyLengthDistribution = [
  { length: "< 100 words", count: 320 },
  { length: "100-500 words", count: 520 },
  { length: "500-1000 words", count: 280 },
  { length: "1000-2000 words", count: 120 },
  { length: "> 2000 words", count: 60 },
];

const usageMetrics = {
  activeStories: {
    value: "1,245",
    change: "+12.3%",
    trend: "up",
  },
  wordsGenerated: {
    value: "86,520",
    change: "+18.7%",
    trend: "up",
  },
  averageSessionTime: {
    value: "24m 38s",
    change: "+5.2%",
    trend: "up",
  },
  dailyActiveUsers: {
    value: "275",
    change: "+15.5%",
    trend: "up",
  },
};

export const UsageAnalytics: React.FC = () => {
  const [timeFrame, setTimeFrame] = useState("10d");

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Active Stories</CardDescription>
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl">
                {usageMetrics.activeStories.value}
              </CardTitle>
              <div
                className={`flex items-center ${
                  usageMetrics.activeStories.trend === "up"
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {usageMetrics.activeStories.trend === "up" ? (
                  <ArrowUp className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowDown className="h-4 w-4 mr-1" />
                )}
                <span>{usageMetrics.activeStories.change}</span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-xs text-muted-foreground">
              Compared to previous period
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Words Generated</CardDescription>
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl">
                {usageMetrics.wordsGenerated.value}
              </CardTitle>
              <div
                className={`flex items-center ${
                  usageMetrics.wordsGenerated.trend === "up"
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {usageMetrics.wordsGenerated.trend === "up" ? (
                  <ArrowUp className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowDown className="h-4 w-4 mr-1" />
                )}
                <span>{usageMetrics.wordsGenerated.change}</span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-xs text-muted-foreground">
              Compared to previous period
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Average Session Time</CardDescription>
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl">
                {usageMetrics.averageSessionTime.value}
              </CardTitle>
              <div
                className={`flex items-center ${
                  usageMetrics.averageSessionTime.trend === "up"
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {usageMetrics.averageSessionTime.trend === "up" ? (
                  <ArrowUp className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowDown className="h-4 w-4 mr-1" />
                )}
                <span>{usageMetrics.averageSessionTime.change}</span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-xs text-muted-foreground">
              Compared to previous period
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Daily Active Users</CardDescription>
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl">
                {usageMetrics.dailyActiveUsers.value}
              </CardTitle>
              <div
                className={`flex items-center ${
                  usageMetrics.dailyActiveUsers.trend === "up"
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {usageMetrics.dailyActiveUsers.trend === "up" ? (
                  <ArrowUp className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowDown className="h-4 w-4 mr-1" />
                )}
                <span>{usageMetrics.dailyActiveUsers.change}</span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-xs text-muted-foreground">
              Compared to previous period
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Platform Usage Over Time</CardTitle>
            <CardDescription>Stories, words and active users</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center text-sm text-muted-foreground">
              <Calendar className="mr-1 h-4 w-4" />
              <span>Period:</span>
            </div>
            <Select
              defaultValue={timeFrame}
              onValueChange={(value) => setTimeFrame(value)}
            >
              <SelectTrigger className="w-[100px]">
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10d">10 days</SelectItem>
                <SelectItem value="30d">30 days</SelectItem>
                <SelectItem value="3m">3 months</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent className="pl-2">
          <ResponsiveContainer width="100%" height={350}>
            <AreaChart
              data={usageData}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Area
                type="monotone"
                dataKey="stories"
                name="Stories Created"
                stackId="1"
                stroke="#facc15"
                fill="#facc15"
                fillOpacity={0.6}
              />
              <Area
                type="monotone"
                dataKey="users"
                name="Active Users"
                stackId="2"
                stroke="#60a5fa"
                fill="#60a5fa"
                fillOpacity={0.6}
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Weekly Activity</CardTitle>
            <CardDescription>
              Stories created and user activity by day of week
            </CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart
                data={weeklyActivity}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="stories" name="Stories Created" fill="#facc15" />
                <Bar dataKey="users" name="Active Users" fill="#60a5fa" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Story Length Distribution</CardTitle>
            <CardDescription>Count of stories by word length</CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart
                data={storyLengthDistribution}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
                layout="vertical"
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="length" type="category" />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" name="Number of Stories" fill="#a78bfa" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
