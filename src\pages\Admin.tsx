import React, { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { UserManagement } from "@/components/admin/UserManagement";
import { RevenueAnalytics } from "@/components/admin/RevenueAnalytics";
import { UsageAnalytics } from "@/components/admin/UsageAnalytics";
import { SystemMaintenanceSettings } from "@/components/admin/SystemMaintenanceSettings";
import { MembershipGifting } from "@/components/admin/MembershipGifting";
import AiUsageViewer from "@/components/admin/AiUsageViewer";
import { AdminTokenManagement } from "@/components/admin/AdminTokenManagement";
import { toast } from "@/hooks/use-toast";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Coins } from "lucide-react";

const AdminPage: React.FC = () => {
  const { isAuthenticated, isAdmin, systemMaintenance } = useAuth();
  const navigate = useNavigate();

  React.useEffect(() => {
    if (!isAuthenticated) {
      navigate("/login");
      toast({
        title: "Access Denied",
        description: "Please login to access this page",
        variant: "destructive",
      });
    } else if (!isAdmin()) {
      navigate("/");
      toast({
        title: "Access Denied",
        description: "You don't have permission to access the admin panel",
        variant: "destructive",
      });
    }
  }, [isAuthenticated, isAdmin, navigate]);

  if (!isAuthenticated || !isAdmin()) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Manage users and monitor platform analytics
          </p>
        </div>

        {systemMaintenance.enabled && (
          <div className="flex items-center gap-2 bg-red-50 border border-red-200 text-red-700 px-3 py-2 rounded-md">
            <ShieldAlert className="h-5 w-5" />
            <span className="font-medium">System Maintenance Mode Active</span>
          </div>
        )}
      </div>

      <Tabs defaultValue="users" className="space-y-4">
        <TabsList>
          <TabsTrigger value="users">User Management</TabsTrigger>
          <TabsTrigger value="tokens" className="flex gap-1 items-center">
            <Coins className="h-4 w-4" />
            Token Management
          </TabsTrigger>
          <TabsTrigger value="revenue">Revenue Analysis</TabsTrigger>
          <TabsTrigger value="usage">Usage Analytics</TabsTrigger>
          <TabsTrigger value="system" className="flex gap-1 items-center">
            <ShieldAlert className="h-4 w-4" />
            System Settings
          </TabsTrigger>
          <TabsTrigger value="gifting" className="flex gap-1 items-center">
            <Gift className="h-4 w-4" />
            Gift Memberships
          </TabsTrigger>
          <TabsTrigger value="ai" className="flex gap-1 items-center">
            <Brain className="h-4 w-4" />
            AI Usage
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-4">
          <UserManagement />
        </TabsContent>

        <TabsContent value="tokens" className="space-y-4">
          <AdminTokenManagement />
        </TabsContent>

        <TabsContent value="revenue" className="space-y-4">
          <RevenueAnalytics />
        </TabsContent>

        <TabsContent value="usage" className="space-y-4">
          <UsageAnalytics />
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <SystemMaintenanceSettings />
        </TabsContent>

        <TabsContent value="gifting" className="space-y-4">
          <MembershipGifting />
        </TabsContent>

        <TabsContent value="ai" className="space-y-4">
          <AiUsageViewer />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminPage;
