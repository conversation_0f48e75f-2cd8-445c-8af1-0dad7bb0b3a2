[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [badge](../README.md) / BadgeProps

# Interface: BadgeProps

Defined in: [src/components/ui/badge.tsx:26](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/badge.tsx#L26)

## Extends

- `HTMLAttributes`\<`HTMLDivElement`\>.`VariantProps`\<*typeof* [`badgeVariants`](../variables/badgeVariants.md)\>

## Properties

### variant?

> `optional` **variant**: `"default"` \| `"secondary"` \| `"destructive"` \| `"outline"`

Defined in: [src/components/ui/badge.tsx:10](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/badge.tsx#L10)

#### Inherited from

`VariantProps.variant`
