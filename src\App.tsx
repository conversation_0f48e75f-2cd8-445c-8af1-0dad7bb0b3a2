import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import {
  BrowserRouter,
  Routes,
  Route,
  Navigate,
  useLocation,
  useNavigate,
  createBrowserRouter,
  RouterProvider,
} from "react-router-dom";
import { useEffect } from "react";
import { AuthProvider } from "./contexts/auth/AuthContext";
import { StoryProvider } from "./contexts/StoryContext";
import { AdsProvider } from "./contexts/AdsContext";
import { OnboardingProvider } from "./contexts/OnboardingContext";
import { NotificationProvider } from "./contexts/NotificationContext";
import { MaintenanceMessage } from "./components/ui/maintenance-message";
import Layout from "./components/layout/Layout";

// Import ad blocker detection
import { useAdBlockDetection } from "./hooks/useAdBlockDetection";
import AdBlockerDetectedModal from "./components/adblock/AdBlockerDetectedModal";

// Import cookie consent
import { useCookieConsent } from "./hooks/useCookieConsent";
import CookieConsentBanner from "./components/cookies/CookieConsentBanner";

// Import all page components
import Index from "@/pages/Index";
import Login from "@/pages/Login";
import Register from "@/pages/Register";
import Story from "@/pages/Story";
import Dashboard from "@/pages/Dashboard";
import Gallery from "@/pages/Gallery";
import CreateStory from "@/pages/CreateStory";
import Profile from "@/pages/Profile";
import Pricing from "@/pages/Pricing";
import AdFreeSubscription from "@/pages/AdFreeSubscription";
import Terms from "@/pages/Terms";
import Admin from "@/pages/Admin";
import NotFound from "@/pages/NotFound";
import ContributionModesDemo from "@/pages/ContributionModesDemo";
import MessagingDemo from "@/pages/MessagingDemo";

// Import auth hooks
import { useAuth } from "./contexts/auth";

const queryClient = new QueryClient();

// ScrollToTop component to force scroll position reset on route change
function ScrollToTop() {
  const { pathname } = useLocation();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
}

// Auth check components
interface AuthGuardProps {
  children: React.ReactNode;
}

function RequireAuth({ children }: AuthGuardProps) {
  const { isAuthenticated } = useAuth();
  const location = useLocation();

  if (!isAuthenticated) {
    // Redirect to login page but remember where they were trying to go
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
}

function RequireAdmin({ children }: AuthGuardProps) {
  const { isAuthenticated, isAdmin } = useAuth();
  const location = useLocation();

  if (!isAuthenticated || !isAdmin()) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
}

// Wrapper component for app routing and context providers
const AppWithProviders = () => {
  // Add ad blocker detection
  const { 
    detectionResult, 
    shouldShowModal, 
    hideModal, 
    redetect 
  } = useAdBlockDetection();

  // Add cookie consent
  const {
    needsBanner: needsCookieBanner,
    hideBanner: hideCookieBanner,
  } = useCookieConsent();

  return (
    <>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route index element={<Index />} />
          <Route path="login" element={<Login />} />
          <Route path="register" element={<Register />} />
          <Route path="story/:id" element={<Story />} />
          <Route path="gallery" element={<Gallery />} />
          <Route path="pricing" element={<Pricing />} />
          <Route
            path="subscription"
            element={<Navigate to="/pricing" replace />}
          />
          <Route path="ad-free" element={<AdFreeSubscription />} />
          <Route path="terms" element={<Terms />} />
          <Route path="contribution-modes-demo" element={<ContributionModesDemo />} />
          <Route path="messaging-demo" element={<MessagingDemo />} />

          {/* Protected routes */}
          <Route
            path="dashboard"
            element={
              <RequireAuth>
                <Dashboard />
              </RequireAuth>
            }
          />
          <Route
            path="create-story"
            element={
              <RequireAuth>
                <CreateStory />
              </RequireAuth>
            }
          />
          <Route
            path="profile"
            element={
              <RequireAuth>
                <Profile />
              </RequireAuth>
            }
          />
          <Route
            path="admin"
            element={
              <RequireAdmin>
                <Admin />
              </RequireAdmin>
            }
          />

          <Route path="*" element={<NotFound />} />
        </Route>
      </Routes>

      {/* Cookie Consent Banner */}
      <CookieConsentBanner
        isVisible={needsCookieBanner}
        onConsentGiven={hideCookieBanner}
      />

      {/* Ad Blocker Detection Modal */}
      {detectionResult && (
        <AdBlockerDetectedModal
          isOpen={shouldShowModal}
          onOpenChange={hideModal}
          detectionResult={detectionResult}
        />
      )}
    </>
  );
};

// App component with all providers and routing
const App = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <BrowserRouter
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true,
          }}
        >
          <ScrollToTop />
          <AuthProvider>
            <OnboardingProvider>
              <StoryProvider>
                <NotificationProvider>
                  <AdsProvider>
                    <MaintenanceMessage />
                    <Toaster />
                    <Sonner />
                    <AppWithProviders />
                  </AdsProvider>
                </NotificationProvider>
              </StoryProvider>
            </OnboardingProvider>
          </AuthProvider>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
