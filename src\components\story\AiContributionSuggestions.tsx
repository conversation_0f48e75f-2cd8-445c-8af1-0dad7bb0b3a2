import React, { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { <PERSON>rk<PERSON>, Loader2 } from "lucide-react";
import {
  generateAiSentences,
  generateAiParagraphs,
} from "@/services/aiServices";
import { useSubscription } from "@/hooks/use-subscription";
import { useCreditOperation } from "@/hooks/use-credit-operation";
import { AiContributionSuggestionsProps } from "@/types";

interface GenerationError extends Error {
  message: string;
}

const AiContributionSuggestions: React.FC<AiContributionSuggestionsProps> = ({
  storyId,
  mode,
  words,
  onSelectContribution,
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const { toast } = useToast();
  const { credits } = useSubscription();
  const { handleCreditOperation, isUsingCredits } = useCreditOperation();
  const creditCost = mode === "sentence" ? 5 : 10;

  const handleGenerate = async () => {
    if (credits < creditCost) {
      toast({
        title: "Insufficient Credits",
        description: `You need ${creditCost} credits to generate a ${mode}.`,
        variant: "destructive",
      });
      return;
    }

    try {
      const success = await handleCreditOperation(
        mode === "sentence" ? "sentenceGeneration" : "paragraphGeneration",
      );
      if (!success) {
        throw new Error("Failed to use credits");
      }
      generateContribution();
    } catch (error) {
      console.error("Error using credits:", error);
      toast({
        title: "Error",
        description: "Failed to use credits for generation.",
        variant: "destructive",
      });
    }
  };

  const { mutate: generateContribution, isPending } = useMutation({
    mutationFn: async () => {
      // Get context from words
      const context = words.map((w) => w.word).join(" ");

      // Generate the contribution
      if (mode === "sentence") {
        return await generateAiSentences(storyId, context);
      } else {
        return await generateAiParagraphs(storyId, context);
      }
    },
    onSuccess: (data) => {
      if (
        mode === "sentence" &&
        "sentences" in data &&
        data.sentences?.length > 0
      ) {
        setSuggestions(data.sentences);
      } else if (
        mode === "paragraph" &&
        "paragraphs" in data &&
        data.paragraphs?.length > 0
      ) {
        setSuggestions(data.paragraphs);
      } else {
        toast({
          title: "No suggestions",
          description: `No ${mode} suggestions could be generated.`,
        });
      }
      setIsGenerating(false);
    },
    onError: (error: GenerationError) => {
      toast({
        title: "Generation Failed",
        description: error.message || `Failed to generate ${mode}.`,
        variant: "destructive",
      });
      setIsGenerating(false);
    },
  });

  const handleSelectSuggestion = (suggestion: string) => {
    onSelectContribution(suggestion);
    setSuggestions([]);
  };

  if (suggestions.length > 0) {
    return (
      <div className="mt-3 space-y-3">
        <div className="text-sm font-medium text-gray-700">AI Suggestions:</div>
        <div className="space-y-2">
          {suggestions.map((suggestion, index) => (
            <div
              key={index}
              className="bg-literary-cream/30 border border-literary-cream p-3 rounded-md hover:bg-literary-cream/50 cursor-pointer transition-colors"
              onClick={() => handleSelectSuggestion(suggestion)}
            >
              <div className="flex items-start">
                <Sparkles className="h-4 w-4 text-literary-gold mr-2 mt-1 flex-shrink-0" />
                <p>{suggestion}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="mt-2">
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={(e) => {
          e.preventDefault();
          handleGenerate();
        }}
        disabled={isPending || credits < creditCost || isUsingCredits}
        className="flex items-center gap-1 bg-literary-cream/50 hover:bg-literary-cream"
      >
        {isPending || isUsingCredits ? (
          <Loader2 size={14} className="mr-1 animate-spin" />
        ) : (
          <Sparkles size={14} className="mr-1 text-literary-gold" />
        )}
        Magic {mode === "sentence" ? "Sentence" : "Paragraph"} ({creditCost}{" "}
        credits)
      </Button>
    </div>
  );
};

export default AiContributionSuggestions;
