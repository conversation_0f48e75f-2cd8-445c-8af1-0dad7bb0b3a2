import { supabase, realtime } from '@/lib/supabase';

// TODO: Refactor this service to use Supabase real-time features instead of Firebase.
// All Firebase-specific logic has been removed. Implement Supabase real-time as needed.

// Define ContributionMode enum to match Prisma schema
export enum ContributionMode {
  WORD = "WORD",
  MULTI_WORD = "MULTI_WORD",
  SENTENCE = "SENTENCE",
  PARAGRAPH = "PARAGRAPH",
}

// Types for real-time events
export enum RealTimeEventType {
  CONTRIBUTION_ADDED = "contribution_added",
  USER_JOINED = "user_joined",
  USER_LEFT = "user_left",
  USER_TYPING = "user_typing",
  USER_STOPPED_TYPING = "user_stopped_typing",
  TURN_CHANGED = "turn_changed",
  STORY_COMPLETED = "story_completed",
  STORY_ARCHIVED = "story_archived",
}

export type RealTimeEvent = 
  | { type: 'contribution_added'; data: ContributionEvent }
  | { type: 'user_joined'; data: UserSessionEvent }
  | { type: 'user_left'; data: UserSessionEvent }
  | { type: 'user_typing'; data: UserSessionEvent }
  | { type: 'user_stopped_typing'; data: UserSessionEvent }
  | { type: 'turn_changed'; data: TurnChangeEvent }
  | { type: 'story_updated'; data: any };

export interface ContributionEvent {
  id: string;
  content: string;
  author: {
    id: string;
    username: string;
    avatar_url?: string;
  };
  created_at: string;
  position: number;
  special_type?: 'gotcha' | 'reverse' | 'golden' | null;
  token_cost?: number;
  storyId: string;
}

export interface UserSessionEvent {
  userId: string;
  username: string;
  isTyping: boolean;
  isOnline: boolean;
  lastActivity: Date;
}

export interface TurnChangeEvent {
  currentTurn: string;
  previousTurn?: string;
  storyId: string;
}

// Class to manage real-time updates for stories
export class RealTimeService {
  private static instance: RealTimeService;
  private subscriptions = new Map<string, any>();
  private userSessions = new Map<string, UserSessionEvent>();

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  public static getInstance(): RealTimeService {
    if (!RealTimeService.instance) {
      RealTimeService.instance = new RealTimeService();
    }
    return RealTimeService.instance;
  }

  /**
   * Subscribe to real-time updates for a specific story
   * @param storyId ID of the story to subscribe to
   * @param callback Function to call when updates occur
   * @returns Unsubscribe function
   */
  public subscribeToStory(
    storyId: string,
    callback: (event: RealTimeEvent) => void
  ): () => void {
    // Clean up existing subscription for this story
    this.unsubscribeFromStory(storyId);

    console.log(`Setting up real-time subscription for story: ${storyId}`);

    // Use the existing realtime helper from supabase.ts
    const subscription = realtime.subscribeToStory(storyId, (event) => {
      console.log('Real-time event received:', event);
      callback(event);
    });

    // Store subscription for cleanup
    this.subscriptions.set(storyId, subscription);

    // Return unsubscribe function
    return () => this.unsubscribeFromStory(storyId);
  }

  /**
   * Register a user session for a story
   * @param storyId ID of the story
   * @param userId ID of the user
   * @param username Username of the user
   * @returns Session ID
   */
  public async registerUserSession(
    storyId: string,
    userId: string,
    username?: string
  ): Promise<string> {
    try {
      console.log(`Registering user session for story ${storyId}, user ${userId}`);
      
      // Insert or update user session in database
      const { data, error } = await supabase
        .from('user_session')
        .upsert({
          story_id: storyId,
          user_id: userId,
          username: username,
          is_online: true,
          is_typing: false,
          last_active: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error('Error registering user session:', error);
        throw error;
      }

      // Store in local cache
      const sessionEvent: UserSessionEvent = {
        userId,
        username: username || 'Anonymous',
        isTyping: false,
        isOnline: true,
        lastActivity: new Date(),
      };
      
      this.userSessions.set(`${storyId}-${userId}`, sessionEvent);
      
      return data.id;
    } catch (error) {
      console.error('Failed to register user session:', error);
      throw error;
    }
  }

  /**
   * Update a user's session status
   * @param storyId ID of the story
   * @param userId ID of the user
   * @param isTyping Whether the user is typing
   * @param isOnline Whether the user is online
   */
  public async updateUserStatus(
    storyId: string,
    userId: string,
    isTyping?: boolean,
    isOnline?: boolean
  ): Promise<void> {
    try {
      const updateData: any = {
        user_id: userId,
        story_id: storyId,
        updated_at: new Date().toISOString(),
      };

      // Add typing status if provided
      if (typeof isTyping === 'boolean') {
        updateData.is_typing = isTyping;
        updateData.typing_updated_at = new Date().toISOString();
      }

      // Add online status if provided
      if (typeof isOnline === 'boolean') {
        updateData.is_online = isOnline;
        updateData.last_seen = new Date().toISOString();
        
        // If going offline, also stop typing
        if (!isOnline) {
          updateData.is_typing = false;
        }
      }

      // Update or insert user status (using the correct table name)
      const { error } = await supabase
        .from('user_session')
        .upsert(updateData, {
          onConflict: 'user_id,story_id'
        });

      if (error) {
        console.error('Error updating user status:', error);
        throw error;
      }

      console.log('User status updated:', { userId, storyId, isTyping, isOnline });
    } catch (error) {
      console.error('Failed to update user status:', error);
      throw error;
    }
  }

  /**
   * Broadcast a new contribution to all subscribers
   * @param storyId ID of the story
   * @param contribution Contribution data
   */
  public async broadcastContribution(
    storyId: string,
    contribution: ContributionEvent
  ): Promise<void> {
    try {
      console.log(`Broadcasting contribution for story ${storyId}:`, contribution);

      // Validate and handle token costs for special actions
      if (contribution.special_type && contribution.token_cost && contribution.token_cost > 0) {
        console.log(`Special action detected: ${contribution.special_type}, cost: ${contribution.token_cost}`);
        
        // Check user's token balance and spend tokens
        const { data: tokenResult, error: tokenError } = await supabase
          .rpc('spend_user_tokens', {
            p_user_id: contribution.author.id,
            p_amount: contribution.token_cost,
            p_story_id: storyId,
            p_description: `${contribution.special_type} special action`
          });

        if (tokenError) {
          console.error('Error spending tokens:', tokenError);
          throw new Error(`Failed to process tokens: ${tokenError.message}`);
        }

        if (tokenResult && tokenResult.length > 0 && !tokenResult[0].success) {
          console.error('Token spending failed:', tokenResult[0].error_message);
          throw new Error(tokenResult[0].error_message || 'Insufficient tokens');
        }

        console.log(`Tokens spent successfully. New balance: ${tokenResult[0]?.new_balance}`);
      }

      // Insert contribution into database - this will trigger real-time updates via Supabase
      const { data: insertData, error } = await supabase
        .from('contributions')
        .insert({
          story_id: storyId,
          user_id: contribution.author.id,
          content: contribution.content,
          position: contribution.position,
          special_type: contribution.special_type,
          token_cost: contribution.token_cost,
        })
        .select()
        .single();

      if (error) {
        console.error('Error broadcasting contribution:', error);
        
        // If contribution insert fails but tokens were spent, we should refund
        if (contribution.special_type && contribution.token_cost && contribution.token_cost > 0) {
          try {
            await supabase.rpc('add_user_tokens', {
              p_user_id: contribution.author.id,
              p_amount: contribution.token_cost,
              p_description: `Refund for failed ${contribution.special_type} action`
            });
            console.log('Tokens refunded due to contribution failure');
          } catch (refundError) {
            console.error('Failed to refund tokens:', refundError);
          }
        }
        
        throw error;
      }

      console.log('Contribution broadcasted successfully:', insertData);
    } catch (error) {
      console.error('Failed to broadcast contribution:', error);
      throw error;
    }
  }

  /**
   * Broadcast a turn change to all subscribers
   * @param storyId ID of the story
   * @param currentTurn User ID whose turn it is now
   * @param previousTurn User ID whose turn it was before
   */
  public async broadcastTurnChange(
    storyId: string,
    currentTurn: string,
    previousTurn?: string
  ): Promise<void> {
    try {
      console.log(`Broadcasting turn change for story ${storyId}: ${previousTurn} -> ${currentTurn}`);

      // Update story turn information in database
      const { error } = await supabase
        .from('stories')
        .update({
          current_turn: currentTurn,
          updated_at: new Date().toISOString(),
        })
        .eq('id', storyId);

      if (error) {
        console.error('Error broadcasting turn change:', error);
        throw error;
      }

      console.log('Turn change broadcasted successfully');
    } catch (error) {
      console.error('Failed to broadcast turn change:', error);
      throw error;
    }
  }

  /**
   * Get all active user sessions for a story
   * @param storyId ID of the story
   * @returns Promise resolving to array of user sessions
   */
  public async getActiveUserSessions(storyId: string): Promise<UserSessionEvent[]> {
    try {
      console.log(`Fetching active user sessions for story: ${storyId}`);

      const { data, error } = await supabase
        .from('user_session')
        .select('*')
        .eq('story_id', storyId)
        .eq('is_online', true)
        .order('last_active', { ascending: false });

      if (error) {
        console.error('Error fetching user sessions:', error);
        throw error;
      }

      return (data || []).map(session => ({
        userId: session.user_id,
        username: session.username || 'Anonymous',
        isTyping: session.is_typing || false,
        isOnline: session.is_online || false,
        lastActivity: new Date(session.last_active),
      }));
    } catch (error) {
      console.error('Failed to get active user sessions:', error);
      return [];
    }
  }

  /**
   * Clean up all sessions and resources for a specific story
   * @param storyId ID of the story to clean up
   */
  public async cleanupStorySessions(storyId: string): Promise<void> {
    try {
      console.log(`Cleaning up sessions for story: ${storyId}`);

      // Unsubscribe from real-time updates
      this.unsubscribeFromStory(storyId);

      // Mark user sessions as offline in database
      const { error } = await supabase
        .from('user_session')
        .update({
          is_online: false,
          is_typing: false,
          last_active: new Date().toISOString(),
        })
        .eq('story_id', storyId);

      if (error) {
        console.error('Error cleaning up story sessions:', error);
      }

      // Clear local cache for this story
      for (const [key] of this.userSessions) {
        if (key.startsWith(`${storyId}-`)) {
          this.userSessions.delete(key);
        }
      }

      console.log('Story sessions cleaned up successfully');
    } catch (error) {
      console.error('Failed to cleanup story sessions:', error);
    }
  }

  /**
   * Clean up resources when service is no longer needed
   */
  public cleanup(): void {
    console.log('Cleaning up RealTimeService');

    // Unsubscribe from all real-time channels
    for (const [storyId] of this.subscriptions) {
      this.unsubscribeFromStory(storyId);
    }

    // Clear all caches
    this.userSessions.clear();
    this.subscriptions.clear();
  }

  /**
   * Unsubscribe from real-time updates for a specific story
   * @param storyId ID of the story
   */
  private unsubscribeFromStory(storyId: string): void {
    const subscription = this.subscriptions.get(storyId);
    if (subscription) {
      console.log(`Unsubscribing from story: ${storyId}`);
      subscription.unsubscribe();
      this.subscriptions.delete(storyId);
    }
  }
}

// Export singleton instance
export const realTimeService = RealTimeService.getInstance();
