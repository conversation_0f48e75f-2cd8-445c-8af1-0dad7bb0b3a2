import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  CardD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CustomProgress } from "@/components/ui/custom-progress";
import { Step1Welcome } from "./steps/Step1Welcome";
import { Step2Profile } from "./steps/Step2Profile";
import { Step3Discover } from "./steps/Step3Discover";
import { Step4Create } from "./steps/Step4Create";
import { useAuth } from "@/contexts/auth";
import { Check, ChevronRight, Loader2 } from "lucide-react";

interface OnboardingWizardProps {
  onComplete: () => void;
  initialStep?: number;
}

const MAX_STEPS = 4;

export const OnboardingWizard: React.FC<OnboardingWizardProps> = ({
  onComplete,
  initialStep = 1,
}) => {
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [isLoading, setIsLoading] = useState(false);
  const { user, updateProfile } = useAuth();

  const progress = (currentStep / MAX_STEPS) * 100;

  const handleNext = () => {
    if (currentStep < MAX_STEPS) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    setIsLoading(true);

    try {
      // Update user profile to mark onboarding as complete
      if (user) {
        await updateProfile({
          ...user,
          // This is where you'd set a field in the user profile to track onboarding completion
        });
      }

      onComplete();
    } catch (error) {
      console.error("Error completing onboarding:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <Step1Welcome onContinue={handleNext} />;
      case 2:
        return <Step2Profile onContinue={handleNext} />;
      case 3:
        return <Step3Discover onContinue={handleNext} />;
      case 4:
        return (
          <Step4Create onContinue={handleNext} onComplete={handleComplete} />
        );
      default:
        return <Step1Welcome onContinue={handleNext} />;
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>Getting Started with Word by Word Story</span>
          <span className="text-sm font-normal text-muted-foreground">
            Step {currentStep} of {MAX_STEPS}
          </span>
        </CardTitle>
        <CardDescription>
          Complete this quick setup to get the most out of your experience
        </CardDescription>
        <CustomProgress value={progress} className="mt-2" height={2} />
      </CardHeader>

      <CardContent className="py-4">{renderStep()}</CardContent>

      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={handleBack}
          disabled={currentStep === 1 || isLoading}
        >
          Back
        </Button>

        <Button onClick={handleNext} disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing
            </>
          ) : currentStep < MAX_STEPS ? (
            <>
              Next
              <ChevronRight className="ml-2 h-4 w-4" />
            </>
          ) : (
            <>
              Complete
              <Check className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};
