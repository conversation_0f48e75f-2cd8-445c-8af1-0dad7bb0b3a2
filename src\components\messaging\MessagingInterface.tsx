import React, { useState, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';
import StoryThread from './StoryThread';
import MessageInput from './MessageInput';
import ParticipantsSidebar from './ParticipantsSidebar';
import StoryCompilationView from './StoryCompilationView';
import InviteButton from './InviteButton';
import TokenStatusBar from './TokenStatusBar';
import { useParticipantManager } from './ParticipantManager';
import { useAuth } from '@/contexts/auth/hooks';
import { useToast } from '@/hooks/use-toast';
import { useRealTimeMessages } from '@/hooks/useRealTimeMessages';
import { useUserTokens } from '@/hooks/useUserTokens';
import { TokenPurchaseDialog } from '@/components/tokens/TokenPurchaseDialog';
import { TokenTransferDialog } from '@/components/tokens/TokenTransferDialog';
import { TransactionHistoryDialog } from '@/components/tokens/TransactionHistoryDialog';

// Types
export interface Story {
  id: string;
  title: string;
  description?: string;
  contribution_mode: 'word' | 'sentence' | 'paragraph';
  words_per_contribution: number;
  max_contributions: number;
  current_contribution_count: number;
  creator_id: string;
  status: 'ACTIVE' | 'COMPLETED' | 'PAUSED';
}

export interface Contribution {
  id: string;
  content: string;
  author: {
    id: string;
    username: string;
    avatar_url?: string;
  };
  created_at: string;
  position: number;
  special_type?: 'gotcha' | 'reverse' | 'golden' | null;
  token_cost?: number;
  reactions?: Array<{
    type: 'heart' | 'star' | 'laugh';
    count: number;
    users: string[];
  }>;
}

export interface Participant {
  id: string;
  username: string;
  avatar_url?: string;
  role: 'creator' | 'contributor' | 'viewer';
  isOnline: boolean;
  lastSeen?: Date;
  contributionCount: number;
  joinedAt: Date;
  isTyping?: boolean;
}

export interface MessagingInterfaceProps {
  story: Story;
  contributions: Contribution[];
  participants: Participant[];
  userTokens?: number;
  onSendContribution: (content: string, specialType?: 'gotcha' | 'reverse' | 'golden') => Promise<void>;
  onInviteUser?: () => void;
  onKickUser?: (userId: string) => void;
  onPromoteUser?: (userId: string) => void;
  onMuteUser?: (userId: string) => void;
  onShareStory?: () => void;
  onExportStory?: (format: 'txt' | 'pdf' | 'markdown') => void;
  className?: string;
}

export const MessagingInterface: React.FC<MessagingInterfaceProps> = ({
  story,
  contributions: initialContributions,
  participants: initialParticipants,
  userTokens = 0, // This will be overridden by real token data
  onSendContribution,
  onInviteUser,
  onKickUser,
  onPromoteUser,
  onMuteUser,
  onShareStory,
  onExportStory,
  className,
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [showParticipants, setShowParticipants] = useState(false);
  const [showStoryView, setShowStoryView] = useState(false);
  
  // Token dialog states
  const [showTokenPurchase, setShowTokenPurchase] = useState(false);
  const [showTokenTransfer, setShowTokenTransfer] = useState(false);
  const [showTransactionHistory, setShowTransactionHistory] = useState(false);

  // Enhanced participant management
  const {
    participants: managedParticipants,
    invitations,
    isLoading: participantLoading,
    processInvitation,
    acceptInvitation,
    removeParticipant,
    updateParticipantRole,
    fetchParticipants,
  } = useParticipantManager({
    storyId: story.id,
    creatorId: story.creator_id,
    onParticipantsUpdate: (participants) => {
      console.log('Participants updated:', participants);
    },
    onInvitationsUpdate: (invitations) => {
      console.log('Invitations updated:', invitations);
    },
  });

  // Use managed participants if available, otherwise fall back to props
  const currentParticipants = managedParticipants.length > 0 ? managedParticipants : initialParticipants;

  // Token management
  const {
    tokenBalance: realTokenBalance,
    canAfford,
    getActionCost,
    processSpecialAction,
    isLoading: tokenLoading,
    error: tokenError,
    refreshBalance
  } = useUserTokens({ autoFetch: true });

  // Use real token balance instead of prop
  const currentTokenBalance = realTokenBalance;

  // Real-time messaging hook
  const {
    messages: realTimeMessages,
    typingUsers: realTimeTypingUsers,
    isConnected,
    connectionState,
    sendMessage: sendRealTimeMessage,
    startTyping,
    stopTyping,
  } = useRealTimeMessages({
    storyId: story.id,
    enabled: story.status === 'ACTIVE',
    onMessageReceived: (message) => {
      console.log('New real-time message received:', message);
      // Refresh token balance after receiving a message in case it was a special action
      refreshBalance();
    },
    onUserJoined: (user) => {
      console.log('User joined:', user);
      // Refresh participants when someone joins
      fetchParticipants();
    },
    onUserLeft: (user) => {
      console.log('User left:', user);
    },
    onTypingUpdate: (users) => {
      console.log('Typing users updated:', users);
    },
  });

  // Convert real-time messages to contribution format
  const combinedContributions = React.useMemo(() => {
    const realTimeContribs = realTimeMessages.map(msg => ({
      id: msg.id,
      content: msg.content,
      author: msg.author,
      created_at: msg.timestamp.toISOString(),
      position: msg.position,
      special_type: msg.specialType,
      token_cost: msg.tokenCost,
      reactions: [],
    }));

    // Combine with initial contributions, removing duplicates
    const allContribs = [...initialContributions];
    
    realTimeContribs.forEach(rtc => {
      if (!allContribs.some(existing => existing.id === rtc.id)) {
        allContribs.push(rtc);
      }
    });

    return allContribs.sort((a, b) => a.position - b.position);
  }, [realTimeMessages, initialContributions]);

  // Convert real-time typing users to the expected format
  const typingUsers = React.useMemo(() => {
    return realTimeTypingUsers.map(user => ({
      id: user.userId,
      username: user.username,
      avatar_url: undefined, // Could be enhanced to include avatar
    }));
  }, [realTimeTypingUsers]);

  // Get current user participant info
  const currentUserParticipant = currentParticipants.find(p => p.id === user?.id);

  // Enhanced message sending with token validation and special actions
  const handleSendMessage = useCallback(async (content: string, specialType?: 'gotcha' | 'reverse' | 'golden') => {
    try {
      // If it's a special action, validate and process tokens first
      if (specialType && user?.id) {
        const cost = getActionCost(specialType);
        
        // Check if user can afford the action
        if (!canAfford(specialType)) {
          toast({
            title: 'Insufficient Tokens',
            description: `You need ${cost} tokens for this action, but you only have ${currentTokenBalance}.`,
            variant: 'destructive',
          });
          return;
        }

        // Show confirmation for expensive actions
        if (cost >= 5) {
          const confirmed = window.confirm(
            `This ${specialType} action will cost ${cost} tokens. Continue?`
          );
          if (!confirmed) {
            return;
          }
        }
      }

      // Send via real-time system if connected
      if (isConnected) {
        await sendRealTimeMessage(content, specialType);
        
        // Refresh token balance after successful send
        await refreshBalance();
      } else {
        // Fallback to original method (for demo or when offline)
        await onSendContribution(content, specialType);
      }

    } catch (error) {
      console.error('Error sending message:', error);
      
      // Show appropriate error message
      let errorMessage = 'Your message could not be sent. Please try again.';
      
      if (error instanceof Error) {
        if (error.message.includes('token') || error.message.includes('Insufficient')) {
          errorMessage = error.message;
        }
      }
      
      toast({
        title: 'Failed to send message',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  }, [
    user?.id, 
    getActionCost, 
    canAfford, 
    currentTokenBalance, 
    isConnected, 
    sendRealTimeMessage, 
    onSendContribution, 
    refreshBalance, 
    toast
  ]);

  // Typing handlers
  const handleTypingStart = useCallback(() => {
    startTyping();
  }, [startTyping]);

  const handleTypingStop = useCallback(() => {
    stopTyping();
  }, [stopTyping]);

  // Enhanced invitation handler
  const handleInviteUser = useCallback(async (inviteData: any) => {
    try {
      await processInvitation(inviteData);
      
      // Refresh participants after successful invitation
      await fetchParticipants();
      
    } catch (error) {
      console.error('Error processing invitation:', error);
      throw error; // Let the InviteButton handle the error display
    }
  }, [processInvitation, fetchParticipants]);

  // Enhanced participant management handlers
  const handleKickUser = useCallback((userId: string) => {
    removeParticipant(userId).catch(error => {
      console.error('Error removing participant:', error);
      toast({
        title: 'Failed to remove participant',
        description: error instanceof Error ? error.message : 'Please try again.',
        variant: 'destructive',
      });
    });
  }, [removeParticipant, toast]);

  const handlePromoteUser = useCallback((userId: string) => {
    const participant = currentParticipants.find(p => p.id === userId);
    const newRole = participant?.role === 'viewer' ? 'contributor' : 'viewer';
    updateParticipantRole(userId, newRole).catch(error => {
      console.error('Error updating participant role:', error);
      toast({
        title: 'Failed to update participant role',
        description: error instanceof Error ? error.message : 'Please try again.',
        variant: 'destructive',
      });
    });
  }, [currentParticipants, updateParticipantRole, toast]);

  const handleMuteUser = useCallback((userId: string) => {
    // Implement mute functionality
    toast({
      title: 'User muted',
      description: 'User has been muted temporarily.',
    });
  }, [toast]);

  // Token-related handlers
  const handleTokenPurchase = useCallback(() => {
    setShowTokenPurchase(true);
  }, []);

  const handleTokenTransfer = useCallback(() => {
    setShowTokenTransfer(true);
  }, []);

  const handleSubscriptionManage = useCallback(() => {
    // Navigate to subscription page
    window.location.href = '/subscription';
  }, []);

  const handleTokenHistory = useCallback(() => {
    setShowTransactionHistory(true);
  }, []);

  // Token dialog handlers
  const handlePurchaseComplete = useCallback((tokens: number) => {
    refreshBalance();
    toast({
      title: 'Purchase Successful',
      description: `You've successfully purchased ${tokens} tokens!`,
    });
  }, [refreshBalance, toast]);

  const handleTransferComplete = useCallback((amount: number, recipient: string) => {
    refreshBalance();
    toast({
      title: 'Transfer Successful',
      description: `Successfully transferred ${amount} tokens to ${recipient}`,
    });
  }, [refreshBalance, toast]);

  // Show token error if there's an issue loading tokens
  useEffect(() => {
    if (tokenError) {
      toast({
        title: 'Token System Error',
        description: 'Unable to load your token balance. Some features may not work properly.',
        variant: 'destructive',
      });
    }
  }, [tokenError, toast]);

  // Responsive layout handling
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => setIsMobile(window.innerWidth < 1024);
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Close sidebars on mobile when story view is opened
  useEffect(() => {
    if (showStoryView && isMobile) {
      setShowParticipants(false);
    }
  }, [showStoryView, isMobile]);

  return (
    <div className={cn('flex h-screen bg-background', className)}>
      {/* Connection Status Indicator */}
      {connectionState !== 'connected' && story.status === 'ACTIVE' && (
        <div className="absolute top-2 left-1/2 transform -translate-x-1/2 z-50">
          <div 
            data-testid="online-indicator"
            className={cn(
              'px-3 py-1 rounded-full text-xs font-medium',
              connectionState === 'connecting' && 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
              connectionState === 'disconnected' && 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
              connectionState === 'error' && 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
            )}>
            {connectionState === 'connecting' && 'Connecting...'}
            {connectionState === 'disconnected' && 'Disconnected'}
            {connectionState === 'error' && 'Connection Error'}
          </div>
        </div>
      )}

      {/* Main Story Thread */}
      <div className="flex-1 flex flex-col min-w-0">
        <StoryThread
          storyId={story.id}
          storyTitle={story.title}
          contributions={combinedContributions}
          participants={currentParticipants}
          currentUserId={user?.id || ''}
          typingUsers={typingUsers}
          onToggleStoryView={() => setShowStoryView(!showStoryView)}
          onToggleParticipants={() => setShowParticipants(!showParticipants)}
          onAuthorClick={(authorId) => {
            // Could open user profile or show user info
            console.log('User clicked:', authorId);
            // For now, just show participants sidebar on mobile
            if (window.innerWidth < 1024) {
              setShowParticipants(true);
            }
          }}
          // Add invite button to the header via children or additional props
          headerActions={
            <InviteButton
              storyId={story.id}
              storyTitle={story.title}
              onInvite={handleInviteUser}
              variant="icon"
              size="sm"
              disabled={participantLoading}
              className="ml-2"
            />
          }
        />

        {/* Message Input */}
        {story.status === 'ACTIVE' && (
          <MessageInput
            storyId={story.id}
            contributionMode={story.contribution_mode}
            wordsPerContribution={story.words_per_contribution}
            userTokens={currentTokenBalance}
            disabled={!currentUserParticipant || currentUserParticipant.role === 'viewer' || tokenLoading}
            onSendMessage={handleSendMessage}
            onTypingStart={handleTypingStart}
            onTypingStop={handleTypingStop}
            onInviteContributor={() => {
              // Open invite dialog via a different trigger
              console.log('Invite contributor from input');
            }}
          />
        )}

        {story.status !== 'ACTIVE' && (
          <div className="p-4 border-t bg-muted/30 text-center">
            <p className="text-muted-foreground text-sm">
              {story.status === 'COMPLETED' && 'This story has been completed.'}
              {story.status === 'PAUSED' && 'This story is currently paused.'}
            </p>
          </div>
        )}
      </div>

      {/* Enhanced Participants Sidebar */}
      <ParticipantsSidebar
        participants={currentParticipants}
        currentUserId={user?.id || ''}
        storyCreatorId={story.creator_id}
        isOpen={showParticipants}
        onClose={() => setShowParticipants(false)}
        onInviteUser={() => {
          // This could trigger the invite dialog
          console.log('Invite user from sidebar');
        }}
        onKickUser={handleKickUser}
        onPromoteUser={handlePromoteUser}
        onMuteUser={handleMuteUser}
      />

      {/* Story Compilation View */}
      <StoryCompilationView
        storyTitle={story.title}
        storyDescription={story.description}
        contributions={combinedContributions}
        isOpen={showStoryView}
        onClose={() => setShowStoryView(false)}
        onBackToThread={() => setShowStoryView(false)}
        onShare={onShareStory}
        onExport={onExportStory}
      />

      {/* Mobile Overlay */}
      {isMobile && (showParticipants || showStoryView) && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => {
            setShowParticipants(false);
            setShowStoryView(false);
          }}
        />
      )}

      {/* Token Status Bar */}
      <TokenStatusBar
        onTokenPurchase={handleTokenPurchase}
        onTokenTransfer={handleTokenTransfer}
        onSubscriptionManage={handleSubscriptionManage}
        onTokenHistory={handleTokenHistory}
        showTransactionHistory={true}
      />

      {/* Token Purchase Dialog */}
      <TokenPurchaseDialog
        isOpen={showTokenPurchase}
        onClose={() => setShowTokenPurchase(false)}
        currentBalance={currentTokenBalance}
        onPurchaseComplete={handlePurchaseComplete}
      />

      {/* Token Transfer Dialog */}
      <TokenTransferDialog
        isOpen={showTokenTransfer}
        onClose={() => setShowTokenTransfer(false)}
        currentBalance={currentTokenBalance}
        onTransferComplete={handleTransferComplete}
      />

      {/* Transaction History Dialog */}
      <TransactionHistoryDialog
        isOpen={showTransactionHistory}
        onClose={() => setShowTransactionHistory(false)}
      />
    </div>
  );
};

export default MessagingInterface;