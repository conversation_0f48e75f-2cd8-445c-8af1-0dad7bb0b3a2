import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Handles image loading errors by providing a local fallback
 * rather than relying on external placeholder services
 */
export const handleImageError = (
  event: React.SyntheticEvent<HTMLImageElement, Event>,
) => {
  // Prevent the browser from trying to load a potentially external placeholder
  event.currentTarget.onerror = null;

  // Use our local placeholder SVG
  event.currentTarget.src = "/placeholder.svg";
};

/**
 * Safely parses JSON from storage or other sources
 * Handles cases where the input might already be an object
 * or could be invalid JSON
 *
 * @param jsonString - The string to parse or object to return
 * @param fallback - Default value to return if parsing fails
 * @returns The parsed object or fallback value
 */
export function safeJsonParse<T>(
  jsonString: string | null | unknown,
  fallback: T,
): T {
  if (!jsonString) return fallback;

  // Already an object (not a string), return as is
  if (typeof jsonString === "object") {
    return jsonString as unknown as T;
  }

  try {
    return JSON.parse(jsonString as string) as T;
  } catch (error) {
    console.warn("Error parsing JSON from storage:", error);
    return fallback;
  }
}
