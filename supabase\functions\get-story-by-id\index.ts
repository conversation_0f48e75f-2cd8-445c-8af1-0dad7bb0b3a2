import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_ANON_KEY');
}

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabase = createClient(supabaseUrl!, supabaseAnonKey!, {
      global: { headers: { Authorization: req.headers.get('Authorization')! } },
      auth: { persistSession: false }
    });

    const url = new URL(req.url);
    const storyId = url.searchParams.get('id');

    if (!storyId) {
      return new Response(JSON.stringify({ error: 'Story ID is required as a query parameter (e.g., ?id=your-story-id).' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400, // Bad Request
      });
    }

    // Validate if storyId is a UUID (optional, but good practice)
    // Regex for UUID: /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/
    // if (!/^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(storyId)) {
    //   return new Response(JSON.stringify({ error: 'Invalid Story ID format.' }), {
    //     headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    //     status: 400, // Bad Request
    //   });
    // }

    const { data, error } = await supabase
      .from('stories')
      .select('*') // Select all columns for the story
      .eq('id', storyId)
      .single(); // Expects a single row, returns error if 0 or more than 1

    if (error) {
      console.error('Supabase select error:', error);
      // If RLS prevents access, or if the ID doesn't exist, Supabase might return PగRST001 or similar
      // .single() returns an error if no rows are found or more than one row is found.
      // PostgREST error 'PGRST116' indicates that the query returned no rows when one was expected.
      if (error.code === 'PGRST116') {
         return new Response(JSON.stringify({ error: 'Story not found or access denied.' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 404, // Not Found
        });
      }
      return new Response(JSON.stringify({ error: error.message, details: error.details, hint: error.hint }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500, // Internal Server Error for other errors
      });
    }
    
    // If data is null and no error, it might also mean not found due to RLS (though .single() should error out)
    // Explicitly checking if data is null after .single() is a good safeguard if Supabase behavior changes.
    if (!data) {
        return new Response(JSON.stringify({ error: 'Story not found or access denied.' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 404, // Not Found
        });
    }

    return new Response(JSON.stringify(data), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200, // OK
    });

  } catch (err) {
    console.error('Error processing request:', err);
    return new Response(JSON.stringify({ error: err.message || 'An unexpected error occurred.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
}); 