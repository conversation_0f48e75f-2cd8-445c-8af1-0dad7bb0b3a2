import * as React from "react";
import { cn } from "@/lib/utils";

interface CustomProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value?: number;
  max?: number;
  height?: number;
}

const CustomProgress = React.forwardRef<HTMLDivElement, CustomProgressProps>(
  ({ className, value = 0, max = 100, height = 4, ...props }, ref) => {
    const percentage =
      value && max ? Math.min(Math.max(0, (value / max) * 100), 100) : 0;

    return (
      <div
        ref={ref}
        className={cn(
          "relative w-full overflow-hidden rounded-full bg-secondary",
          className,
        )}
        style={{ height: `${height}px` }}
        role="progressbar"
        aria-valuemin={0}
        aria-valuemax={max}
        aria-valuenow={value}
        {...props}
      >
        <div
          className="h-full w-full flex-1 bg-primary transition-all"
          style={{ width: `${percentage}%` }}
        />
      </div>
    );
  },
);

CustomProgress.displayName = "CustomProgress";

export { CustomProgress };
