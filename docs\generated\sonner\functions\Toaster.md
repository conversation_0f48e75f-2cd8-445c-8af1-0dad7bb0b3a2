[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [sonner](../README.md) / Toaster

# Function: Toaster()

> **Toaster**(`__namedParameters`): `Element`

Defined in: [src/components/ui/sonner.tsx:6](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/sonner.tsx#L6)

## Parameters

### \_\_namedParameters

`ToasterProps` & `RefAttributes`\<`HTMLElement`\>

## Returns

`Element`
