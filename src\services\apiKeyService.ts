import { supabase } from '@/lib/supabase';
import { cacheService } from './cacheService';
import crypto from 'crypto';

// API Tier definitions
export enum ApiTier {
  FREE = 'free',
  PREMIUM = 'premium',
  ENTERPRISE = 'enterprise'
}

// Tier limits configuration
export const TIER_LIMITS = {
  [ApiTier.FREE]: {
    requestsPerHour: 100,
    requestsPerDay: 1000,
    requestsPerMonth: 10000,
    features: ['Basic API access', 'Read-only operations', 'Community support'],
    maxApiKeys: 1,
    rateLimitWindow: 3600, // 1 hour in seconds
    cacheTTL: 300, // 5 minutes
    maxRequestSize: 1024 * 1024, // 1MB
    allowedMethods: ['GET']
  },
  [ApiTier.PREMIUM]: {
    requestsPerHour: 10000,
    requestsPerDay: 100000,
    requestsPerMonth: 1000000,
    features: [
      'Full API access',
      'CRUD operations',
      'Real-time updates',
      'Priority caching',
      'Analytics dashboard',
      'Email support'
    ],
    maxApiKeys: 5,
    rateLimitWindow: 3600,
    cacheTTL: 60, // 1 minute
    maxRequestSize: 10 * 1024 * 1024, // 10MB
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE']
  },
  [ApiTier.ENTERPRISE]: {
    requestsPerHour: 100000,
    requestsPerDay: 1000000,
    requestsPerMonth: 10000000,
    features: [
      'Unlimited API access',
      'Custom integrations',
      'Dedicated support',
      'SLA guarantees',
      'Custom rate limits',
      'Webhook callbacks',
      'Bulk operations'
    ],
    maxApiKeys: 50,
    rateLimitWindow: 3600,
    cacheTTL: 30, // 30 seconds
    maxRequestSize: 100 * 1024 * 1024, // 100MB
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
  }
} as const;

// API Key interface
export interface ApiKey {
  id: string;
  userId: string;
  name: string;
  description?: string;
  keyHash: string;
  keyPreview: string;
  tier: ApiTier;
  isActive: boolean;
  allowedOrigins?: string[];
  allowedIPs?: string[];
  createdAt: Date;
  lastUsedAt?: Date;
  expiresAt?: Date;
  totalRequests: number;
  monthlyRequests: number;
  billingStatus: 'active' | 'suspended' | 'cancelled';
  subscriptionId?: string;
}

// API Usage tracking interface
export interface ApiUsage {
  keyId: string;
  timestamp: Date;
  endpoint: string;
  method: string;
  responseTime: number;
  cached: boolean;
  bytes: number;
  statusCode: number;
  userAgent?: string;
  ip?: string;
  cost: number;
}

// Rate limit result interface
export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  error?: string;
}

// Usage statistics interface
export interface UsageStats {
  totalRequests: number;
  requestsThisPeriod: number;
  averageResponseTime: number;
  cacheHitRate: number;
  totalCost: number;
  topEndpoints: Array<{
    endpoint: string;
    requests: number;
    avgResponseTime: number;
  }>;
  errorRate: number;
  bandwidthUsed: number;
}

export class ApiKeyService {
  
  // Generate a new API key
  static generateApiKey(): { key: string; hash: string; preview: string } {
    // Generate a secure random key
    const keyBytes = crypto.randomBytes(32);
    const key = `wws_${keyBytes.toString('hex')}`;
    
    // Create hash for storage
    const hash = crypto.createHash('sha256').update(key).digest('hex');
    
    // Create preview (first 8 chars + last 4 chars)
    const preview = `${key.substring(0, 12)}...${key.substring(key.length - 4)}`;
    
    return { key, hash, preview };
  }
  
  // Create a new API key in the database
  static async createApiKey(data: {
    userId: string;
    name: string;
    description?: string;
    tier: ApiTier;
    allowedOrigins?: string[];
    allowedIPs?: string[];
    expiresAt?: Date;
    subscriptionId?: string;
  }): Promise<{ apiKey: ApiKey; plainKey: string } | null> {
    try {
      const { key, hash, preview } = this.generateApiKey();
      
      const { data: apiKey, error } = await supabase
        .from('api_keys')
        .insert({
          user_id: data.userId,
          name: data.name,
          description: data.description,
          key_hash: hash,
          key_preview: preview,
          tier: data.tier,
          is_active: true,
          allowed_origins: data.allowedOrigins,
          allowed_ips: data.allowedIPs,
          expires_at: data.expiresAt?.toISOString(),
          subscription_id: data.subscriptionId,
          total_requests: 0,
          monthly_requests: 0,
          billing_status: 'active'
        })
        .select()
        .single();
      
      if (error) {
        console.error('❌ Error creating API key:', error);
        return null;
      }
      
      // Convert database format to ApiKey interface
      const formattedApiKey: ApiKey = {
        id: apiKey.id,
        userId: apiKey.user_id,
        name: apiKey.name,
        description: apiKey.description,
        keyHash: apiKey.key_hash,
        keyPreview: apiKey.key_preview,
        tier: apiKey.tier as ApiTier,
        isActive: apiKey.is_active,
        allowedOrigins: apiKey.allowed_origins,
        allowedIPs: apiKey.allowed_ips,
        createdAt: new Date(apiKey.created_at),
        lastUsedAt: apiKey.last_used_at ? new Date(apiKey.last_used_at) : undefined,
        expiresAt: apiKey.expires_at ? new Date(apiKey.expires_at) : undefined,
        totalRequests: apiKey.total_requests,
        monthlyRequests: apiKey.monthly_requests,
        billingStatus: apiKey.billing_status,
        subscriptionId: apiKey.subscription_id
      };
      
      return { apiKey: formattedApiKey, plainKey: key };
      
    } catch (error) {
      console.error('❌ Error in createApiKey:', error);
      return null;
    }
  }
  
  // Validate an API key
  static async validateApiKey(key: string): Promise<{ valid: boolean; apiKey?: ApiKey; error?: string }> {
    try {
      if (!key || !key.startsWith('wws_')) {
        return { valid: false, error: 'Invalid API key format' };
      }
      
             // Check cache first
       const cacheKey = `api_key_validation:${key}`;
       const cached = await cacheService.get<{ valid: boolean; apiKey?: ApiKey; error?: string }>(cacheKey);
       if (cached) {
         return cached;
       }
      
      // Hash the key to compare with stored hash
      const hash = crypto.createHash('sha256').update(key).digest('hex');
      
      const { data: apiKey, error } = await supabase
        .from('api_keys')
        .select('*')
        .eq('key_hash', hash)
        .eq('is_active', true)
        .single();
      
      if (error || !apiKey) {
        const result = { valid: false, error: 'API key not found or inactive' };
        cacheService.set(cacheKey, result, 300); // Cache negative results for 5 minutes
        return result;
      }
      
      // Check if key is expired
      if (apiKey.expires_at && new Date(apiKey.expires_at) < new Date()) {
        const result = { valid: false, error: 'API key has expired' };
        cacheService.set(cacheKey, result, 300);
        return result;
      }
      
      // Convert to ApiKey interface
      const formattedApiKey: ApiKey = {
        id: apiKey.id,
        userId: apiKey.user_id,
        name: apiKey.name,
        description: apiKey.description,
        keyHash: apiKey.key_hash,
        keyPreview: apiKey.key_preview,
        tier: apiKey.tier as ApiTier,
        isActive: apiKey.is_active,
        allowedOrigins: apiKey.allowed_origins,
        allowedIPs: apiKey.allowed_ips,
        createdAt: new Date(apiKey.created_at),
        lastUsedAt: apiKey.last_used_at ? new Date(apiKey.last_used_at) : undefined,
        expiresAt: apiKey.expires_at ? new Date(apiKey.expires_at) : undefined,
        totalRequests: apiKey.total_requests,
        monthlyRequests: apiKey.monthly_requests,
        billingStatus: apiKey.billing_status,
        subscriptionId: apiKey.subscription_id
      };
      
      const result = { valid: true, apiKey: formattedApiKey };
      cacheService.set(cacheKey, result, 600); // Cache valid results for 10 minutes
      return result;
      
    } catch (error) {
      console.error('❌ Error validating API key:', error);
      return { valid: false, error: 'Validation service error' };
    }
  }
  
  // Check rate limits for an API key
  static async checkRateLimit(keyId: string, tier: ApiTier): Promise<RateLimitResult> {
    try {
      const limits = TIER_LIMITS[tier];
      const window = limits.rateLimitWindow;
      const maxRequests = limits.requestsPerHour;
      
      const cacheKey = `rate_limit:${keyId}`;
      const now = new Date();
      const windowStart = new Date(now.getTime() - window * 1000);
      
             // Get current usage from cache
       let usage = (await cacheService.get<{ count: number; resetTime: Date }>(cacheKey)) || { count: 0, resetTime: new Date(now.getTime() + window * 1000) };
      
      // If window has reset, start fresh
      if (now >= usage.resetTime) {
        usage = { count: 0, resetTime: new Date(now.getTime() + window * 1000) };
      }
      
      // Check if limit exceeded
      if (usage.count >= maxRequests) {
        return {
          allowed: false,
          remaining: 0,
          resetTime: usage.resetTime,
          error: `Rate limit exceeded. Maximum ${maxRequests} requests per hour allowed for ${tier} tier.`
        };
      }
      
      // Increment usage
      usage.count++;
      cacheService.set(cacheKey, usage, window);
      
      return {
        allowed: true,
        remaining: maxRequests - usage.count,
        resetTime: usage.resetTime
      };
      
    } catch (error) {
      console.error('❌ Error checking rate limit:', error);
      return {
        allowed: false,
        remaining: 0,
        resetTime: new Date(),
        error: 'Rate limit service error'
      };
    }
  }
  
  // Record API usage
  static async recordUsage(usage: ApiUsage): Promise<void> {
    try {
      // Store in database
      await supabase
        .from('api_usage')
        .insert({
          key_id: usage.keyId,
          timestamp: usage.timestamp.toISOString(),
          endpoint: usage.endpoint,
          method: usage.method,
          response_time: usage.responseTime,
          cached: usage.cached,
          bytes: usage.bytes,
          status_code: usage.statusCode,
          user_agent: usage.userAgent,
          ip: usage.ip,
          cost: usage.cost
        });
      
             // Update API key usage counters
       await supabase.rpc('increment_api_key_usage', {
         key_id: usage.keyId,
         last_used: usage.timestamp.toISOString()
       });
      
    } catch (error) {
      console.error('❌ Error recording API usage:', error);
    }
  }
  
  // Get usage statistics
  static async getUsageStats(keyId: string, period: 'hour' | 'day' | 'month'): Promise<UsageStats | null> {
    try {
      const now = new Date();
      let startTime: Date;
      
      switch (period) {
        case 'hour':
          startTime = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case 'day':
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
      }
      
      const { data: usage, error } = await supabase
        .from('api_usage')
        .select('*')
        .eq('key_id', keyId)
        .gte('timestamp', startTime.toISOString());
      
      if (error) {
        console.error('❌ Error fetching usage stats:', error);
        return null;
      }
      
      if (!usage || usage.length === 0) {
        return {
          totalRequests: 0,
          requestsThisPeriod: 0,
          averageResponseTime: 0,
          cacheHitRate: 0,
          totalCost: 0,
          topEndpoints: [],
          errorRate: 0,
          bandwidthUsed: 0
        };
      }
      
      // Calculate statistics
      const totalRequests = usage.length;
      const averageResponseTime = usage.reduce((sum, u) => sum + u.response_time, 0) / totalRequests;
      const cachedRequests = usage.filter(u => u.cached).length;
      const cacheHitRate = cachedRequests / totalRequests;
      const totalCost = usage.reduce((sum, u) => sum + u.cost, 0);
      const errorRequests = usage.filter(u => u.status_code >= 400).length;
      const errorRate = errorRequests / totalRequests;
      const bandwidthUsed = usage.reduce((sum, u) => sum + u.bytes, 0);
      
      // Top endpoints
      const endpointStats = usage.reduce((acc, u) => {
        if (!acc[u.endpoint]) {
          acc[u.endpoint] = { requests: 0, totalResponseTime: 0 };
        }
        acc[u.endpoint].requests++;
        acc[u.endpoint].totalResponseTime += u.response_time;
        return acc;
      }, {} as Record<string, { requests: number; totalResponseTime: number }>);
      
             const topEndpoints = Object.entries(endpointStats)
         .map(([endpoint, stats]) => ({
           endpoint,
           requests: (stats as { requests: number; totalResponseTime: number }).requests,
           avgResponseTime: (stats as { requests: number; totalResponseTime: number }).totalResponseTime / (stats as { requests: number; totalResponseTime: number }).requests
         }))
         .sort((a, b) => b.requests - a.requests)
         .slice(0, 10);
      
      return {
        totalRequests,
        requestsThisPeriod: totalRequests,
        averageResponseTime,
        cacheHitRate,
        totalCost,
        topEndpoints,
        errorRate,
        bandwidthUsed
      };
      
    } catch (error) {
      console.error('❌ Error calculating usage stats:', error);
      return null;
    }
  }
  
  // Rotate API key
  static async rotateApiKey(keyId: string, userId: string, reason: string): Promise<{ success: boolean; newKey?: string; error?: string }> {
    try {
      // Get existing key
      const { data: existingKey, error: fetchError } = await supabase
        .from('api_keys')
        .select('*')
        .eq('id', keyId)
        .eq('user_id', userId)
        .single();
      
      if (fetchError || !existingKey) {
        return { success: false, error: 'API key not found' };
      }
      
      // Generate new key
      const { key, hash, preview } = this.generateApiKey();
      
      // Update the key in database
      const { error: updateError } = await supabase
        .from('api_keys')
        .update({
          key_hash: hash,
          key_preview: preview,
          updated_at: new Date().toISOString()
        })
        .eq('id', keyId);
      
      if (updateError) {
        console.error('❌ Error rotating API key:', updateError);
        return { success: false, error: 'Failed to rotate key' };
      }
      
      // Log the rotation
      await supabase
        .from('api_key_rotations')
        .insert({
          key_id: keyId,
          rotated_by: userId,
          reason,
          rotated_at: new Date().toISOString()
        });
      
      // Clear cache
      cacheService.delete(`api_key_validation:${existingKey.key_hash}`);
      
      return { success: true, newKey: key };
      
    } catch (error) {
      console.error('❌ Error in rotateApiKey:', error);
      return { success: false, error: 'Service error' };
    }
  }
  
  // Deactivate API key
  static async deactivateApiKey(keyId: string, reason: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('api_keys')
        .update({
          is_active: false,
          deactivated_at: new Date().toISOString(),
          deactivation_reason: reason
        })
        .eq('id', keyId);
      
      if (error) {
        console.error('❌ Error deactivating API key:', error);
        return false;
      }
      
      return true;
      
    } catch (error) {
      console.error('❌ Error in deactivateApiKey:', error);
      return false;
    }
  }
  
  // Generate billing report
  static async generateBillingReport(keyId: string, month: number, year: number): Promise<any> {
    try {
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0);
      
      const { data: usage, error } = await supabase
        .from('api_usage')
        .select('*')
        .eq('key_id', keyId)
        .gte('timestamp', startDate.toISOString())
        .lte('timestamp', endDate.toISOString());
      
      if (error) {
        console.error('❌ Error generating billing report:', error);
        return null;
      }
      
      const totalCost = usage?.reduce((sum, u) => sum + u.cost, 0) || 0;
      const totalRequests = usage?.length || 0;
      
      return {
        period: { month, year },
        totalRequests,
        totalCost,
        breakdown: usage?.map(u => ({
          date: u.timestamp,
          endpoint: u.endpoint,
          requests: 1,
          cost: u.cost
        })) || []
      };
      
    } catch (error) {
      console.error('❌ Error in generateBillingReport:', error);
      return null;
    }
  }
} 