// Declare global window interface augmentation
declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

import React, { useEffect, useRef, useState } from "react";
import { useAds } from "@/contexts/AdsContext";

interface GoogleAdProps {
  placement: string;
  slot: string;
  format?: string;
  responsive?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

const GoogleAd: React.FC<GoogleAdProps> = ({
  placement,
  slot,
  format = "auto",
  responsive = true,
  className = "",
  style = {},
}) => {
  const { shouldShowAd } = useAds();
  const adRef = useRef<HTMLDivElement>(null);
  const [hasError, setHasError] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (!shouldShowAd(placement) || !slot || hasError) return;

    // Check if container has width
    const checkVisibility = () => {
      if (adRef.current) {
        const rect = adRef.current.getBoundingClientRect();
        const hasWidth = rect.width > 0;
        setIsVisible(hasWidth);
        return hasWidth;
      }
      return false;
    };

    // Initial check
    if (!checkVisibility()) {
      // If not visible, wait for next frame
      const rafId = requestAnimationFrame(() => {
        checkVisibility();
      });
      return () => cancelAnimationFrame(rafId);
    }

    // Only push ads if container is visible
    if (isVisible) {
      try {
        (window.adsbygoogle = window.adsbygoogle || []).push({});
      } catch (error) {
        console.error("AdSense error:", error);
        setHasError(true);
      }
    }

    // Listen for resize events
    const handleResize = () => {
      checkVisibility();
    };
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [shouldShowAd, placement, slot, hasError, isVisible]);

  if (!shouldShowAd(placement) || !slot || hasError) {
    return null;
  }

  return (
    <div 
      ref={adRef}
      className={`google-ad-container ${className}`}
      style={{ minHeight: '90px', ...style }}
      data-testid="google-ad"
    >
      {isVisible && (
        <ins
          className="adsbygoogle"
          style={{ display: "block", ...style }}
          data-ad-client={import.meta.env.VITE_ADSENSE_CLIENT_ID}
          data-ad-slot={slot}
          data-ad-format={format}
          data-full-width-responsive={responsive}
          data-ad-test={import.meta.env.DEV ? "on" : undefined}
        />
      )}
    </div>
  );
};

export default GoogleAd;
