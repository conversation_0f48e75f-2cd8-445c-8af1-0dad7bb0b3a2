/**
 * Content Report Interface Component
 * Provides reporting functionality and content security validation feedback
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/auth';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Flag,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  Eye,
  MoreVertical,
  Clock,
  User,
  MessageSquare,
} from 'lucide-react';
import {
  validateContent,
  submitContentReport,
  getContentReportStatus,
  type ContentValidationResult,
  type ContentViolation,
  type ContentReport,
} from '@/services/contentSecurityService';

interface ContentReportInterfaceProps {
  contentType: 'story' | 'contribution' | 'comment' | 'message';
  contentId: string;
  contentPreview?: string;
  isOpen: boolean;
  onClose: () => void;
  onReported?: () => void;
}

export const ContentReportInterface: React.FC<ContentReportInterfaceProps> = ({
  contentType,
  contentId,
  contentPreview,
  isOpen,
  onClose,
  onReported,
}) => {
  const { user } = useAuth();
  const { toast } = useToast();

  // State management
  const [reportReason, setReportReason] = useState('');
  const [reportDetails, setReportDetails] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [reportStatus, setReportStatus] = useState<{
    hasReports: boolean;
    reportCount: number;
    isUnderReview: boolean;
  }>({ hasReports: false, reportCount: 0, isUnderReview: false });

  // Load report status when dialog opens
  useEffect(() => {
    if (isOpen && contentId) {
      loadReportStatus();
    }
  }, [isOpen, contentId]);

  const loadReportStatus = async () => {
    try {
      const status = await getContentReportStatus(contentType, contentId);
      setReportStatus(status);
    } catch (error) {
      console.error('Error loading report status:', error);
    }
  };

  const handleSubmitReport = async () => {
    if (!reportReason.trim()) {
      toast({
        title: 'Reason required',
        description: 'Please select a reason for reporting this content.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await submitContentReport(
        contentType,
        contentId,
        reportReason,
        reportDetails.trim() || undefined
      );

      if (result.success) {
        await loadReportStatus();
        onReported?.();
        onClose();
        setReportReason('');
        setReportDetails('');
      } else {
        toast({
          title: 'Failed to submit report',
          description: result.error || 'Please try again later.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error submitting report:', error);
      toast({
        title: 'Error submitting report',
        description: 'Please try again later.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const reportReasons = [
    { value: 'inappropriate_content', label: 'Inappropriate Content' },
    { value: 'spam', label: 'Spam or Repetitive Content' },
    { value: 'harassment', label: 'Harassment or Bullying' },
    { value: 'personal_information', label: 'Contains Personal Information' },
    { value: 'copyright', label: 'Copyright Violation' },
    { value: 'off_topic', label: 'Off-Topic or Irrelevant' },
    { value: 'security_threat', label: 'Security Threat or Malicious Content' },
    { value: 'other', label: 'Other' },
  ];

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Flag className="w-5 h-5 text-red-500" />
            Report Content
          </DialogTitle>
          <DialogDescription>
            Help us maintain a safe community by reporting inappropriate content.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Current Report Status */}
          {reportStatus.hasReports && (
            <Alert className="border-amber-200 bg-amber-50">
              <Info className="w-4 h-4 text-amber-600" />
              <AlertDescription className="text-amber-800">
                This content has already been reported {reportStatus.reportCount} time(s)
                {reportStatus.isUnderReview && ' and is under review'}.
              </AlertDescription>
            </Alert>
          )}

          {/* Content Preview */}
          {contentPreview && (
            <Card className="bg-muted/30">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Content Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground line-clamp-3">
                  {contentPreview}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Report Reason */}
          <div className="space-y-2">
            <Label htmlFor="report-reason">Reason for reporting *</Label>
            <Select value={reportReason} onValueChange={setReportReason}>
              <SelectTrigger>
                <SelectValue placeholder="Select a reason" />
              </SelectTrigger>
              <SelectContent>
                {reportReasons.map((reason) => (
                  <SelectItem key={reason.value} value={reason.value}>
                    {reason.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Additional Details */}
          <div className="space-y-2">
            <Label htmlFor="report-details">Additional details (optional)</Label>
            <Textarea
              id="report-details"
              placeholder="Provide any additional context that might help our moderation team..."
              value={reportDetails}
              onChange={(e) => setReportDetails(e.target.value)}
              rows={3}
              maxLength={500}
            />
            <p className="text-xs text-muted-foreground">
              {reportDetails.length}/500 characters
            </p>
          </div>

          {/* Reporting Guidelines */}
          <Alert className="border-blue-200 bg-blue-50">
            <Shield className="w-4 h-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              <p className="font-medium mb-1">Reporting Guidelines:</p>
              <ul className="text-sm space-y-1">
                <li>• Reports are reviewed by our moderation team</li>
                <li>• False reports may result in account restrictions</li>
                <li>• We investigate all reports within 24 hours</li>
              </ul>
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmitReport} 
            disabled={isSubmitting || !reportReason}
            className="bg-red-600 hover:bg-red-700"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Report'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

/**
 * Content Validation Indicator Component
 * Shows validation results inline with content
 */
interface ContentValidationIndicatorProps {
  content: string;
  contentType?: 'story' | 'contribution' | 'comment' | 'message';
  showDetails?: boolean;
  className?: string;
}

export const ContentValidationIndicator: React.FC<ContentValidationIndicatorProps> = ({
  content,
  contentType = 'contribution',
  showDetails = false,
  className,
}) => {
  const [validation, setValidation] = useState<ContentValidationResult | null>(null);

  useEffect(() => {
    if (content.trim()) {
      const result = validateContent(content, contentType);
      setValidation(result);
    } else {
      setValidation(null);
    }
  }, [content, contentType]);

  if (!validation || validation.recommendation === 'allow') {
    return null;
  }

  const getIndicatorStyle = (recommendation: string) => {
    switch (recommendation) {
      case 'warn':
        return 'border-yellow-300 bg-yellow-50 text-yellow-800';
      case 'review':
        return 'border-orange-300 bg-orange-50 text-orange-800';
      case 'block':
        return 'border-red-300 bg-red-50 text-red-800';
      default:
        return 'border-gray-300 bg-gray-50 text-gray-800';
    }
  };

  const getIcon = (recommendation: string) => {
    switch (recommendation) {
      case 'warn':
        return <AlertTriangle className="w-4 h-4" />;
      case 'review':
        return <Eye className="w-4 h-4" />;
      case 'block':
        return <XCircle className="w-4 h-4" />;
      default:
        return <Info className="w-4 h-4" />;
    }
  };

  const getMessage = (recommendation: string) => {
    switch (recommendation) {
      case 'warn':
        return 'Content may need review';
      case 'review':
        return 'Content requires moderation review';
      case 'block':
        return 'Content violates community guidelines';
      default:
        return 'Content flagged for review';
    }
  };

  return (
    <Alert className={`${getIndicatorStyle(validation.recommendation)} ${className || ''}`}>
      {getIcon(validation.recommendation)}
      <AlertDescription>
        <div>
          <p className="font-medium">{getMessage(validation.recommendation)}</p>
          {showDetails && validation.violations.length > 0 && (
            <div className="mt-2 space-y-1">
              {validation.violations.map((violation, index) => (
                <p key={index} className="text-sm">
                  • {violation.description}
                </p>
              ))}
            </div>
          )}
          {validation.score > 0 && (
            <p className="text-sm mt-1">
              Risk Score: {validation.score}/100
            </p>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
};

/**
 * Quick Report Button Component
 * Inline button for quick reporting
 */
interface QuickReportButtonProps {
  contentType: 'story' | 'contribution' | 'comment' | 'message';
  contentId: string;
  contentPreview?: string;
  compact?: boolean;
  className?: string;
}

export const QuickReportButton: React.FC<QuickReportButtonProps> = ({
  contentType,
  contentId,
  contentPreview,
  compact = false,
  className,
}) => {
  const [showReportDialog, setShowReportDialog] = useState(false);

  if (compact) {
    return (
      <>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowReportDialog(true)}
          className={`h-8 w-8 p-0 text-muted-foreground hover:text-red-600 ${className || ''}`}
        >
          <Flag className="w-4 h-4" />
        </Button>
        
        <ContentReportInterface
          contentType={contentType}
          contentId={contentId}
          contentPreview={contentPreview}
          isOpen={showReportDialog}
          onClose={() => setShowReportDialog(false)}
        />
      </>
    );
  }

  return (
    <>
      <DropdownMenuItem
        onClick={() => setShowReportDialog(true)}
        className="text-red-600 focus:text-red-600"
      >
        <Flag className="w-4 h-4 mr-2" />
        Report Content
      </DropdownMenuItem>
      
      <ContentReportInterface
        contentType={contentType}
        contentId={contentId}
        contentPreview={contentPreview}
        isOpen={showReportDialog}
        onClose={() => setShowReportDialog(false)}
      />
    </>
  );
};

/**
 * Content Security Status Component
 * Shows overall security status of content
 */
interface ContentSecurityStatusProps {
  contentType: string;
  contentId: string;
  showReportCount?: boolean;
  className?: string;
}

export const ContentSecurityStatus: React.FC<ContentSecurityStatusProps> = ({
  contentType,
  contentId,
  showReportCount = true,
  className,
}) => {
  const [reportStatus, setReportStatus] = useState<{
    hasReports: boolean;
    reportCount: number;
    isUnderReview: boolean;
  } | null>(null);

  useEffect(() => {
    const loadStatus = async () => {
      try {
        const status = await getContentReportStatus(contentType, contentId);
        setReportStatus(status);
      } catch (error) {
        console.error('Error loading content security status:', error);
      }
    };

    loadStatus();
  }, [contentType, contentId]);

  if (!reportStatus || !reportStatus.hasReports) {
    return null;
  }

  return (
    <div className={`flex items-center gap-2 ${className || ''}`}>
      {reportStatus.isUnderReview ? (
        <Badge variant="destructive" className="flex items-center gap-1">
          <Clock className="w-3 h-3" />
          Under Review
        </Badge>
      ) : (
        <Badge variant="secondary" className="flex items-center gap-1">
          <Flag className="w-3 h-3" />
          Reported
        </Badge>
      )}
      
      {showReportCount && reportStatus.reportCount > 1 && (
        <span className="text-xs text-muted-foreground">
          ({reportStatus.reportCount} reports)
        </span>
      )}
    </div>
  );
};