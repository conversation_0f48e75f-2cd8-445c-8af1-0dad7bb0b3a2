import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { Loader2, CheckCircle2, <PERSON>Check } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/auth";

interface OTPVerificationProps {
  phoneOrEmail: string;
  type: "phone" | "email";
  onSuccess?: () => void;
  onCancel?: () => void;
}

const OTPVerification: React.FC<OTPVerificationProps> = ({
  phoneOrEmail,
  type,
  onSuccess,
  onCancel,
}) => {
  const [value, setValue] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [verified, setVerified] = useState(false);

  const { verifyOTP, resendOTP } = useAuth();

  const handleVerify = async () => {
    if (value.length !== 6) {
      toast({
        title: "Invalid OTP",
        description: "Please enter a valid 6-digit code",
        variant: "destructive",
      });
      return;
    }

    setIsVerifying(true);
    try {
      const success = await verifyOTP(phoneOrEmail, value);

      if (success) {
        setVerified(true);
        toast({
          title: "Verification successful",
          description: "Your identity has been verified",
        });

        if (onSuccess) {
          setTimeout(() => {
            onSuccess();
          }, 1500);
        }
      } else {
        toast({
          title: "Verification failed",
          description: "The code you entered is incorrect. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Verification error",
        description: "An error occurred during verification. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResend = async () => {
    setIsResending(true);
    try {
      await resendOTP(phoneOrEmail, type);

      toast({
        title: "Code resent",
        description: `A new verification code has been sent to your ${type}`,
      });
    } catch (error) {
      toast({
        title: "Resend failed",
        description: "Failed to resend verification code. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsResending(false);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center">
        <ShieldCheck className="w-12 h-12 mx-auto text-literary-burgundy mb-2" />
        <CardTitle>
          Verify Your {type === "phone" ? "Phone" : "Email"}
        </CardTitle>
        <CardDescription>
          Enter the 6-digit code sent to{" "}
          <span className="font-medium">
            {type === "phone"
              ? phoneOrEmail.replace(/(\d{3})(\d{3})(\d{4})/, "($1) $2-$3")
              : phoneOrEmail}
          </span>
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {verified ? (
          <div className="flex flex-col items-center justify-center py-4">
            <CheckCircle2 className="h-16 w-16 text-green-500 mb-4" />
            <p className="text-lg font-medium text-center">
              Verification Complete
            </p>
            <p className="text-sm text-muted-foreground text-center">
              You have been successfully verified
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex justify-center">
              <InputOTP
                maxLength={6}
                value={value}
                onChange={setValue}
                disabled={isVerifying}
                containerClassName="gap-2"
              >
                <InputOTPGroup className="gap-2">
                  <InputOTPSlot index={0} />
                  <InputOTPSlot index={1} />
                  <InputOTPSlot index={2} />
                  <InputOTPSlot index={3} />
                  <InputOTPSlot index={4} />
                  <InputOTPSlot index={5} />
                </InputOTPGroup>
              </InputOTP>
            </div>
          </div>
        )}
      </CardContent>

      <CardFooter className="flex-col space-y-2">
        {!verified && (
          <>
            <Button
              onClick={handleVerify}
              className="w-full"
              disabled={value.length !== 6 || isVerifying || verified}
            >
              {isVerifying && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isVerifying ? "Verifying..." : "Verify Code"}
            </Button>

            <div className="flex w-full justify-between text-sm">
              <Button
                variant="ghost"
                className="text-muted-foreground hover:text-current"
                onClick={onCancel}
                disabled={isVerifying || isResending || verified}
              >
                Cancel
              </Button>
              <Button
                variant="link"
                onClick={handleResend}
                className="text-literary-burgundy"
                disabled={isResending || isVerifying || verified}
              >
                {isResending && (
                  <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                )}
                {isResending ? "Resending..." : "Resend Code"}
              </Button>
            </div>
          </>
        )}
      </CardFooter>
    </Card>
  );
};

export default OTPVerification;
