import { useState, useCallback, useEffect } from 'react';
import { useAuth } from '@/contexts/auth/hooks';
import { transferService, TransferRequest, TokenTransferUser, TokenTransferHistory } from '@/services/transferService';
import { useToast } from '@/hooks/use-toast';

export interface UseTokenTransfersOptions {
  autoLoadHistory?: boolean;
  historyLimit?: number;
}

export interface TransferStats {
  totalSent: number;
  totalReceived: number;
  transferCount: number;
  todayTransferred: number;
  remainingDailyLimit: number;
}

export interface UseTokenTransfersReturn {
  // State
  transfers: TokenTransferHistory[];
  stats: TransferStats | null;
  isLoading: boolean;
  isTransferring: boolean;
  error: string | null;

  // Actions
  executeTransfer: (request: TransferRequest) => Promise<{
    success: boolean;
    transferId?: string;
    error?: string;
  }>;
  searchUsers: (searchTerm: string) => Promise<TokenTransferUser[]>;
  findUserByUsername: (username: string) => Promise<TokenTransferUser | null>;
  checkTransferLimits: (amount: number) => Promise<{
    allowed: boolean;
    error?: string;
    remainingDaily?: number;
  }>;
  refreshHistory: () => Promise<void>;
  refreshStats: () => Promise<void>;
}

/**
 * Custom hook for managing token transfer operations
 */
export const useTokenTransfers = (options: UseTokenTransfersOptions = {}): UseTokenTransfersReturn => {
  const { autoLoadHistory = true, historyLimit = 50 } = options;
  const { user } = useAuth();
  const { toast } = useToast();

  // State
  const [transfers, setTransfers] = useState<TokenTransferHistory[]>([]);
  const [stats, setStats] = useState<TransferStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isTransferring, setIsTransferring] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load transfer history
  const loadTransferHistory = useCallback(async () => {
    if (!user?.id) {
      setTransfers([]);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const history = await transferService.getUserTransferHistory(user.id, historyLimit, 0);
      setTransfers(history);
    } catch (err) {
      console.error('Error loading transfer history:', err);
      setError('Failed to load transfer history');
      setTransfers([]);
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, historyLimit]);

  // Load transfer stats
  const loadTransferStats = useCallback(async () => {
    if (!user?.id) {
      setStats(null);
      return;
    }

    try {
      const transferStats = await transferService.getTransferStats(user.id);
      setStats(transferStats);
    } catch (err) {
      console.error('Error loading transfer stats:', err);
      setStats(null);
    }
  }, [user?.id]);

  // Execute a transfer
  const executeTransfer = useCallback(async (request: TransferRequest) => {
    setIsTransferring(true);
    setError(null);

    try {
      const result = await transferService.executeTransfer(request);

      if (result.success) {
        toast({
          title: 'Transfer Successful!',
          description: `Sent ${request.amount} tokens to @${request.recipientUsername}`,
          variant: 'default',
        });

        // Refresh data
        await Promise.all([
          loadTransferHistory(),
          loadTransferStats()
        ]);

        return {
          success: true,
          transferId: result.transferId
        };
      } else {
        toast({
          title: 'Transfer Failed',
          description: result.error || 'Unknown error occurred',
          variant: 'destructive',
        });

        return {
          success: false,
          error: result.error
        };
      }
    } catch (err) {
      console.error('Error executing transfer:', err);
      const errorMessage = 'Failed to process transfer';
      
      toast({
        title: 'Transfer Error',
        description: errorMessage,
        variant: 'destructive',
      });

      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setIsTransferring(false);
    }
  }, [toast, loadTransferHistory, loadTransferStats]);

  // Search for users
  const searchUsers = useCallback(async (searchTerm: string): Promise<TokenTransferUser[]> => {
    if (!searchTerm || searchTerm.length < 2) {
      return [];
    }

    try {
      return await transferService.searchUsersByUsername(searchTerm, 10);
    } catch (err) {
      console.error('Error searching users:', err);
      return [];
    }
  }, []);

  // Find user by username
  const findUserByUsername = useCallback(async (username: string): Promise<TokenTransferUser | null> => {
    if (!username) return null;

    try {
      return await transferService.findUserByUsername(username);
    } catch (err) {
      console.error('Error finding user:', err);
      return null;
    }
  }, []);

  // Check transfer limits
  const checkTransferLimits = useCallback(async (amount: number) => {
    if (!user?.id) {
      return { allowed: false, error: 'User not authenticated' };
    }

    try {
      return await transferService.checkTransferLimits(user.id, amount);
    } catch (err) {
      console.error('Error checking transfer limits:', err);
      return { allowed: false, error: 'Failed to check limits' };
    }
  }, [user?.id]);

  // Refresh history manually
  const refreshHistory = useCallback(async () => {
    await loadTransferHistory();
  }, [loadTransferHistory]);

  // Refresh stats manually
  const refreshStats = useCallback(async () => {
    await loadTransferStats();
  }, [loadTransferStats]);

  // Auto-load data on mount and when user changes
  useEffect(() => {
    if (autoLoadHistory && user?.id) {
      Promise.all([
        loadTransferHistory(),
        loadTransferStats()
      ]);
    }
  }, [autoLoadHistory, user?.id, loadTransferHistory, loadTransferStats]);

  return {
    // State
    transfers,
    stats,
    isLoading,
    isTransferring,
    error,

    // Actions
    executeTransfer,
    searchUsers,
    findUserByUsername,
    checkTransferLimits,
    refreshHistory,
    refreshStats,
  };
};