import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import Stripe from 'https://esm.sh/stripe@10.17.0?target=deno&deno-std=0.132.0';
import { supabaseAdmin } from '../_shared/supabaseAdmin.ts';
import { corsHeaders } from '../_shared/cors.ts';

const stripe = new Stripe(Deno.env.get('STRIPE_TEST_SECRET_KEY')!, {
  httpClient: Stripe.createFetchHttpClient(),
  apiVersion: '2024-04-10',
});

// Enhanced webhook handler for API key integration
async function handleApiKeySync(stripeCustomerId: string, subscriptionStatus: string): Promise<void> {
  try {
    // Find user by Stripe customer ID
    const { data: subscription, error: subError } = await supabaseAdmin
      .from('subscriptions')
      .select('user_id')
      .eq('stripe_customer_id', stripeCustomerId)
      .single();
    
    if (subError || !subscription) {
      console.error('❌ User not found for Stripe customer:', stripeCustomerId);
      return;
    }
    
    // Update user's ad-free status and tier based on subscription
    const isAdFree = subscriptionStatus === 'active' || subscriptionStatus === 'trialing';
    const tier = isAdFree ? 'premium' : 'free';
    
    const { error: profileError } = await supabaseAdmin
      .from('user_profiles')
      .update({
        is_ad_free: isAdFree,
        tier: tier
      })
      .eq('id', subscription.user_id);
    
    if (profileError) {
      console.error('❌ Error updating user profile:', profileError);
    } else {
      console.log(`✅ Updated user ${subscription.user_id} to ${tier} tier (subscription: ${subscriptionStatus})`);
    }
    
    // TODO: Sync API keys when database integration is complete
    // This would update all API keys for the user to match their new subscription tier
    // await StripeApiIntegration.syncAllUserApiKeys(subscription.user_id);
    
  } catch (error) {
    console.error('❌ Error syncing API keys with subscription:', error);
  }
}

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  const signature = req.headers.get('Stripe-Signature');
  const body = await req.text();
  const webhookSecret = Deno.env.get('STRIPE_WEBHOOK_SIGNING_SECRET')!;

  let event: Stripe.Event;

  try {
    if (!signature) {
        throw new Error('Stripe-Signature header is missing.');
    }
    event = await stripe.webhooks.constructEventAsync(body, signature, webhookSecret);
  } catch (err) {
    const errorMessage = err instanceof Error ? err.message : 'Unknown error';
    console.error(`Webhook signature verification failed: ${errorMessage}`);
    return new Response(JSON.stringify({ error: `Webhook signature verification failed: ${errorMessage}` }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400 
    });
  }

  try {
    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        const userId = session.metadata?.user_id;
        const subscriptionId = session.subscription as string;
        const customerId = session.customer as string;

        if (!userId) {
          console.error('User ID not found in checkout session metadata');
          throw new Error('User ID not found in checkout session metadata');
        }

        // Store subscription details in your database
        const { error } = await supabaseAdmin.from('subscriptions').upsert({
          user_id: userId,
          stripe_subscription_id: subscriptionId,
          stripe_customer_id: customerId,
          status: 'active',
        });
        if (error) throw error;
        
        console.log(`Subscription created/updated for user: ${userId}`);
        
        // 🚀 NEW: Sync API key tiers with new subscription
        await handleApiKeySync(customerId, 'active');
        
        break;
      }
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
      case 'customer.subscription.created': {
        const subscription = event.data.object as Stripe.Subscription;
        const customerId = subscription.customer as string;

        const { error } = await supabaseAdmin.from('subscriptions').update({
          status: subscription.status,
        }).eq('stripe_subscription_id', subscription.id);

        if (error) throw error;
        console.log(`Subscription ${subscription.id} status updated to ${subscription.status}`);
        
        // 🚀 NEW: Sync API key tiers with subscription changes
        await handleApiKeySync(customerId, subscription.status);
        
        break;
      }
      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice;
        const customerId = invoice.customer as string;
        
        console.log(`💰 Payment succeeded for customer ${customerId}`);
        
        // Ensure API keys are active when payment succeeds
        await handleApiKeySync(customerId, 'active');
        
        break;
      }
      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice;
        const customerId = invoice.customer as string;
        
        console.log(`❌ Payment failed for customer ${customerId}`);
        
        // Handle payment failure - could trigger grace period or downgrade
        // For now, keep premium access until subscription actually canceled
        break;
      }
      default:
        console.log(`Unhandled event type ${event.type}`);
    }

    return new Response(JSON.stringify({ received: true }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error processing webhook event:', error);
    return new Response(JSON.stringify({ error: errorMessage }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500 
    });
  }
});
