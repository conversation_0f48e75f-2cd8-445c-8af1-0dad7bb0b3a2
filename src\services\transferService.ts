import { tokenService, TokenTransferUser, TokenTransferHistory } from './tokenService';
import { transferNotificationService } from './transferNotificationService';
import { supabase } from '@/lib/supabase';

export interface TransferValidationResult {
  isValid: boolean;
  error?: string;
  recipient?: TokenTransferUser;
  senderBalance?: number;
}

export interface TransferRequest {
  senderId: string;
  recipientUsername: string;
  amount: number;
  transferMessage?: string;
}

export interface TransferResult {
  success: boolean;
  transferId?: string;
  senderNewBalance?: number;
  recipientNewBalance?: number;
  error?: string;
  recipient?: TokenTransferUser;
}

/**
 * Service for handling token transfer operations with authentication and validation
 */
export class TransferService {
  private static instance: TransferService;

  private constructor() {}

  public static getInstance(): TransferService {
    if (!TransferService.instance) {
      TransferService.instance = new TransferService();
    }
    return TransferService.instance;
  }

  /**
   * Authenticate and validate a transfer request
   */
  async validateTransferRequest(request: TransferRequest): Promise<TransferValidationResult> {
    try {
      // Validate basic inputs
      if (!request.senderId || request.senderId.trim().length === 0) {
        return { isValid: false, error: 'Sender ID is required' };
      }

      if (!request.recipientUsername || request.recipientUsername.trim().length === 0) {
        return { isValid: false, error: 'Recipient username is required' };
      }

      if (request.amount <= 0) {
        return { isValid: false, error: 'Transfer amount must be positive' };
      }

      if (request.amount > 1000000) {
        return { isValid: false, error: 'Transfer amount exceeds maximum limit' };
      }

      // Verify sender is authenticated (check if user exists in auth.users)
      const { data: senderData, error: senderError } = await supabase
        .from('user_profiles')
        .select('id, username')
        .eq('id', request.senderId)
        .single();

      if (senderError || !senderData) {
        return { isValid: false, error: 'Sender not found or not authenticated' };
      }

      // Find recipient by username
      const recipient = await tokenService.findUserByUsername(request.recipientUsername.trim());
      
      if (!recipient) {
        return { isValid: false, error: `User '@${request.recipientUsername}' not found` };
      }

      if (!recipient.isActive) {
        return { isValid: false, error: 'Recipient account is not active' };
      }

      // Check if trying to transfer to self
      if (recipient.userId === request.senderId) {
        return { isValid: false, error: 'Cannot transfer tokens to yourself' };
      }

      // Check sender's balance
      const balanceCheck = await tokenService.canAffordTransfer(request.senderId, request.amount);
      
      if (!balanceCheck.canAfford) {
        return { 
          isValid: false, 
          error: `Insufficient tokens. You need ${request.amount} tokens but only have ${balanceCheck.balance}`,
          senderBalance: balanceCheck.balance
        };
      }

      return {
        isValid: true,
        recipient,
        senderBalance: balanceCheck.balance
      };

    } catch (error) {
      console.error('Error validating transfer request:', error);
      return { isValid: false, error: 'Failed to validate transfer request' };
    }
  }

  /**
   * Execute a validated token transfer
   */
  async executeTransfer(request: TransferRequest): Promise<TransferResult> {
    try {
      // First validate the request
      const validation = await this.validateTransferRequest(request);
      
      if (!validation.isValid) {
        // Send failure notification for validation errors
        if (validation.error) {
          // Get sender username for notification
          const { data: senderData } = await supabase
            .from('user_profiles')
            .select('username')
            .eq('id', request.senderId)
            .single();

          await transferNotificationService.notifyTransferFailure({
            senderId: request.senderId,
            senderUsername: senderData?.username || 'Unknown',
            recipientUsername: request.recipientUsername,
            amount: request.amount,
            error: validation.error,
            transferMessage: request.transferMessage,
          });
        }

        return {
          success: false,
          error: validation.error
        };
      }

      // Execute the transfer
      const transferResult = await tokenService.transferTokens(
        request.senderId,
        request.recipientUsername,
        request.amount,
        request.transferMessage
      );

      // Send notifications based on transfer result
      if (transferResult.success && transferResult.transferId && validation.recipient) {
        // Get sender username for notification
        const { data: senderData } = await supabase
          .from('user_profiles')
          .select('username')
          .eq('id', request.senderId)
          .single();

        // Send success notifications to both sender and recipient
        await transferNotificationService.notifyTransferSuccess({
          senderId: request.senderId,
          senderUsername: senderData?.username || 'Unknown',
          recipientId: validation.recipient.userId,
          recipientUsername: validation.recipient.username,
          amount: request.amount,
          transferId: transferResult.transferId,
          transferMessage: request.transferMessage,
          senderNewBalance: transferResult.senderNewBalance,
          recipientNewBalance: transferResult.recipientNewBalance,
        });
      } else if (!transferResult.success && transferResult.error) {
        // Get sender username for notification
        const { data: senderData } = await supabase
          .from('user_profiles')
          .select('username')
          .eq('id', request.senderId)
          .single();

        // Send failure notification to sender
        await transferNotificationService.notifyTransferFailure({
          senderId: request.senderId,
          senderUsername: senderData?.username || 'Unknown',
          recipientUsername: request.recipientUsername,
          amount: request.amount,
          error: transferResult.error,
          transferMessage: request.transferMessage,
        });
      }

      return {
        success: transferResult.success,
        transferId: transferResult.transferId,
        senderNewBalance: transferResult.senderNewBalance,
        recipientNewBalance: transferResult.recipientNewBalance,
        error: transferResult.error,
        recipient: validation.recipient
      };

    } catch (error) {
      console.error('Error executing transfer:', error);
      
      // Send failure notification for unexpected errors
      try {
        const { data: senderData } = await supabase
          .from('user_profiles')
          .select('username')
          .eq('id', request.senderId)
          .single();

        await transferNotificationService.notifyTransferFailure({
          senderId: request.senderId,
          senderUsername: senderData?.username || 'Unknown',
          recipientUsername: request.recipientUsername,
          amount: request.amount,
          error: 'An unexpected error occurred during transfer',
          transferMessage: request.transferMessage,
        });
      } catch (notificationError) {
        console.error('Failed to send error notification:', notificationError);
      }

      return {
        success: false,
        error: 'Failed to execute transfer'
      };
    }
  }

  /**
   * Get transfer history for a user with enhanced information
   */
  async getUserTransferHistory(
    userId: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<TokenTransferHistory[]> {
    try {
      return await tokenService.getTransferHistory(userId, limit, offset);
    } catch (error) {
      console.error('Error fetching transfer history:', error);
      return [];
    }
  }

  /**
   * Search for users by username with rate limiting
   */
  async searchUsersByUsername(
    searchTerm: string,
    limit: number = 10
  ): Promise<TokenTransferUser[]> {
    try {
      if (!searchTerm || searchTerm.trim().length < 2) {
        return [];
      }

      // Search for users with usernames that start with the search term
      const { data, error } = await supabase
        .from('user_profiles')
        .select('id, username, display_name, avatar_url')
        .ilike('username', `${searchTerm.trim()}%`)
        .limit(limit);

      if (error) {
        console.error('Error searching users:', error);
        return [];
      }

      return (data || []).map(user => ({
        userId: user.id,
        username: user.username,
        displayName: user.display_name,
        avatarUrl: user.avatar_url,
        isActive: true // All returned users are considered active
      }));

    } catch (error) {
      console.error('Error searching users by username:', error);
      return [];
    }
  }

  /**
   * Check if a transfer is allowed based on rate limiting and other constraints
   */
  async checkTransferLimits(
    senderId: string,
    amount: number
  ): Promise<{ allowed: boolean; error?: string; remainingDaily?: number }> {
    try {
      // Get today's transfers for rate limiting
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const { data: todayTransfers, error } = await supabase
        .from('token_transfers')
        .select('amount')
        .eq('sender_id', senderId)
        .eq('status', 'completed')
        .gte('created_at', today.toISOString());

      if (error) {
        console.error('Error checking transfer limits:', error);
        return { allowed: false, error: 'Failed to check transfer limits' };
      }

      const dailyTransferredAmount = (todayTransfers || []).reduce(
        (total, transfer) => total + transfer.amount, 
        0
      );

      const DAILY_TRANSFER_LIMIT = 1000; // Max 1000 tokens per day
      const MAX_SINGLE_TRANSFER = 500; // Max 500 tokens per transfer

      if (amount > MAX_SINGLE_TRANSFER) {
        // Send notification about single transfer limit
        await transferNotificationService.notifyTransferLimit(senderId, 'single');
        
        return { 
          allowed: false, 
          error: `Single transfer cannot exceed ${MAX_SINGLE_TRANSFER} tokens` 
        };
      }

      if (dailyTransferredAmount + amount > DAILY_TRANSFER_LIMIT) {
        const remaining = DAILY_TRANSFER_LIMIT - dailyTransferredAmount;
        
        // Send notification about daily limit
        await transferNotificationService.notifyTransferLimit(senderId, 'daily', remaining);
        
        return { 
          allowed: false, 
          error: `Daily transfer limit would be exceeded. You can transfer up to ${remaining} more tokens today`,
          remainingDaily: remaining
        };
      }

      return { 
        allowed: true, 
        remainingDaily: DAILY_TRANSFER_LIMIT - dailyTransferredAmount - amount 
      };

    } catch (error) {
      console.error('Error checking transfer limits:', error);
      return { allowed: false, error: 'Failed to check transfer limits' };
    }
  }

  /**
   * Get transfer statistics for a user
   */
  async getTransferStats(userId: string): Promise<{
    totalSent: number;
    totalReceived: number;
    transferCount: number;
    todayTransferred: number;
    remainingDailyLimit: number;
  }> {
    try {
      const history = await this.getUserTransferHistory(userId, 1000, 0);
      
      const totalSent = history
        .filter(t => t.isSender && t.status === 'completed')
        .reduce((sum, t) => sum + t.amount, 0);
      
      const totalReceived = history
        .filter(t => !t.isSender && t.status === 'completed')
        .reduce((sum, t) => sum + t.amount, 0);

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const todayTransferred = history
        .filter(t => t.isSender && t.status === 'completed' && new Date(t.createdAt) >= today)
        .reduce((sum, t) => sum + t.amount, 0);

      const DAILY_LIMIT = 1000;
      const remainingDailyLimit = Math.max(0, DAILY_LIMIT - todayTransferred);

      return {
        totalSent,
        totalReceived,
        transferCount: history.length,
        todayTransferred,
        remainingDailyLimit
      };

    } catch (error) {
      console.error('Error getting transfer stats:', error);
      return {
        totalSent: 0,
        totalReceived: 0,
        transferCount: 0,
        todayTransferred: 0,
        remainingDailyLimit: 1000
      };
    }
  }
}

// Export singleton instance
export const transferService = TransferService.getInstance();