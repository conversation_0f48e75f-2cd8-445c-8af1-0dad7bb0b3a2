import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MessagingInterface } from './MessagingInterface';
import { useState } from 'react';

const meta: Meta<typeof MessagingInterface> = {
  title: 'Messaging/MessagingInterface',
  component: MessagingInterface,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'A comprehensive messaging-style interface for collaborative story writing, featuring real-time chat bubbles, special actions, participant management, and story compilation.',
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

// Mock data
const mockStory = {
  id: 'story-1',
  title: 'The Mysterious Forest Adventure',
  description: 'A collaborative adventure story set in an enchanted forest',
  contribution_mode: 'word' as const,
  words_per_contribution: 1,
  max_contributions: 100,
  current_contribution_count: 15,
  creator_id: 'user-1',
  status: 'ACTIVE' as const,
};

const mockContributions = [
  {
    id: 'contrib-1',
    content: 'Once',
    author: { id: 'user-1', username: '<PERSON><PERSON><PERSON>', avatar_url: 'https://i.pravatar.cc/150?u=user1' },
    created_at: '2024-01-01T10:00:00Z',
    position: 1,
  },
  {
    id: 'contrib-2', 
    content: 'upon',
    author: { id: 'user-2', username: 'WordSmith', avatar_url: 'https://i.pravatar.cc/150?u=user2' },
    created_at: '2024-01-01T10:05:00Z',
    position: 2,
  },
  {
    id: 'contrib-3',
    content: 'a',
    author: { id: 'user-3', username: 'PenMaster', avatar_url: 'https://i.pravatar.cc/150?u=user3' },
    created_at: '2024-01-01T10:10:00Z',
    position: 3,
  },
  {
    id: 'contrib-4',
    content: 'time',
    author: { id: 'user-1', username: 'StoryTeller', avatar_url: 'https://i.pravatar.cc/150?u=user1' },
    created_at: '2024-01-01T10:15:00Z',
    position: 4,
  },
  {
    id: 'contrib-5',
    content: 'there',
    author: { id: 'user-4', username: 'NarrativeNinja', avatar_url: 'https://i.pravatar.cc/150?u=user4' },
    created_at: '2024-01-01T10:20:00Z',
    position: 5,
  },
  {
    id: 'contrib-6',
    content: 'was',
    author: { id: 'user-2', username: 'WordSmith', avatar_url: 'https://i.pravatar.cc/150?u=user2' },
    created_at: '2024-01-01T10:25:00Z',
    position: 6,
  },
  {
    id: 'contrib-7',
    content: 'mysterious',
    author: { id: 'user-5', username: 'MysticWriter', avatar_url: 'https://i.pravatar.cc/150?u=user5' },
    created_at: '2024-01-01T10:30:00Z',
    position: 7,
    special_type: 'gotcha' as const,
    token_cost: 2,
  },
  {
    id: 'contrib-8',
    content: 'forest',
    author: { id: 'user-3', username: 'PenMaster', avatar_url: 'https://i.pravatar.cc/150?u=user3' },
    created_at: '2024-01-01T10:35:00Z',
    position: 8,
  },
  {
    id: 'contrib-9',
    content: 'filled',
    author: { id: 'user-6', username: 'TaleWeaver', avatar_url: 'https://i.pravatar.cc/150?u=user6' },
    created_at: '2024-01-01T10:40:00Z',
    position: 9,
  },
  {
    id: 'contrib-10',
    content: 'with',
    author: { id: 'user-4', username: 'NarrativeNinja', avatar_url: 'https://i.pravatar.cc/150?u=user4' },
    created_at: '2024-01-01T10:45:00Z',
    position: 10,
  },
  {
    id: 'contrib-11',
    content: 'ancient',
    author: { id: 'user-1', username: 'StoryTeller', avatar_url: 'https://i.pravatar.cc/150?u=user1' },
    created_at: '2024-01-01T10:50:00Z',
    position: 11,
    special_type: 'golden' as const,
    token_cost: 3,
  },
  {
    id: 'contrib-12',
    content: 'secrets',
    author: { id: 'user-7', username: 'SecretKeeper', avatar_url: 'https://i.pravatar.cc/150?u=user7' },
    created_at: '2024-01-01T10:55:00Z',
    position: 12,
  },
  {
    id: 'contrib-13',
    content: 'and',
    author: { id: 'user-2', username: 'WordSmith', avatar_url: 'https://i.pravatar.cc/150?u=user2' },
    created_at: '2024-01-01T11:00:00Z',
    position: 13,
  },
  {
    id: 'contrib-14',
    content: 'magical',
    author: { id: 'user-8', username: 'MagicCrafter', avatar_url: 'https://i.pravatar.cc/150?u=user8' },
    created_at: '2024-01-01T11:05:00Z',
    position: 14,
  },
  {
    id: 'contrib-15',
    content: 'creatures',
    author: { id: 'user-3', username: 'PenMaster', avatar_url: 'https://i.pravatar.cc/150?u=user3' },
    created_at: '2024-01-01T11:10:00Z',
    position: 15,
  },
];

const mockParticipants = [
  {
    id: 'user-1',
    username: 'StoryTeller',
    avatar_url: 'https://i.pravatar.cc/150?u=user1',
    role: 'creator' as const,
    isOnline: true,
    contributionCount: 3,
    joinedAt: new Date('2024-01-01T09:30:00Z'),
  },
  {
    id: 'user-2',
    username: 'WordSmith',
    avatar_url: 'https://i.pravatar.cc/150?u=user2',
    role: 'contributor' as const,
    isOnline: true,
    contributionCount: 3,
    joinedAt: new Date('2024-01-01T09:45:00Z'),
    isTyping: true,
  },
  {
    id: 'user-3',
    username: 'PenMaster',
    avatar_url: 'https://i.pravatar.cc/150?u=user3',
    role: 'contributor' as const,
    isOnline: true,
    contributionCount: 3,
    joinedAt: new Date('2024-01-01T09:50:00Z'),
  },
  {
    id: 'user-4',
    username: 'NarrativeNinja',
    avatar_url: 'https://i.pravatar.cc/150?u=user4',
    role: 'contributor' as const,
    isOnline: false,
    lastSeen: new Date('2024-01-01T10:30:00Z'),
    contributionCount: 2,
    joinedAt: new Date('2024-01-01T10:00:00Z'),
  },
  {
    id: 'user-5',
    username: 'MysticWriter',
    avatar_url: 'https://i.pravatar.cc/150?u=user5',
    role: 'contributor' as const,
    isOnline: true,
    contributionCount: 1,
    joinedAt: new Date('2024-01-01T10:15:00Z'),
  },
  {
    id: 'user-6',
    username: 'TaleWeaver',
    avatar_url: 'https://i.pravatar.cc/150?u=user6',
    role: 'contributor' as const,
    isOnline: false,
    lastSeen: new Date('2024-01-01T10:35:00Z'),
    contributionCount: 1,
    joinedAt: new Date('2024-01-01T10:20:00Z'),
  },
  {
    id: 'user-7',
    username: 'SecretKeeper',
    avatar_url: 'https://i.pravatar.cc/150?u=user7',
    role: 'contributor' as const,
    isOnline: true,
    contributionCount: 1,
    joinedAt: new Date('2024-01-01T10:25:00Z'),
  },
  {
    id: 'user-8',
    username: 'MagicCrafter',
    avatar_url: 'https://i.pravatar.cc/150?u=user8',
    role: 'viewer' as const,
    isOnline: true,
    contributionCount: 1,
    joinedAt: new Date('2024-01-01T10:30:00Z'),
  },
];

// Interactive story with state management
export const InteractiveStory: Story = {
  render: () => {
    const [contributions, setContributions] = useState(mockContributions);
    const [participants, setParticipants] = useState(mockParticipants);
    const [userTokens, setUserTokens] = useState(15);

    const handleSendContribution = async (content: string, specialType?: 'gotcha' | 'reverse' | 'golden') => {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Calculate token cost
      const tokenCost = specialType === 'gotcha' ? 2 : specialType === 'reverse' ? 5 : specialType === 'golden' ? 3 : 0;
      
      if (tokenCost > userTokens) {
        throw new Error('Insufficient tokens');
      }

      const newContribution = {
        id: `contrib-${Date.now()}`,
        content,
        author: {
          id: 'user-1',
          username: 'StoryTeller',
          avatar_url: 'https://i.pravatar.cc/150?u=user1',
        },
        created_at: new Date().toISOString(),
        position: contributions.length + 1,
        special_type: specialType || null,
        token_cost: tokenCost || undefined,
      };

      setContributions(prev => [...prev, newContribution]);
      setUserTokens(prev => prev - tokenCost);
    };

    const handleInviteUser = () => {
      console.log('Invite user clicked');
    };

    const handleKickUser = (userId: string) => {
      setParticipants(prev => prev.filter(p => p.id !== userId));
    };

    const handlePromoteUser = (userId: string) => {
      setParticipants(prev => 
        prev.map(p => p.id === userId ? { ...p, role: 'contributor' as const } : p)
      );
    };

    const handleMuteUser = (userId: string) => {
      console.log('Mute user:', userId);
    };

    const handleShareStory = () => {
      const storyText = contributions.map(c => c.content).join(' ');
      navigator.clipboard.writeText(storyText);
      console.log('Story shared');
    };

    const handleExportStory = (format: 'txt' | 'pdf' | 'markdown') => {
      console.log('Export story as:', format);
    };

    return (
      <div className="h-screen w-full">
        <MessagingInterface
          story={mockStory}
          contributions={contributions}
          participants={participants}
          userTokens={userTokens}
          onSendContribution={handleSendContribution}
          onInviteUser={handleInviteUser}
          onKickUser={handleKickUser}
          onPromoteUser={handlePromoteUser}
          onMuteUser={handleMuteUser}
          onShareStory={handleShareStory}
          onExportStory={handleExportStory}
        />
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story: 'Interactive messaging interface where you can add contributions, use special actions, and manage participants. Try typing a word and clicking send!',
      },
    },
  },
};

// Static examples for different states
export const WordByWordMode: Story = {
  args: {
    story: mockStory,
    contributions: mockContributions.slice(0, 8),
    participants: mockParticipants.slice(0, 4),
    userTokens: 15,
    onSendContribution: async () => {},
  },
};

export const SentenceMode: Story = {
  args: {
    story: {
      ...mockStory,
      contribution_mode: 'sentence',
      words_per_contribution: 1,
    },
    contributions: [
      {
        id: 'contrib-1',
        content: 'The old lighthouse keeper had been alone for thirty years.',
        author: { id: 'user-1', username: 'StoryTeller', avatar_url: 'https://i.pravatar.cc/150?u=user1' },
        created_at: '2024-01-01T10:00:00Z',
        position: 1,
      },
      {
        id: 'contrib-2',
        content: 'Every night, he would light the beacon and watch for ships that never came.',
        author: { id: 'user-2', username: 'WordSmith', avatar_url: 'https://i.pravatar.cc/150?u=user2' },
        created_at: '2024-01-01T10:05:00Z',
        position: 2,
      },
    ],
    participants: mockParticipants.slice(0, 3),
    userTokens: 8,
    onSendContribution: async () => {},
  },
};

export const CompletedStory: Story = {
  args: {
    story: {
      ...mockStory,
      status: 'COMPLETED',
      current_contribution_count: 100,
    },
    contributions: mockContributions,
    participants: mockParticipants,
    userTokens: 5,
    onSendContribution: async () => {},
  },
};

export const EmptyStory: Story = {
  args: {
    story: {
      ...mockStory,
      current_contribution_count: 0,
    },
    contributions: [],
    participants: mockParticipants.slice(0, 2),
    userTokens: 20,
    onSendContribution: async () => {},
  },
};