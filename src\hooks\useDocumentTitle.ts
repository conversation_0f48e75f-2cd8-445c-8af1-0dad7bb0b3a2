import { useEffect } from 'react';

/**
 * Hook to dynamically set the document title
 * @param title - The title to set for the page
 * @param dependencies - Optional dependencies array to trigger title updates
 */
export const useDocumentTitle = (title: string, dependencies: any[] = []) => {
  useEffect(() => {
    const previousTitle = document.title;
    
    // Set the new title
    document.title = title;
    
    // Cleanup function to restore previous title (optional)
    return () => {
      // Optionally restore the previous title when component unmounts
      // document.title = previousTitle;
    };
  }, [title, ...dependencies]);
};

/**
 * Helper function to generate consistent page titles
 * @param pageTitle - The specific page title
 * @param includeAppName - Whether to include the app name (default: true)
 */
export const createPageTitle = (pageTitle: string, includeAppName: boolean = true): string => {
  const appName = 'Word by Word Story';
  
  if (includeAppName) {
    return `${pageTitle} | ${appName}`;
  }
  
  return pageTitle;
}; 