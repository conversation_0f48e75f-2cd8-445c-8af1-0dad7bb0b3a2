import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_ANON_KEY');
  // Consider throwing an error or returning a specific response if these are not set
  // For now, proceeding will likely cause createClient to fail if they are truly missing.
}

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const supabase = createClient(supabaseUrl!, supabase<PERSON><PERSON><PERSON>ey!, {
      global: { headers: { Authorization: req.headers.get('Authorization')! } },
      // auth: { persistSession: false } // Recommended for server-side/Edge Functions
    });

    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = parseInt(url.searchParams.get('limit') || '10', 10);
    const queryStatus = url.searchParams.get('status');
    const authorId = url.searchParams.get('authorId');
    const genre = url.searchParams.get('genre');
    const sortBy = url.searchParams.get('sortBy') || 'created_at';
    const sortOrder = url.searchParams.get('sortOrder') || 'desc';

    const validatedLimit = Math.min(Math.max(1, limit), 100); // Clamp limit between 1 and 100
    const offset = (page - 1) * validatedLimit;

    let query = supabase
      .from('stories')
      .select('*', { count: 'exact' }); // Request total count for pagination

    // Apply filters
    // RLS policies will handle actual data visibility. 
    // These filters are for user-specified search criteria.
    // Defaulting to is_public = true might be too restrictive if a user wants to see their own private stories via API.
    // Let RLS handle visibility primarily. For a public-facing part of an app, the frontend would explicitly set is_public=true.
    // query = query.eq('is_public', true); 

    if (queryStatus) {
      query = query.eq('status', queryStatus);
    }
    if (authorId) {
      query = query.eq('author_id', authorId);
    }
    if (genre) {
      query = query.ilike('genre', `%${genre}%`);
    }

    // Apply sorting
    const validSortColumns = ['created_at', 'last_contribution_at', 'title', 'current_word_count'];
    if (validSortColumns.includes(sortBy)) {
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });
    } else {
      query = query.order('created_at', { ascending: false }); // Default sort
    }
    
    // Apply pagination
    query = query.range(offset, offset + validatedLimit - 1);

    const { data, error, count } = await query;

    if (error) {
      console.error('Supabase query error:', error);
      // Supabase errors often have more specific details in error.details or error.hint
      // Consider logging those or returning a more structured error.
      return new Response(JSON.stringify({ error: error.message, details: error.details, hint: error.hint }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: error.code && !isNaN(parseInt(error.code)) ? parseInt(error.code) : 500, // Attempt to use Supabase error code if numeric
      });
    }

    return new Response(
      JSON.stringify({
        data,
        meta: {
          total_items: count || 0,
          current_page: page,
          items_per_page: validatedLimit,
          total_pages: Math.ceil((count || 0) / validatedLimit),
        }
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (err) {
    console.error('Error processing request:', err);
    return new Response(JSON.stringify({ error: err.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    });
  }
}); 