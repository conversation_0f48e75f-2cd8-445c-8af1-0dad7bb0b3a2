[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [input-otp](../README.md) / InputOTP

# Variable: InputOTP

> `const` **InputOTP**: `ForwardRefExoticComponent`\<`Omit`\<`Omit`\<`InputHTMLAttributes`\<`HTMLInputElement`\>, `"onChange"` \| `"value"` \| `"maxLength"` \| `"textAlign"` \| `"onComplete"` \| `"pushPasswordManagerStrategy"` \| `"pasteTransformer"` \| `"containerClassName"` \| `"noScriptCSSFallback"`\> & `object` & `object` & `RefAttributes`\<`HTMLInputElement`\>, `"ref"`\> \| `Omit`\<`Omit`\<`InputHTMLAttributes`\<`HTMLInputElement`\>, `"onChange"` \| `"value"` \| `"maxLength"` \| `"textAlign"` \| `"onComplete"` \| `"pushPasswordManagerStrategy"` \| `"pasteTransformer"` \| `"containerClassName"` \| `"noScriptCSSFallback"`\> & `object` & `object` & `RefAttributes`\<`HTMLInputElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLInputElement`\>\>

Defined in: [src/components/ui/input-otp.tsx:7](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/input-otp.tsx#L7)
