# PostHog Analytics Integration

This directory contains the PostHog analytics integration for tracking user behavior and key events in the word-by-word story application.

## Setup

### 1. Environment Variables

Add the following environment variables to your `.env` file:

```env
# PostHog Analytics
VITE_POSTHOG_KEY=your_posthog_project_api_key_here
VITE_POSTHOG_HOST=https://app.posthog.com
VITE_POSTHOG_DISABLE_IN_DEV=true
```

- `VITE_POSTHOG_KEY`: Your PostHog project API key (get this from PostHog dashboard)
- `VITE_POSTHOG_HOST`: PostHog API host (use default unless self-hosting)
- `VITE_POSTHOG_DISABLE_IN_DEV`: Set to `true` to disable tracking in development

### 2. Provider Setup

Wrap your app with the PostHog provider in your main App component:

```tsx
import { PostHogProvider } from './components/providers';

function App() {
  return (
    <PostHogProvider>
      {/* Your app content */}
    </PostHogProvider>
  );
}
```

### 3. User Authentication Integration

Use the `usePostHogAuth` hook in your authentication context:

```tsx
import { usePostHogAuth } from '../hooks/usePostHog';

export const AuthContext = () => {
  const [user, setUser] = useState(null);
  
  // This will automatically identify/reset users in PostHog
  usePostHogAuth(user);
  
  // ... rest of your auth logic
};
```

## Event Tracking

### Story Events

```tsx
import { trackStoryEvent } from '../lib/posthog';

// Track story creation
trackStoryEvent.storyCreated('story-123', {
  story_title: 'My Story',
  initial_content: 'Once upon a time...',
});

// Track story contributions
trackStoryEvent.storyContribution('story-123', 'sentence', {
  contribution_text: 'The hero discovered a secret.',
  contribution_length: 28,
});

// Track story completion
trackStoryEvent.storyCompleted('story-123', 42, {
  completion_reason: 'reached_word_limit',
  final_word_count: 500,
});

// Track story views
trackStoryEvent.storyViewed('story-123', {
  view_duration: 120, // seconds
  scroll_depth: 0.8,
});

// Track story votes
trackStoryEvent.storyVoted('story-123', 'up', {
  voter_contribution_count: 5,
});
```

### User Events

```tsx
import { trackUserEvent } from '../lib/posthog';

// Track user registration
trackUserEvent.userRegistered('user-456', 'email', {
  registration_source: 'homepage_cta',
});

// Track login events (usually handled automatically by usePostHogAuth)
trackUserEvent.userLoggedIn('user-456', 'supabase_auth');

// Track logout
trackUserEvent.userLoggedOut({
  session_duration: 1800, // seconds
});
```

### Subscription Events

```tsx
import { trackSubscriptionEvent } from '../lib/posthog';

// Track subscription start
trackSubscriptionEvent.subscriptionStarted('price_monthly', 'Ad-Free Monthly', {
  payment_amount: 5.00,
  currency: 'USD',
});

// Track subscription cancellation
trackSubscriptionEvent.subscriptionCancelled('price_monthly', 'too_expensive', {
  subscription_duration_days: 30,
});

// Track successful payments
trackSubscriptionEvent.paymentSuccessful(5.00, 'USD', 'price_monthly', {
  payment_method: 'card',
});

// Track payment failures
trackSubscriptionEvent.paymentFailed('price_monthly', 'insufficient_funds', {
  retry_count: 2,
});
```

### Engagement Events

```tsx
import { trackEngagementEvent } from '../lib/posthog';

// Track feature usage
trackEngagementEvent.featureUsed('story_search', {
  search_filters_used: ['genre', 'length'],
});

// Track time spent (usually handled automatically by usePostHogTimeTracking)
trackEngagementEvent.timeSpent('story_gallery', 300);

// Track search
trackEngagementEvent.searchPerformed('adventure stories', 15, {
  search_source: 'homepage',
});

// Track errors
trackEngagementEvent.errorEncountered('network_error', 'Failed to load stories', {
  error_code: 500,
  retry_count: 1,
});
```

## Custom Hooks

### usePostHogAuth

Automatically handles user identification and login tracking:

```tsx
import { usePostHogAuth } from '../hooks/usePostHog';

const MyComponent = () => {
  const { user } = useAuth(); // Your auth hook
  const posthog = usePostHogAuth(user);
  
  // posthog is now available for manual tracking if needed
};
```

### usePostHogPageView

Track page views:

```tsx
import { usePostHogPageView } from '../hooks/usePostHog';

const StoryPage = () => {
  usePostHogPageView('story_detail');
  
  return <div>Story content</div>;
};
```

### usePostHogTimeTracking

Automatically track time spent in a component:

```tsx
import { usePostHogTimeTracking } from '../hooks/usePostHog';

const StoryGallery = () => {
  usePostHogTimeTracking('story_gallery');
  
  return <div>Gallery content</div>;
};
```

## Feature Flags

PostHog supports feature flags for A/B testing and gradual rollouts:

```tsx
import { analytics } from '../lib/posthog';

const MyComponent = () => {
  const showNewFeature = analytics.isFeatureEnabled('new_story_editor');
  const variant = analytics.getFeatureFlag('story_layout_test');
  
  return (
    <div>
      {showNewFeature && <NewStoryEditor />}
      {variant === 'variant_a' && <LayoutA />}
      {variant === 'variant_b' && <LayoutB />}
    </div>
  );
};
```

## Privacy Considerations

The PostHog integration is configured with privacy in mind:

- **Input Masking**: All form inputs are masked in session recordings
- **Respect DNT**: Do Not Track headers are respected
- **Secure Cookies**: Cookies are marked as secure
- **Development Disabled**: Tracking is disabled in development by default
- **Identified Users Only**: Person profiles are only created for identified users

## Debugging

To debug PostHog events:

1. Open browser DevTools
2. Go to Network tab
3. Filter by "posthog" or "ph"
4. Trigger events and watch for network requests
5. Check PostHog dashboard for events appearing (may take a few minutes)

You can also enable PostHog debugging:

```tsx
import posthog from 'posthog-js';

if (import.meta.env.DEV) {
  posthog.debug(); // Enable debug logging
}
```

## Best Practices

1. **Consistent Naming**: Use consistent event names (snake_case recommended)
2. **Meaningful Properties**: Include relevant context in event properties
3. **Avoid PII**: Don't include personally identifiable information in events
4. **Batch Similar Events**: Group related properties together
5. **Test Events**: Always test events in development before deploying
6. **Document Events**: Keep this README updated with new events

## Common Events to Track

- User registration and authentication
- Story creation, contribution, and completion
- Subscription lifecycle events
- Feature usage and engagement
- Errors and performance issues
- Search and discovery actions
- Social interactions (votes, shares)

## Integration Checklist

- [ ] PostHog account created and project configured
- [ ] Environment variables set
- [ ] PostHogProvider added to app root
- [ ] User authentication integration complete
- [ ] Key story events implemented
- [ ] Subscription events implemented
- [ ] Error tracking implemented
- [ ] Feature flags tested (if using)
- [ ] Privacy settings reviewed
- [ ] Events tested in PostHog dashboard