import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useNavigate } from "react-router-dom";
import { BookText, Pencil, Users } from "lucide-react";

interface Step4CreateProps {
  onContinue: () => void;
  onComplete: () => void;
}

export const Step4Create: React.FC<Step4CreateProps> = ({
  onContinue,
  onComplete,
}) => {
  const navigate = useNavigate();

  const handleNavigation = (path: string) => {
    // First complete the onboarding
    onComplete();
    // Then navigate to the desired page
    navigate(path);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-xl font-semibold">Ready to Start Creating?</h2>
        <p className="text-muted-foreground mt-2">
          Choose how you want to begin your storytelling journey
        </p>
      </div>

      <RadioGroup defaultValue="create" className="space-y-3 pt-2">
        <div className="flex items-center space-x-2 border rounded-md p-4">
          <RadioGroupItem value="create" id="r1" />
          <Label
            htmlFor="r1"
            className="flex items-center gap-2 cursor-pointer w-full"
          >
            <div className="bg-primary/10 h-10 w-10 rounded-full flex items-center justify-center">
              <Pencil className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="font-medium">Create a new story</p>
              <p className="text-sm text-muted-foreground">
                Start fresh with your own story idea
              </p>
            </div>
          </Label>
        </div>

        <div className="flex items-center space-x-2 border rounded-md p-4">
          <RadioGroupItem value="join" id="r2" />
          <Label
            htmlFor="r2"
            className="flex items-center gap-2 cursor-pointer w-full"
          >
            <div className="bg-primary/10 h-10 w-10 rounded-full flex items-center justify-center">
              <Users className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="font-medium">Join existing stories</p>
              <p className="text-sm text-muted-foreground">
                Contribute to stories created by others
              </p>
            </div>
          </Label>
        </div>

        <div className="flex items-center space-x-2 border rounded-md p-4">
          <RadioGroupItem value="browse" id="r3" />
          <Label
            htmlFor="r3"
            className="flex items-center gap-2 cursor-pointer w-full"
          >
            <div className="bg-primary/10 h-10 w-10 rounded-full flex items-center justify-center">
              <BookText className="h-5 w-5 text-primary" />
            </div>
            <div>
              <p className="font-medium">Browse completed stories</p>
              <p className="text-sm text-muted-foreground">
                Get inspired by reading finished works
              </p>
            </div>
          </Label>
        </div>
      </RadioGroup>

      <div className="flex flex-col gap-3 mt-6">
        <Button onClick={() => handleNavigation("/create-story")}>
          <Pencil className="mr-2 h-4 w-4" />
          Create Your First Story
        </Button>

        <Button variant="outline" onClick={() => handleNavigation("/gallery")}>
          <BookText className="mr-2 h-4 w-4" />
          Browse Stories
        </Button>
      </div>
    </div>
  );
};
