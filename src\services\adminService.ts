import { supabase } from '@/lib/supabase';

export interface AdminUser {
  id: string;
  email?: string;
  username?: string;
  isAdmin: boolean;
  avatarUrl?: string;
  createdAt: string;
}

export interface AdminSession {
  id: string;
  adminUserId: string;
  sessionStart: string;
  sessionEnd?: string;
  ipAddress?: string;
  userAgent?: string;
  actionsPerformed: number;
  lastActivity: string;
  isActive: boolean;
}

export interface AdminTokenGrant {
  id: string;
  adminUserId: string;
  adminUsername?: string;
  targetUserId: string;
  targetUsername?: string;
  amount: number;
  reason: string;
  grantType: 'manual' | 'bulk' | 'automated' | 'correction';
  batchId?: string;
  createdAt: string;
}

export interface AdminAction {
  id: string;
  adminUserId: string;
  sessionId?: string;
  actionType: 'token_grant' | 'user_search' | 'view_logs' | 'bulk_operation' | 'export_data';
  targetUserId?: string;
  actionDetails: Record<string, any>;
  createdAt: string;
}

export interface TokenGrantResult {
  success: boolean;
  newBalance?: number;
  grantId?: string;
  errorMessage?: string;
}

export interface BulkGrantResult {
  batchId: string;
  totalGranted: number;
  successfulGrants: number;
  failedGrants: number;
  errorDetails: Array<{ userId: string; error: string }>;
}

export interface UserSearchResult {
  id: string;
  username?: string;
  email?: string;
  displayName?: string;
  avatarUrl?: string;
  tokenBalance?: number;
  createdAt: string;
  isActive: boolean;
}

/**
 * Service for handling admin operations including authentication, token management, and audit logging
 */
export class AdminService {
  private static instance: AdminService;

  private constructor() {}

  public static getInstance(): AdminService {
    if (!AdminService.instance) {
      AdminService.instance = new AdminService();
    }
    return AdminService.instance;
  }

  /**
   * Check if current user has admin privileges
   */
  async isUserAdmin(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('is_admin')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error checking admin status:', error);
        return false;
      }

      return data?.is_admin === true;
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  }

  /**
   * Get admin user details
   */
  async getAdminUser(userId: string): Promise<AdminUser | null> {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('id, username, avatar_url, is_admin, created_at')
        .eq('id', userId)
        .eq('is_admin', true)
        .single();

      if (error || !data) {
        return null;
      }

      return {
        id: data.id,
        username: data.username,
        isAdmin: data.is_admin,
        avatarUrl: data.avatar_url,
        createdAt: data.created_at,
      };
    } catch (error) {
      console.error('Error fetching admin user:', error);
      return null;
    }
  }

  /**
   * Start an admin session
   */
  async startAdminSession(
    adminUserId: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<{ success: boolean; sessionId?: string; error?: string }> {
    try {
      const { data, error } = await supabase.rpc('start_admin_session', {
        p_admin_user_id: adminUserId,
        p_ip_address: ipAddress,
        p_user_agent: userAgent,
      });

      if (error) {
        console.error('Error starting admin session:', error);
        return { success: false, error: error.message };
      }

      const result = data[0];
      return {
        success: result.success,
        sessionId: result.session_id,
        error: result.error_message,
      };
    } catch (error) {
      console.error('Error starting admin session:', error);
      return { success: false, error: 'Failed to start admin session' };
    }
  }

  /**
   * End an admin session
   */
  async endAdminSession(sessionId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase.rpc('end_admin_session', {
        p_session_id: sessionId,
      });

      if (error) {
        console.error('Error ending admin session:', error);
        return false;
      }

      return data === true;
    } catch (error) {
      console.error('Error ending admin session:', error);
      return false;
    }
  }

  /**
   * Grant tokens to a user
   */
  async grantTokens(
    adminUserId: string,
    targetUserId: string,
    amount: number,
    reason: string,
    grantType: 'manual' | 'bulk' | 'automated' | 'correction' = 'manual',
    batchId?: string,
    sessionId?: string
  ): Promise<TokenGrantResult> {
    try {
      const { data, error } = await supabase.rpc('admin_grant_tokens', {
        p_admin_user_id: adminUserId,
        p_target_user_id: targetUserId,
        p_amount: amount,
        p_reason: reason,
        p_grant_type: grantType,
        p_batch_id: batchId,
        p_session_id: sessionId,
      });

      if (error) {
        console.error('Error granting tokens:', error);
        return { success: false, errorMessage: error.message };
      }

      const result = data[0];
      return {
        success: result.success,
        newBalance: result.new_balance,
        grantId: result.grant_id,
        errorMessage: result.error_message,
      };
    } catch (error) {
      console.error('Error granting tokens:', error);
      return { success: false, errorMessage: 'Failed to grant tokens' };
    }
  }

  /**
   * Bulk grant tokens to multiple users
   */
  async bulkGrantTokens(
    adminUserId: string,
    targetUserIds: string[],
    amount: number,
    reason: string,
    sessionId?: string
  ): Promise<BulkGrantResult> {
    try {
      const { data, error } = await supabase.rpc('admin_bulk_grant_tokens', {
        p_admin_user_id: adminUserId,
        p_target_user_ids: targetUserIds,
        p_amount: amount,
        p_reason: reason,
        p_session_id: sessionId,
      });

      if (error) {
        console.error('Error bulk granting tokens:', error);
        throw error;
      }

      const result = data[0];
      return {
        batchId: result.batch_id,
        totalGranted: result.total_granted,
        successfulGrants: result.successful_grants,
        failedGrants: result.failed_grants,
        errorDetails: result.error_details || [],
      };
    } catch (error) {
      console.error('Error bulk granting tokens:', error);
      throw error;
    }
  }

  /**
   * Search for users by username or email
   */
  async searchUsers(
    searchTerm: string,
    limit: number = 20,
    includeTokenBalance: boolean = true
  ): Promise<UserSearchResult[]> {
    try {
      if (includeTokenBalance) {
        // When including token balance, we need a left join
        let query = supabase
          .from('user_profiles')
          .select(`
            id,
            username,
            display_name,
            avatar_url,
            created_at,
            user_tokens(token_balance)
          `)
          .limit(limit);

        // Search by username or display name
        if (searchTerm.trim()) {
          query = query.or(`username.ilike.%${searchTerm}%,display_name.ilike.%${searchTerm}%`);
        }

        const { data, error } = await query;

        if (error) {
          console.error('Error searching users:', error);
          return [];
        }

        return (data || []).map((user: any) => ({
          id: user.id,
          username: user.username,
          displayName: user.display_name,
          avatarUrl: user.avatar_url,
          tokenBalance: user.user_tokens?.[0]?.token_balance || 0,
          createdAt: user.created_at,
          isActive: true, // Assume active if in database
        }));
      } else {
        // When not including token balance, simpler query
        let query = supabase
          .from('user_profiles')
          .select('id, username, display_name, avatar_url, created_at')
          .limit(limit);

        // Search by username or display name
        if (searchTerm.trim()) {
          query = query.or(`username.ilike.%${searchTerm}%,display_name.ilike.%${searchTerm}%`);
        }

        const { data, error } = await query;

        if (error) {
          console.error('Error searching users:', error);
          return [];
        }

        return (data || []).map((user: any) => ({
          id: user.id,
          username: user.username,
          displayName: user.display_name,
          avatarUrl: user.avatar_url,
          createdAt: user.created_at,
          isActive: true, // Assume active if in database
        }));
      }
    } catch (error) {
      console.error('Error searching users:', error);
      return [];
    }
  }

  /**
   * Get token grant history with filtering
   */
  async getTokenGrantHistory(
    adminUserId: string,
    options: {
      limit?: number;
      offset?: number;
      targetUserId?: string;
      grantType?: string;
      fromDate?: string;
      toDate?: string;
    } = {}
  ): Promise<AdminTokenGrant[]> {
    try {
      const {
        limit = 50,
        offset = 0,
        targetUserId,
        grantType,
        fromDate,
        toDate,
      } = options;

      const { data, error } = await supabase.rpc('get_admin_token_grants', {
        p_admin_user_id: adminUserId,
        p_limit: limit,
        p_offset: offset,
        p_target_user_id: targetUserId,
        p_grant_type: grantType,
        p_from_date: fromDate,
        p_to_date: toDate,
      });

      if (error) {
        console.error('Error fetching token grant history:', error);
        return [];
      }

      return (data || []).map(grant => ({
        id: grant.grant_id,
        adminUserId: adminUserId,
        adminUsername: grant.admin_username,
        targetUserId: grant.target_user_id,
        targetUsername: grant.target_username,
        amount: grant.amount,
        reason: grant.reason,
        grantType: grant.grant_type,
        batchId: grant.batch_id,
        createdAt: grant.created_at,
      }));
    } catch (error) {
      console.error('Error fetching token grant history:', error);
      return [];
    }
  }

  /**
   * Get admin sessions
   */
  async getAdminSessions(
    adminUserId: string,
    limit: number = 20,
    offset: number = 0
  ): Promise<AdminSession[]> {
    try {
      const { data, error } = await supabase
        .from('admin_sessions')
        .select('*')
        .eq('admin_user_id', adminUserId)
        .order('session_start', { ascending: false })
        .range(offset, offset + limit - 1);

      if (error) {
        console.error('Error fetching admin sessions:', error);
        return [];
      }

      return (data || []).map(session => ({
        id: session.id,
        adminUserId: session.admin_user_id,
        sessionStart: session.session_start,
        sessionEnd: session.session_end,
        ipAddress: session.ip_address,
        userAgent: session.user_agent,
        actionsPerformed: session.actions_performed,
        lastActivity: session.last_activity,
        isActive: session.is_active,
      }));
    } catch (error) {
      console.error('Error fetching admin sessions:', error);
      return [];
    }
  }

  /**
   * Get admin actions log
   */
  async getAdminActions(
    adminUserId: string,
    limit: number = 50,
    offset: number = 0,
    actionType?: string
  ): Promise<AdminAction[]> {
    try {
      let query = supabase
        .from('admin_actions')
        .select('*')
        .eq('admin_user_id', adminUserId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      if (actionType) {
        query = query.eq('action_type', actionType);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching admin actions:', error);
        return [];
      }

      return (data || []).map(action => ({
        id: action.id,
        adminUserId: action.admin_user_id,
        sessionId: action.session_id,
        actionType: action.action_type,
        targetUserId: action.target_user_id,
        actionDetails: action.action_details,
        createdAt: action.created_at,
      }));
    } catch (error) {
      console.error('Error fetching admin actions:', error);
      return [];
    }
  }

  /**
   * Log admin action
   */
  async logAdminAction(
    adminUserId: string,
    actionType: 'token_grant' | 'user_search' | 'view_logs' | 'bulk_operation' | 'export_data',
    targetUserId?: string,
    actionDetails: Record<string, any> = {},
    sessionId?: string
  ): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('admin_actions')
        .insert({
          admin_user_id: adminUserId,
          session_id: sessionId,
          action_type: actionType,
          target_user_id: targetUserId,
          action_details: actionDetails,
        });

      if (error) {
        console.error('Error logging admin action:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error logging admin action:', error);
      return false;
    }
  }

  /**
   * Get user token balance
   */
  async getUserTokenBalance(userId: string): Promise<number | null> {
    try {
      const { data, error } = await supabase
        .from('user_tokens')
        .select('token_balance')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.error('Error fetching user token balance:', error);
        return null;
      }

      return data?.token_balance || 0;
    } catch (error) {
      console.error('Error fetching user token balance:', error);
      return null;
    }
  }

  /**
   * Export admin data (for compliance/auditing)
   */
  async exportAdminData(
    adminUserId: string,
    dataType: 'token_grants' | 'admin_actions' | 'admin_sessions',
    fromDate?: string,
    toDate?: string
  ): Promise<any[]> {
    try {
      // Log the export action
      await this.logAdminAction(adminUserId, 'export_data', undefined, {
        data_type: dataType,
        from_date: fromDate,
        to_date: toDate,
      });

      let query;
      let tableName: string;

      switch (dataType) {
        case 'token_grants':
          tableName = 'admin_token_grants';
          query = supabase
            .from(tableName)
            .select(`
              id,
              admin_user_id,
              target_user_id,
              amount,
              reason,
              grant_type,
              batch_id,
              created_at
            `);
          break;

        case 'admin_actions':
          tableName = 'admin_actions';
          query = supabase
            .from(tableName)
            .select('*');
          break;

        case 'admin_sessions':
          tableName = 'admin_sessions';
          query = supabase
            .from(tableName)
            .select('*');
          break;

        default:
          throw new Error('Invalid data type for export');
      }

      if (fromDate) {
        query = query.gte('created_at', fromDate);
      }

      if (toDate) {
        query = query.lte('created_at', toDate);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) {
        console.error('Error exporting admin data:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Error exporting admin data:', error);
      return [];
    }
  }
}

// Export singleton instance
export const adminService = AdminService.getInstance(); 