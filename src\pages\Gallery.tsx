import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useStory } from "@/contexts/StoryContext";
import { Story } from "@/types";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { BookOpen, Search, Share2, Star, ThumbsUp, User } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import SocialShare from "@/components/story/SocialShare";
import { RotatingAdvertisement } from "@/components/ads/RotatingAdvertisement";
import { useAds } from "@/contexts/AdsContext";
import { handleImageError } from "@/lib/utils";
import { trackEngagementEvent } from "@/lib/posthog";
import { usePostHogTimeTracking } from "@/hooks/usePostHog";
import { useDocumentTitle, createPageTitle } from "@/hooks/useDocumentTitle";

const GalleryPage: React.FC = () => {
  const { stories = [], voteForStory, loading } = useStory();
  const { isAdFreeUser } = useAds();
  const [searchTerm, setSearchTerm] = useState("");
  const [filter, setFilter] = useState<"all" | "popular" | "recent">("all");
  
  // Track time spent in gallery
  usePostHogTimeTracking('story_gallery');

  // Set page title
  useDocumentTitle(createPageTitle("Story Gallery"));

  // Add null check to prevent "Cannot read properties of undefined (reading 'filter')" error
  const publishedStories = Array.isArray(stories)
    ? stories.filter(
        (story) => story && story.status === "completed" && story.isPublished,
      )
    : [];

  const filteredStories = publishedStories
    .filter((story) => {
      if (!searchTerm.trim()) return true;

      const term = searchTerm.toLowerCase();
      return (
        story.title.toLowerCase().includes(term) ||
        story.createdBy?.username?.toLowerCase().includes(term) ||
        story.words?.some((word) => word.word.toLowerCase().includes(term)) ||
        false
      );
    })
    .sort((a, b) => {
      if (filter === "popular") {
        return (b.votes || 0) - (a.votes || 0);
      } else if (filter === "recent") {
        return (
          new Date(b.updatedAt || 0).getTime() -
          new Date(a.updatedAt || 0).getTime()
        );
      }
      if ((b.votes || 0) !== (a.votes || 0)) {
        return (b.votes || 0) - (a.votes || 0);
      }
      return (
        new Date(b.updatedAt || 0).getTime() -
        new Date(a.updatedAt || 0).getTime()
      );
    });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 bg-literary-burgundy/20 rounded-full"></div>
          <div className="h-4 w-48 mt-4 bg-literary-burgundy/20 rounded"></div>
          <div className="h-3 w-36 mt-2 bg-literary-burgundy/10 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-serif">Story Gallery</h1>
        <p className="text-gray-600 mt-2 max-w-2xl mx-auto">
          Discover collaborative stories written one word at a time by our
          community
        </p>
      </div>

      {/* Advertisement at header */}
      {!isAdFreeUser && (
        <div className="mb-6 max-w-4xl mx-auto">
          <RotatingAdvertisement placement="header" />
        </div>
      )}

      <div className="flex flex-col md:flex-row justify-between items-center mb-8 gap-4">
        <div className="relative w-full md:w-96">
          <Search
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            size={18}
          />
          <Input
            placeholder="Search stories..."
            className="pl-10 input-primary"
            value={searchTerm}
            onChange={(e) => {
              const newValue = e.target.value;
              setSearchTerm(newValue);
              
              // Track search when user types
              if (newValue.length > 2) {
                trackEngagementEvent.searchPerformed(newValue, filteredStories.length, {
                  search_source: 'gallery',
                  current_filter: filter,
                });
              }
            }}
          />
        </div>

        <div className="flex space-x-2">
          <Button
            variant={filter === "all" ? "default" : "outline"}
            onClick={() => {
              setFilter("all");
              trackEngagementEvent.featureUsed('filter_stories', {
                filter_type: 'all',
                previous_filter: filter,
              });
            }}
            className={
              filter === "all" ? "bg-literary-burgundy hover:bg-opacity-90" : ""
            }
          >
            All
          </Button>
          <Button
            variant={filter === "popular" ? "default" : "outline"}
            onClick={() => {
              setFilter("popular");
              trackEngagementEvent.featureUsed('filter_stories', {
                filter_type: 'popular',
                previous_filter: filter,
              });
            }}
            className={
              filter === "popular"
                ? "bg-literary-burgundy hover:bg-opacity-90"
                : ""
            }
          >
            Most Popular
          </Button>
          <Button
            variant={filter === "recent" ? "default" : "outline"}
            onClick={() => {
              setFilter("recent");
              trackEngagementEvent.featureUsed('filter_stories', {
                filter_type: 'recent',
                previous_filter: filter,
              });
            }}
            className={
              filter === "recent"
                ? "bg-literary-burgundy hover:bg-opacity-90"
                : ""
            }
          >
            Recently Added
          </Button>
        </div>
      </div>

      {filteredStories.length === 0 ? (
        <div className="text-center py-16">
          <BookOpen className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-700">
            No stories found
          </h3>
          <p className="text-gray-500 mt-2">
            {publishedStories.length === 0
              ? "There are no published stories in the gallery yet."
              : "No stories match your search criteria."}
          </p>
        </div>
      ) : (
        <>
          {/* Content advertisement */}
          {!isAdFreeUser && (
            <div className="mb-6 max-w-4xl mx-auto">
              <RotatingAdvertisement placement="content" />
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {filteredStories.map((story) => (
              <StoryCard key={story.id} story={story} onVote={voteForStory} />
            ))}
          </div>
        </>
      )}

      {/* Footer advertisement */}
      {!isAdFreeUser && (
        <div className="mt-8 max-w-4xl mx-auto">
          <RotatingAdvertisement placement="footer" />
        </div>
      )}
    </div>
  );
};

const StoryCard: React.FC<{
  story: Story;
  onVote: (storyId: string) => Promise<boolean>;
}> = ({ story, onVote }) => {
  const [isVoting, setIsVoting] = useState(false);
  const [excerpt, setExcerpt] = useState("");

  useEffect(() => {
    const words = story.words.map((w) => w.word);
    const excerptWords = words.slice(0, Math.min(40, words.length));
    let excerptText = excerptWords.join(" ");

    if (words.length > 40) {
      excerptText += "...";
    }

    setExcerpt(excerptText);
  }, [story]);

  const handleVote = async () => {
    setIsVoting(true);
    try {
      await onVote(story.id);
    } catch (error) {
      console.error("Failed to vote:", error);
    } finally {
      setIsVoting(false);
    }
  };

  return (
    <Card className="overflow-hidden border-literary-gold/10 hover:shadow-lg transition-shadow duration-300">
      <CardHeader className="pb-2">
        <div className="flex justify-between">
          <div>
            <CardTitle className="text-xl font-serif">{story.title}</CardTitle>
            <CardDescription className="flex items-center mt-1">
              <Avatar className="h-5 w-5 mr-1">
                <AvatarImage
                  src={`/placeholder.svg`}
                  onError={handleImageError}
                />
                <AvatarFallback>
                  <User size={12} />
                </AvatarFallback>
              </Avatar>
              <span>Created by {story.createdBy.username}</span>
            </CardDescription>
          </div>
          <Badge className="bg-literary-gold text-literary-ink">
            {story.wordCount} Words
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600 italic mb-4 line-clamp-3">"{excerpt}"</p>

        <div className="flex justify-between items-center text-sm text-gray-500">
          <div className="flex items-center">
            <Star className="h-4 w-4 text-literary-gold mr-1" />
            <span>{story.votes} votes</span>
          </div>
          <div>
            Published{" "}
            {formatDistanceToNow(new Date(story.updatedAt), {
              addSuffix: true,
            })}
          </div>
        </div>
      </CardContent>
      <CardFooter className="border-t flex justify-between pt-4">
        <Button
          variant="outline"
          size="sm"
          onClick={handleVote}
          disabled={isVoting}
          className="text-literary-burgundy border-literary-burgundy hover:bg-literary-burgundy hover:text-white"
        >
          <ThumbsUp className="h-4 w-4 mr-2" />
          Upvote
        </Button>

        <div className="flex space-x-2">
          <SocialShare storyId={story.id} storyTitle={story.title} />
          <Link to={`/story/${story.id}`}>
            <Button className="bg-literary-navy hover:bg-opacity-90">
              <BookOpen className="h-4 w-4 mr-2" />
              Read Story
            </Button>
          </Link>
        </div>
      </CardFooter>
    </Card>
  );
};

export default GalleryPage;
