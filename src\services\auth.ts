import { supabase } from "@/lib/supabase";

// Export supabase for use in other services
export { supabase };

// Sign up with email and password
export const signUp = async (email: string, password: string) => {
  return await supabase.auth.signUp({ email, password });
};

// Sign in with email and password
export const signIn = async (email: string, password: string) => {
  return await supabase.auth.signInWithPassword({ email, password });
};

// Sign out
export const signOut = async () => {
  return await supabase.auth.signOut();
};

// Password reset
export const resetPassword = async (email: string) => {
  return await supabase.auth.resetPasswordForEmail(email);
};

// Get current user
export const getUser = async () => {
  return await supabase.auth.getUser();
}; 