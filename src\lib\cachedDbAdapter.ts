import { DatabaseAdapter } from './db-adapter';
import { cacheService, CacheKeys, InvalidationPatterns } from '../services/cacheService';

// Cache-enhanced database adapter
export class CachedDatabaseAdapter {
  
  // Story operations with caching
  static async createStory(storyData: any) {
    const result = await DatabaseAdapter.createStory(storyData);
    
    if (result.data && !result.error) {
      // Cache the newly created story
      await cacheService.setStory(result.data.id, result.data);
      
      // Invalidate popular stories cache since we have new content
      await cacheService.delete(CacheKeys.POPULAR_STORIES());
      await cacheService.delete(CacheKeys.HOT_CONTENT());
    }
    
    return result;
  }

  static async getStory(storyId: string) {
    // Try cache first
    const cachedStory = await cacheService.getStory(storyId);
    if (cachedStory) {
      console.log(`🔥 Cache HIT: Story ${storyId}`);
      return { data: cachedStory, error: null };
    }

    console.log(`💾 Cache MISS: Story ${storyId} - fetching from DB`);
    
    // Fetch from database
    const result = await DatabaseAdapter.getStory(storyId);
    
    if (result.data && !result.error) {
      // Cache the result
      await cacheService.setStory(storyId, result.data);
    }
    
    return result;
  }

  static async getStoryWithContributions(storyId: string) {
    // Try cache first
    const cachedData = await cacheService.getStoryWithContributions(storyId);
    if (cachedData) {
      console.log(`🔥 Cache HIT: Story with contributions ${storyId}`);
      return { data: cachedData, error: null };
    }

    console.log(`💾 Cache MISS: Story with contributions ${storyId} - fetching from DB`);
    
    // Fetch from database
    const result = await DatabaseAdapter.getStoryWithContributions(storyId);
    
    if (result.data && !result.error) {
      // Cache the result
      await cacheService.setStoryWithContributions(storyId, result.data);
      
      // Also cache individual story if it exists
      await cacheService.setStory(storyId, result.data);
    }
    
    return result;
  }

  static async getLatestContribution(storyId: string) {
    // For latest contribution, we'll use a shorter cache time since it changes frequently
    const cacheKey = CacheKeys.CONTRIBUTIONS(storyId) + ':latest';
    const cached = await cacheService.get(cacheKey);
    
    if (cached) {
      console.log(`🔥 Cache HIT: Latest contribution ${storyId}`);
      return { data: cached, error: null };
    }

    console.log(`💾 Cache MISS: Latest contribution ${storyId} - fetching from DB`);
    
    const result = await DatabaseAdapter.getLatestContribution(storyId);
    
    if (result.data && !result.error) {
      // Cache for a shorter time (5 minutes) since contributions change frequently
      await cacheService.set(cacheKey, result.data, 300);
    }
    
    return result;
  }

  static async createContribution(contributionData: any) {
    const result = await DatabaseAdapter.createContribution(contributionData);
    
    if (result.data && !result.error) {
      // Invalidate all related caches when a new contribution is added
      await cacheService.invalidateContributionCache(contributionData.story_id);
      
      // Update latest contribution cache
      const latestCacheKey = CacheKeys.CONTRIBUTIONS(contributionData.story_id) + ':latest';
      await cacheService.set(latestCacheKey, result.data, 300);
    }
    
    return result;
  }

  static async updateStory(storyId: string, updateData: any) {
    const result = await DatabaseAdapter.updateStory(storyId, updateData);
    
    if (result.data && !result.error) {
      // Invalidate story cache and update with new data
      await cacheService.invalidateStoryCache(storyId);
      await cacheService.setStory(storyId, result.data);
    }
    
    return result;
  }

  static async addParticipantToStory(storyId: string, userId: string) {
    const result = await DatabaseAdapter.addParticipantToStory(storyId, userId);
    
    if (result.data && !result.error) {
      // Invalidate story cache since participants list changed
      await cacheService.invalidateStoryCache(storyId);
    }
    
    return result;
  }

  // User session operations with caching
  static async updateUserSession(sessionData: any) {
    const result = await DatabaseAdapter.updateUserSession(sessionData);
    
    if (result.data && !result.error) {
      // Cache the session data
      await cacheService.setUserSession(
        sessionData.story_id,
        sessionData.user_id,
        result.data
      );
      
      // Invalidate active sessions cache for the story
      await cacheService.delete(CacheKeys.ACTIVE_SESSIONS(sessionData.story_id));
    }
    
    return result;
  }

  static async getStoryUserSessions(storyId: string) {
    // Try cache first
    const cacheKey = CacheKeys.ACTIVE_SESSIONS(storyId);
    const cached = await cacheService.get(cacheKey);
    
    if (cached) {
      console.log(`🔥 Cache HIT: User sessions ${storyId}`);
      return { data: cached, error: null };
    }

    console.log(`💾 Cache MISS: User sessions ${storyId} - fetching from DB`);
    
    const result = await DatabaseAdapter.getStoryUserSessions(storyId);
    
    if (result.data && !result.error) {
      // Cache for shorter time since sessions change frequently
      await cacheService.set(cacheKey, result.data, 300); // 5 minutes
    }
    
    return result;
  }

  // Bulk operations for efficiency
  static async getMultipleStories(storyIds: string[]) {
    const results: any[] = [];
    const uncachedIds: string[] = [];

    // Check cache for each story
    for (const storyId of storyIds) {
      const cached = await cacheService.getStory(storyId);
      if (cached) {
        results.push({ id: storyId, data: cached, fromCache: true });
      } else {
        uncachedIds.push(storyId);
      }
    }

    // Fetch uncached stories from database
    if (uncachedIds.length > 0) {
      console.log(`💾 Bulk fetching ${uncachedIds.length} uncached stories`);
      
      // Note: This would need to be implemented in your DatabaseAdapter
      // For now, fetch individually
      for (const storyId of uncachedIds) {
        const result = await this.getStory(storyId);
        if (result.data && !result.error) {
          results.push({ id: storyId, data: result.data, fromCache: false });
        }
      }
    }

    console.log(`🔥 Cache efficiency: ${results.filter(r => r.fromCache).length}/${storyIds.length} hits`);
    
    return results;
  }

  // Cache warming for popular content
  static async warmPopularContent() {
    console.log('🔥 Warming cache with popular content...');
    
    try {
      // This would typically fetch popular stories from analytics or a trending system
      // For now, this is a placeholder for the implementation
      
      // Example: Fetch most active stories
      // const popularStories = await DatabaseAdapter.getPopularStories();
      // for (const story of popularStories) {
      //   await cacheService.setStory(story.id, story);
      //   await cacheService.setStoryWithContributions(story.id, story);
      // }
      
      console.log('🔥 Cache warming completed');
    } catch (error) {
      console.error('❌ Cache warming error:', error);
    }
  }

  // Cache statistics and monitoring
  static async getCacheStats() {
    return cacheService.getStats();
  }

  static async getCacheHealth() {
    return cacheService.healthCheck();
  }

  // Manual cache management (for admin/debugging)
  static async clearStoryCache(storyId: string) {
    await cacheService.invalidateStoryCache(storyId);
    console.log(`🗑️ Cleared cache for story ${storyId}`);
  }

  static async clearAllCache() {
    // This would clear all cache entries
    await cacheService.deletePattern('*');
    console.log('🗑️ Cleared entire cache');
  }

  // Cost optimization method - preload critical data
  static async preloadCriticalData(storyId: string) {
    console.log(`🚀 Preloading critical data for story ${storyId}`);
    
    // Preload story and contributions in parallel
    const [storyResult, contributionsResult] = await Promise.all([
      this.getStory(storyId),
      this.getStoryWithContributions(storyId)
    ]);

    if (storyResult.data && contributionsResult.data) {
      console.log(`✅ Preloaded story ${storyId} into cache`);
      return true;
    }
    
    return false;
  }
}

// Export both the cached adapter and original adapter
export { DatabaseAdapter as OriginalDatabaseAdapter };
export default CachedDatabaseAdapter; 