/// <reference types="cypress" />
/// <reference types="cypress-axe" />

// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Import accessibility testing
import 'cypress-axe'

// Custom commands for the Word by Word Story app

declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Custom command to login with email and password
       * @param email - User email
       * @param password - User password
       */
      login(email: string, password: string): Chainable<void>
      
      /**
       * Custom command to register a new user
       * @param email - User email
       * @param password - User password
       * @param username - Username
       */
      register(email: string, password: string, username: string): Chainable<void>
      
      /**
       * Custom command to create a test story
       * @param title - Story title
       * @param description - Story description
       */
      createStory(title: string, description: string): Chainable<void>
      
      /**
       * Custom command to contribute to a story
       * @param contribution - The word/sentence to contribute
       */
      contributeToStory(contribution: string): Chainable<void>
      
      /**
       * Custom command to navigate and wait for page load
       * @param path - Path to navigate to
       */
      visitAndWait(path: string): Chainable<void>
      
      /**
       * Custom command to wait for loading states to finish
       */
      waitForLoadingToFinish(): Chainable<void>
      
      /**
       * Custom command to mock Supabase responses
       */
      mockSupabaseAuth(): Chainable<void>
      
      /**
       * Custom command to check accessibility
       * @param context - Element to test (optional)
       * @param options - Axe options (optional)
       */
      checkAccessibility(context?: string, options?: any): Chainable<void>
      
      /**
       * Custom command to test browser compatibility
       */
      checkBrowserCompatibility(): Chainable<void>
    }
  }
}

// Login command
Cypress.Commands.add('login', (email: string, password: string) => {
  cy.visit('/login')
  cy.get('[data-testid="email-input"]').type(email)
  cy.get('[data-testid="password-input"]').type(password)
  cy.get('[data-testid="login-button"]').click()
  cy.waitForLoadingToFinish()
})

// Register command
Cypress.Commands.add('register', (email: string, password: string, username: string) => {
  cy.visit('/register')
  cy.get('[data-testid="username-input"]').type(username)
  cy.get('[data-testid="email-input"]').type(email)
  cy.get('[data-testid="password-input"]').type(password)
  cy.get('[data-testid="confirm-password-input"]').type(password)
  cy.get('[data-testid="register-button"]').click()
  cy.waitForLoadingToFinish()
})

// Create story command
Cypress.Commands.add('createStory', (title: string, description: string) => {
  cy.visit('/create-story')
  cy.get('[data-testid="story-title-input"]').type(title)
  cy.get('[data-testid="story-description-input"]').type(description)
  cy.get('[data-testid="create-story-button"]').click()
  cy.waitForLoadingToFinish()
})

// Contribute to story command
Cypress.Commands.add('contributeToStory', (contribution: string) => {
  cy.get('[data-testid="contribution-input"]').type(contribution)
  cy.get('[data-testid="submit-contribution-button"]').click()
  cy.waitForLoadingToFinish()
})

// Visit and wait command
Cypress.Commands.add('visitAndWait', (path: string) => {
  cy.visit(path)
  cy.waitForLoadingToFinish()
})

// Wait for loading to finish
Cypress.Commands.add('waitForLoadingToFinish', () => {
  // Wait for any loading spinners to disappear
  cy.get('[data-testid="loading-spinner"]', { timeout: 10000 }).should('not.exist')
  // Wait for any skeleton loaders to disappear
  cy.get('.animate-pulse', { timeout: 10000 }).should('not.exist')
  // Ensure the page is fully loaded
  cy.get('body').should('be.visible')
})

// Mock Supabase auth for testing
Cypress.Commands.add('mockSupabaseAuth', () => {
  // Mock successful authentication
  cy.window().then((win) => {
    // Mock the Supabase auth object
    win.localStorage.setItem('supabase.auth.token', JSON.stringify({
      access_token: 'mock-token',
      refresh_token: 'mock-refresh-token',
      expires_at: Date.now() + 3600000, // 1 hour from now
      user: {
        id: 'mock-user-id',
        email: '<EMAIL>',
        user_metadata: {
          username: 'testuser'
        }
      }
    }))
  })
})

// Accessibility testing command
Cypress.Commands.add('checkAccessibility', (context?: string, options?: any) => {
  const defaultOptions = {
    runOnly: {
      type: 'tag',
      values: ['wcag2a', 'wcag2aa']
    },
    rules: {
      'color-contrast': { enabled: true },
      'keyboard-navigation': { enabled: true },
      'focus-management': { enabled: true },
      'aria-labels': { enabled: true }
    }
  }
  
  const mergedOptions = { ...defaultOptions, ...options }
  
  cy.injectAxe()
  cy.checkA11y(context, mergedOptions, (violations) => {
    if (violations.length > 0) {
      cy.task('log', 'Accessibility violations found:')
      cy.task('table', violations.map(v => ({
        id: v.id,
        impact: v.impact,
        description: v.description,
        nodes: v.nodes.length
      })))
    }
  })
})

// Browser compatibility testing command
Cypress.Commands.add('checkBrowserCompatibility', () => {
  cy.window().then((win) => {
    const browser = Cypress.browser
    
    // Log browser information
    cy.task('log', `Testing compatibility on: ${browser.displayName} ${browser.version}`)
    
    // Check essential features
    const features = {
      localStorage: typeof win.localStorage !== 'undefined',
      sessionStorage: typeof win.sessionStorage !== 'undefined',
      flexbox: CSS.supports('display', 'flex'),
      grid: CSS.supports('display', 'grid'),
      customProperties: CSS.supports('--test', 'value'),
      es6: typeof Symbol !== 'undefined',
      fetch: typeof win.fetch !== 'undefined',
      promises: typeof Promise !== 'undefined'
    }
    
    cy.task('log', 'Browser feature support:')
    cy.task('table', features)
    
    // Assert critical features
    expect(features.localStorage).to.be.true
    expect(features.sessionStorage).to.be.true
    expect(features.flexbox).to.be.true
    expect(features.es6).to.be.true
    expect(features.fetch).to.be.true
    expect(features.promises).to.be.true
  })
})