import { Router } from 'express';
import * as apiKeyController from '../api/apiKeys';
import ApiAuthMiddleware from '../middleware/apiAuthMiddleware';
import UserAuthMiddleware from '../middleware/userAuthMiddleware';
import { APICacheMiddleware } from '../middleware/cacheMiddleware';
import rateLimit from 'express-rate-limit';

const router = Router();

// Rate limiting for API key management endpoints
const apiKeyManagementLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    error: 'Too many API key management requests',
    message: 'Please try again later',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Strict rate limiting for key creation/rotation (more sensitive operations)
const keyCreationLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // Limit each IP to 10 key creations per hour
  message: {
    error: 'Too many API key creation attempts',
    message: 'Please try again later',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Apply rate limiting to all routes
router.use(apiKeyManagementLimiter);

/**
 * Public Routes (no authentication required)
 */

// GET /api/keys/pricing - Get pricing information for API tiers
router.get('/pricing', 
  APICacheMiddleware.cache(), // Cache pricing info for 1 hour by default
  apiKeyController.getPricingInfo
);

// GET /api/keys/health - Health check for API key service
router.get('/health',
  ApiAuthMiddleware.healthCheck()
);

// GET /api/keys/auth/health - Health check for user auth service
router.get('/auth/health',
  UserAuthMiddleware.healthCheck()
);

/**
 * Protected Routes (require authentication)
 * 
 * Note: In a real application, these would be protected by user authentication
 * middleware that validates JWT tokens or session cookies to ensure the user
 * is logged in and can only access their own API keys.
 */

// POST /api/keys - Create a new API key
router.post('/',
  keyCreationLimiter, // Extra strict rate limiting
  UserAuthMiddleware.requireUser(), // Require authenticated user
  APICacheMiddleware.invalidateOnMutation(), // Clear cache after creation
  apiKeyController.createApiKey
);

// GET /api/keys - Get all API keys for the authenticated user
router.get('/',
  UserAuthMiddleware.requireUser(), // Require authenticated user
  APICacheMiddleware.cache(), // Cache user's API keys for 5 minutes
  apiKeyController.getUserApiKeys
);

// GET /api/keys/:keyId - Get specific API key details
router.get('/:keyId',
  UserAuthMiddleware.requireUser(), // Require authenticated user
  UserAuthMiddleware.requireApiKeyOwnership, // Ensure user owns the API key
  APICacheMiddleware.cache(), // Cache key details for 5 minutes
  apiKeyController.getApiKey
);

// PUT /api/keys/:keyId - Update API key settings
router.put('/:keyId',
  UserAuthMiddleware.requireUser(), // Require authenticated user
  UserAuthMiddleware.requireApiKeyOwnership, // Ensure user owns the API key
  APICacheMiddleware.invalidateOnMutation(), // Clear cache after update
  apiKeyController.updateApiKey
);

// POST /api/keys/:keyId/rotate - Rotate API key
router.post('/:keyId/rotate',
  keyCreationLimiter, // Extra strict rate limiting
  UserAuthMiddleware.requireUser(), // Require authenticated user
  UserAuthMiddleware.requireApiKeyOwnership, // Ensure user owns the API key
  APICacheMiddleware.invalidateOnMutation(), // Clear cache after rotation
  apiKeyController.rotateApiKey
);

// POST /api/keys/:keyId/deactivate - Deactivate API key
router.post('/:keyId/deactivate',
  UserAuthMiddleware.requireUser(), // Require authenticated user
  UserAuthMiddleware.requireApiKeyOwnership, // Ensure user owns the API key
  APICacheMiddleware.invalidateOnMutation(), // Clear cache after deactivation
  apiKeyController.deactivateApiKey
);

// GET /api/keys/:keyId/usage - Get API key usage statistics
router.get('/:keyId/usage',
  UserAuthMiddleware.requireUser(), // Require authenticated user
  UserAuthMiddleware.requireApiKeyOwnership, // Ensure user owns the API key
  APICacheMiddleware.cache(), // Cache usage stats for 5 minutes
  apiKeyController.getApiKeyUsage
);

// GET /api/keys/:keyId/billing - Get billing report for API key
router.get('/:keyId/billing',
  UserAuthMiddleware.requireUser(), // Require authenticated user
  UserAuthMiddleware.requireApiKeyOwnership, // Ensure user owns the API key
  APICacheMiddleware.cache(), // Cache billing data for 30 minutes
  apiKeyController.getBillingReport
);

// GET /api/keys/billing/summary - Get comprehensive billing summary for user
router.get('/billing/summary',
  UserAuthMiddleware.requireUser(), // Require authenticated user
  APICacheMiddleware.cache(), // Cache billing summary for 30 minutes
  apiKeyController.getBillingSummary
);

/**
 * Protected API Usage Endpoint
 * This endpoint uses the API key authentication middleware we built
 */

// GET /api/keys/my/usage - Get usage for the current API key
router.get('/my/usage',
  ApiAuthMiddleware.requireApiKey(), // Require valid API key
  ApiAuthMiddleware.usageReport()
);

/**
 * Premium Endpoints (require premium subscription)
 */

// GET /api/keys/analytics - Advanced analytics (premium only)
router.get('/analytics',
  ApiAuthMiddleware.requirePremium(), // Require premium subscription
  APICacheMiddleware.cache(), // Cache analytics for 10 minutes
  (req, res) => {
    res.json({
      message: 'Premium analytics endpoint',
      tier: req.tier,
      keyPreview: req.apiKey?.keyPreview,
      analytics: {
        totalRequests: 12500,
        avgResponseTime: 250,
        cacheHitRate: 85.4,
        topEndpoints: [
          { endpoint: 'GET:/api/stories', requests: 5000 },
          { endpoint: 'POST:/api/stories/:id/contributions', requests: 3500 },
          { endpoint: 'GET:/api/stories/:id', requests: 4000 }
        ],
        costSavings: {
          cached: '$15.75',
          optimizations: '$8.25'
        }
      }
    });
  }
);

/**
 * Enterprise Endpoints (require enterprise subscription)
 */

// GET /api/keys/enterprise/dashboard - Enterprise dashboard
router.get('/enterprise/dashboard',
  ApiAuthMiddleware.requireEnterprise(), // Require enterprise subscription
  APICacheMiddleware.cache(), // Cache enterprise dashboard for 10 minutes
  (req, res) => {
    res.json({
      message: 'Enterprise dashboard endpoint',
      tier: req.tier,
      keyPreview: req.apiKey?.keyPreview,
      dashboard: {
        multiTenantUsage: true,
        dedicatedSupport: true,
        customIntegrations: ['webhook-callbacks', 'bulk-imports'],
        slaMetrics: {
          uptime: '99.95%',
          avgResponseTime: '95ms',
          supportResponseTime: '< 1 hour'
        }
      }
    });
  }
);

// Error handling middleware
router.use((error: Error, req: any, res: any, next: any) => {
  console.error('❌ API Key Routes Error:', error);
  
  res.status(500).json({
    error: 'Internal server error',
    message: 'An error occurred while processing your API key request',
    timestamp: new Date().toISOString()
  });
});

export default router;

/**
 * Example Usage:
 * 
 * 1. Create API Key:
 * POST /api/keys
 * {
 *   "name": "Production API",
 *   "tier": "premium",
 *   "description": "API key for production environment",
 *   "allowedOrigins": ["https://myapp.com"]
 * }
 * 
 * 2. Use API Key:
 * GET /api/stories
 * Headers: { "Authorization": "Bearer wws_abc12345_xyz789_..." }
 * 
 * 3. Check Usage:
 * GET /api/keys/my/usage
 * Headers: { "x-api-key": "wws_abc12345_xyz789_..." }
 * 
 * 4. Upgrade to Premium:
 * - User pays $49.99/month via Stripe
 * - System automatically upgrades their tier
 * - API key gains access to premium features
 * 
 * 5. Monitor Billing:
 * GET /api/keys/abc12345/billing?month=12&year=2024
 * Headers: { "Authorization": "Bearer jwt_token" }
 */ 