import React, { useRef, useEffect, useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import MessageBubble from './MessageBubble';
import TypingIndicator from './TypingIndicator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { ArrowDown, Users, BookOpen } from 'lucide-react';

export interface Contribution {
  id: string;
  content: string;
  author: {
    id: string;
    username: string;
    avatar_url?: string;
  };
  created_at: string;
  position: number;
  special_type?: 'gotcha' | 'reverse' | 'golden' | null;
  token_cost?: number;
  reactions?: Array<{
    type: 'heart' | 'star' | 'laugh';
    count: number;
    users: string[];
  }>;
}

export interface Participant {
  id: string;
  username: string;
  avatar_url?: string;
  role: 'creator' | 'contributor' | 'viewer';
  isOnline: boolean;
  lastSeen?: Date;
  contributionCount: number;
  joinedAt: Date;
  isTyping?: boolean;
}

export interface TypingUser {
  id: string;
  username: string;
  avatar_url?: string;
}

export interface StoryThreadProps {
  storyId: string;
  storyTitle: string;
  contributions: Contribution[];
  participants?: Participant[];
  currentUserId: string;
  typingUsers?: TypingUser[];
  onToggleStoryView?: () => void;
  onToggleParticipants?: () => void;
  onAuthorClick?: (authorId: string) => void;
  headerActions?: React.ReactNode;
  className?: string;
}

export const StoryThread: React.FC<StoryThreadProps> = ({
  storyId,
  storyTitle,
  contributions,
  participants = [],
  currentUserId,
  typingUsers = [],
  onToggleStoryView,
  onToggleParticipants,
  onAuthorClick,
  headerActions,
  className,
}) => {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [autoScroll, setAutoScroll] = useState(true);

  // Enhanced function to get participant info for an author
  const getEnhancedAuthorInfo = useCallback((author: Contribution['author']) => {
    const participant = participants.find(p => p.id === author.id);
    
    return {
      ...author,
      role: participant?.role || 'contributor',
      isOnline: participant?.isOnline ?? true, // Default to online if not found
    };
  }, [participants]);

  // Handle author click
  const handleAuthorClick = useCallback((authorId: string) => {
    if (onAuthorClick) {
      onAuthorClick(authorId);
    } else {
      // Default behavior - could show user profile or info
      console.log('Author clicked:', authorId);
    }
  }, [onAuthorClick]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    const scrollArea = scrollAreaRef.current;
    if (!scrollArea || !autoScroll) return;

    const scrollToBottom = () => {
      const scrollContainer = scrollArea.querySelector('[data-radix-scroll-area-viewport]');
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    };

    // Small delay to ensure content is rendered
    const timeoutId = setTimeout(scrollToBottom, 100);
    return () => clearTimeout(timeoutId);
  }, [contributions.length, autoScroll]);

  // Handle scroll events
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
    const isNearBottom = scrollTop + clientHeight >= scrollHeight - 100;
    
    setShowScrollButton(!isNearBottom);
    setAutoScroll(isNearBottom);
  }, []);

  // Scroll to bottom manually
  const scrollToBottom = useCallback(() => {
    const scrollArea = scrollAreaRef.current;
    if (!scrollArea) return;

    const scrollContainer = scrollArea.querySelector('[data-radix-scroll-area-viewport]');
    if (scrollContainer) {
      scrollContainer.scrollTo({
        top: scrollContainer.scrollHeight,
        behavior: 'smooth',
      });
      setAutoScroll(true);
    }
  }, []);

  return (
    <div className={cn('flex flex-col h-full bg-background', className)}>
      {/* Enhanced Header */}
      <div className="flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur-sm">
        <div className="flex items-center gap-3">
          <div>
            <h2 className="font-semibold text-lg">{storyTitle}</h2>
            <p className="text-sm text-muted-foreground">
              {contributions.length} contribution{contributions.length !== 1 ? 's' : ''}
              {participants.length > 0 && (
                <> • {participants.filter(p => p.isOnline).length} online</>
              )}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggleParticipants}
            className="h-9 w-9"
            aria-label="Participants"
          >
            <Users className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggleStoryView}
            className="h-9 w-9"
            aria-label="Story View"
          >
            <BookOpen className="h-4 w-4" />
          </Button>
          {headerActions}
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 relative">
        <ScrollArea 
          ref={scrollAreaRef}
          className="h-full"
          onScrollCapture={handleScroll}
        >
          <div className="p-4 space-y-4">
            {/* Welcome Message */}
            {contributions.length === 0 && (
              <div className="text-center py-8">
                <BookOpen className="w-12 h-12 mx-auto text-muted-foreground/50 mb-4" />
                <h3 className="text-lg font-semibold mb-2">Start Your Story</h3>
                <p className="text-muted-foreground max-w-md mx-auto">
                  Be the first to contribute to "{storyTitle}". Every great story begins with a single word.
                </p>
              </div>
            )}

            {/* Message List */}
            {contributions.map((contribution) => {
              const enhancedAuthor = getEnhancedAuthorInfo(contribution.author);
              
              return (
                <MessageBubble
                  key={contribution.id}
                  id={contribution.id}
                  content={contribution.content}
                  author={enhancedAuthor}
                  timestamp={new Date(contribution.created_at)}
                  isCurrentUser={contribution.author.id === currentUserId}
                  position={contribution.position}
                  specialType={contribution.special_type}
                  tokenCost={contribution.token_cost}
                  reactions={contribution.reactions}
                  onAuthorClick={handleAuthorClick}
                />
              );
            })}

            {/* Typing Indicators */}
            {typingUsers.length > 0 && (
              <div className="flex gap-3">
                <div className="w-10 h-10" /> {/* Spacer for avatar alignment */}
                <TypingIndicator users={typingUsers} />
              </div>
            )}
          </div>
        </ScrollArea>

        {/* Scroll to Bottom Button */}
        {showScrollButton && (
          <Button
            onClick={scrollToBottom}
            size="icon"
            className={cn(
              'absolute bottom-4 right-4 h-10 w-10 rounded-full shadow-lg',
              'bg-primary text-primary-foreground hover:bg-primary/90',
              'transition-all duration-200 animate-in slide-in-from-bottom-2'
            )}
          >
            <ArrowDown className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
};

export default StoryThread;