/**
 * Cookie Consent Management Utility
 * Handles GDPR/CCPA compliant cookie consent for analytics and ads
 */

export interface CookieConsentSettings {
  necessary: boolean; // Always true - required for basic functionality
  analytics: boolean; // PostHog, Google Analytics, etc.
  marketing: boolean; // Ad tracking, marketing pixels
  functional: boolean; // User preferences, saved settings
  consentDate: Date;
  version: string; // Track consent version for future updates
}

const CONSENT_KEY = 'cookie_consent_settings';
const CONSENT_VERSION = '1.0';

/**
 * Default cookie settings (necessary only)
 */
const DEFAULT_SETTINGS: CookieConsentSettings = {
  necessary: true,
  analytics: false,
  marketing: false,
  functional: false,
  consentDate: new Date(),
  version: CONSENT_VERSION,
};

/**
 * Get current cookie consent settings
 */
export const getCookieConsent = (): CookieConsentSettings | null => {
  try {
    const stored = localStorage.getItem(CONSENT_KEY);
    if (!stored) return null;
    
    const parsed = JSON.parse(stored);
    return {
      ...parsed,
      consentDate: new Date(parsed.consentDate),
    };
  } catch (error) {
    console.warn('Failed to parse cookie consent settings:', error);
    return null;
  }
};

/**
 * Store cookie consent settings
 */
export const setCookieConsent = (settings: Partial<CookieConsentSettings>): void => {
  try {
    const fullSettings: CookieConsentSettings = {
      ...DEFAULT_SETTINGS,
      ...settings,
      consentDate: new Date(),
      version: CONSENT_VERSION,
    };
    
    localStorage.setItem(CONSENT_KEY, JSON.stringify({
      ...fullSettings,
      consentDate: fullSettings.consentDate.toISOString(),
    }));
    
    // Dispatch custom event for other components to listen to
    window.dispatchEvent(new CustomEvent('cookieConsentChanged', {
      detail: fullSettings
    }));
  } catch (error) {
    console.warn('Failed to store cookie consent settings:', error);
  }
};

/**
 * Check if user has given consent for a specific category
 */
export const hasConsentFor = (category: keyof Omit<CookieConsentSettings, 'consentDate' | 'version'>): boolean => {
  const consent = getCookieConsent();
  return consent ? consent[category] : false;
};

/**
 * Check if user needs to see the consent banner
 */
export const needsConsentBanner = (): boolean => {
  const consent = getCookieConsent();
  
  // Show banner if no consent given or version mismatch
  return !consent || consent.version !== CONSENT_VERSION;
};

/**
 * Accept all cookies
 */
export const acceptAllCookies = (): void => {
  setCookieConsent({
    necessary: true,
    analytics: true,
    marketing: true,
    functional: true,
  });
};

/**
 * Accept only necessary cookies
 */
export const acceptNecessaryOnly = (): void => {
  setCookieConsent({
    necessary: true,
    analytics: false,
    marketing: false,
    functional: false,
  });
};

/**
 * Accept custom cookie settings
 */
export const acceptCustomSettings = (settings: Partial<CookieConsentSettings>): void => {
  setCookieConsent({
    necessary: true, // Always required
    ...settings,
  });
};

/**
 * Clear all consent (for testing)
 */
export const clearCookieConsent = (): void => {
  try {
    localStorage.removeItem(CONSENT_KEY);
    window.dispatchEvent(new CustomEvent('cookieConsentChanged', {
      detail: null
    }));
  } catch (error) {
    console.warn('Failed to clear cookie consent:', error);
  }
}; 