// This would be deployed as a Supabase Edge Function
// File: supabase/functions/check-subscription/index.js

import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import Stripe from "https://esm.sh/stripe@14.21.0";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

// Helper logging function for enhanced debugging
const logStep = (step, details) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : "";
  console.log(`[CHECK-SUBSCRIPTION] ${step}${detailsStr}`);
};

// Map price IDs to subscription tiers
const priceTierMap = {
  price_wordsmith: "wordsmith",
  price_storyteller: "storyteller",
  price_authors_guild: "authors-guild",
  // Add your actual price IDs here once you have them from Stripe
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    logStep("Function started");

    const stripeKey = Deno.env.get("STRIPE_SECRET_KEY");
    if (!stripeKey) throw new Error("STRIPE_SECRET_KEY is not set");

    // Use the service role key to perform writes (upsert) in Supabase
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") ?? "",
      { auth: { persistSession: false } },
    );

    // Authenticate user
    const authHeader = req.headers.get("Authorization");
    if (!authHeader) throw new Error("No authorization header provided");

    const token = authHeader.replace("Bearer ", "");
    const { data: userData, error: userError } =
      await supabaseClient.auth.getUser(token);
    if (userError)
      throw new Error(`Authentication error: ${userError.message}`);

    const user = userData.user;
    if (!user?.email) throw new Error("User email not available");
    logStep("User authenticated", { userId: user.id, email: user.email });

    // Initialize Stripe
    const stripe = new Stripe(stripeKey, { apiVersion: "2023-10-16" });

    // Check if user already exists as a customer
    const customers = await stripe.customers.list({
      email: user.email,
      limit: 1,
    });
    if (customers.data.length === 0) {
      // No customer record - user is not subscribed
      logStep("No customer found");

      // Update user profile in Supabase
      await supabaseClient
        .from("profiles")
        .update({
          tier: "free",
          tier_expiry: null,
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id);

      return new Response(JSON.stringify({ subscribed: false, tier: "free" }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    const customerId = customers.data[0].id;
    logStep("Found customer", { customerId });

    // Check for active subscriptions
    const subscriptions = await stripe.subscriptions.list({
      customer: customerId,
      status: "active",
      limit: 1,
    });

    if (subscriptions.data.length === 0) {
      // No active subscription
      logStep("No active subscription");

      // Update user profile in Supabase
      await supabaseClient
        .from("profiles")
        .update({
          tier: "free",
          tier_expiry: null,
          updated_at: new Date().toISOString(),
        })
        .eq("id", user.id);

      return new Response(JSON.stringify({ subscribed: false, tier: "free" }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    // User has active subscription
    const subscription = subscriptions.data[0];
    const priceId = subscription.items.data[0].price.id;
    const tier = priceTierMap[priceId] || "free";
    const expiryDate = new Date(
      subscription.current_period_end * 1000,
    ).toISOString();

    logStep("Active subscription found", {
      subscriptionId: subscription.id,
      priceId,
      tier,
      expiryDate,
    });

    // Update user profile in Supabase
    await supabaseClient
      .from("profiles")
      .update({
        tier: tier,
        tier_expiry: expiryDate,
        updated_at: new Date().toISOString(),
      })
      .eq("id", user.id);

    logStep("Updated user profile", { tier, expiryDate });

    return new Response(
      JSON.stringify({
        subscribed: true,
        tier: tier,
        expiryDate: expiryDate,
      }),
      { headers: { ...corsHeaders, "Content-Type": "application/json" } },
    );
  } catch (error) {
    logStep("ERROR", { message: error.message });
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 400,
    });
  }
});
