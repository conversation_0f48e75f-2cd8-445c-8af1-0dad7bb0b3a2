import React from "react";
import { useAuth } from "@/contexts/auth";
import { useSubscription } from "@/hooks/use-subscription";
import { RotatingAdvertisement } from "@/components/ads/RotatingAdvertisement";
import { useAds } from "@/contexts/AdsContext";
import { PricingHeader } from "@/components/pricing/PricingHeader";
import { CreditsCard } from "@/components/pricing/CreditsCard";
import { CreditsInfo } from "@/components/pricing/CreditsInfo";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { useDocumentTitle, createPageTitle } from "@/hooks/useDocumentTitle";

const Pricing = () => {
  const { user } = useAuth();
  const { isAdFreeUser } = useAds();
  const { handleBuyCredits, isLoading, credits } = useSubscription();

  // Set page title
  useDocumentTitle(createPageTitle("Credits & Pricing"));

  return (
    <div className="max-w-6xl mx-auto pb-16">
      <PricingHeader />

      {(!user || !isAdFreeUser) && (
        <RotatingAdvertisement placement="header" className="mb-6 max-w-4xl mx-auto" />
      )}

      {user && <CreditsCard credits={credits} />}

      <div className="max-w-4xl mx-auto mb-8">
        <Card className="border-literary-gold/30 mb-8">
          <CardHeader className="border-b border-literary-gold/10 bg-literary-paper/50">
            <CardTitle className="text-xl font-serif text-literary-navy">
              Credits Rate Card
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white p-4 rounded-lg border border-gray-100 shadow-sm flex flex-col items-center text-center hover:shadow-md transition-shadow">
                <div className="font-bold text-2xl text-literary-navy mb-1">
                  20 Credits
                </div>
                <div className="text-lg font-bold text-literary-gold mb-3">
                  $0.99
                </div>
                <div className="text-gray-500 text-sm">$0.05 per credit</div>
              </div>

              <div className="bg-white p-4 rounded-lg border border-literary-gold shadow-sm flex flex-col items-center text-center hover:shadow-md transition-shadow">
                <div className="font-bold text-2xl text-literary-navy mb-1">
                  100 Credits
                </div>
                <div className="text-lg font-bold text-literary-gold mb-3">
                  $3.99
                </div>
                <div className="text-gray-500 text-sm">$0.04 per credit</div>
                <div className="mt-2 bg-amber-50 text-amber-800 text-xs px-2 py-1 rounded">
                  Most Popular
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg border border-gray-100 shadow-sm flex flex-col items-center text-center hover:shadow-md transition-shadow">
                <div className="font-bold text-2xl text-literary-navy mb-1">
                  500 Credits
                </div>
                <div className="text-lg font-bold text-literary-gold mb-3">
                  $14.99
                </div>
                <div className="text-gray-500 text-sm">$0.03 per credit</div>
                <div className="mt-2 bg-green-50 text-green-800 text-xs px-2 py-1 rounded">
                  Best Value
                </div>
              </div>
            </div>

            <div className="mt-4 text-center text-sm text-gray-600">
              Credits can be used flexibly across all features. Purchase once,
              use anywhere.
            </div>
          </CardContent>
        </Card>
      </div>

      <CreditsInfo />

      {(!user || !isAdFreeUser) && (
        <RotatingAdvertisement placement="footer" className="mt-8 max-w-4xl mx-auto" />
      )}
    </div>
  );
};
export default Pricing;
