import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_ANON_KEY for subscribe-story function.');
}

async function getAuthenticatedUser(req: Request, supabase: SupabaseClient) {
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    console.error('Auth error or no user for subscribe-story:', authError);
    throw { message: 'User not authenticated or auth error.', status: 401 };
  }
  return user;
}

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed. Use POST.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 405,
    });
  }

  try {
    const supabase = createClient(supabaseUrl!, supabaseAnonKey!, {
      global: { headers: { Authorization: req.headers.get('Authorization')! } },
      auth: { persistSession: false }
    });

    const user = await getAuthenticatedUser(req, supabase);

    const url = new URL(req.url);
    const storyId = url.searchParams.get('story_id');

    if (!storyId) {
      return new Response(JSON.stringify({ error: 'story_id query parameter is required.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    // Optional: Check if story exists
    const { data: storyData, error: storyError } = await supabase
        .from('stories')
        .select('id')
        .eq('id', storyId)
        .maybeSingle();

    if (storyError) {
        console.error("Error checking story existence for subscription:", storyError);
        // Proceed, FK constraint on subscriptions table will catch non-existent story_id
    }
    if (!storyError && !storyData) {
        return new Response(JSON.stringify({ error: 'Story not found.' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 404,
        });
    }

    const subscriptionData = {
      story_id: storyId,
      user_id: user.id,
    };

    // Attempt to insert. If it conflicts, it means subscription already exists.
    // RLS: "Allow user to create their own subscriptions" -> WITH CHECK (auth.uid() = user_id)
    const { data, error, status } = await supabase
      .from('subscriptions')
      .insert(subscriptionData)
      .select() // Select the inserted/conflicting row
      .maybeSingle(); // Use maybeSingle as insert might not return data on conflict depending on DB/client version behavior with ignoreDuplicates or resolution

    if (error) {
      // PostgreSQL unique violation error code is '23505'
      if (error.code === '23505') {
        // Subscription already exists. Fetch it to return.
        const { data: existingSub, error: fetchError } = await supabase
            .from('subscriptions')
            .select('*')
            .eq('story_id', storyId)
            .eq('user_id', user.id)
            .single();
        
        if (fetchError) {
             console.error('Error fetching existing subscription after conflict:', fetchError);
             return new Response(JSON.stringify({ error: 'Subscription already exists, but failed to retrieve it.', details: fetchError.message }), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                status: 500,
             });
        }
        return new Response(JSON.stringify(existingSub), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200, // OK, already subscribed
        });
      }
      
      console.error('Supabase insert subscription error:', error);
      return new Response(JSON.stringify({ error: error.message, details: error.details, hint: error.hint }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: (error as any).status || 500, 
      });
    }
    
    // If data is null and no error, it might mean insert was ignored due to onConflict strategy (if one was set at DB level or via client)
    // For an explicit insert without upsert/onConflict client-side, a successful insert returns data.
    if (!data) {
        // This case might occur if RLS prevented the insert silently or some other unexpected DB behavior.
        // Or if the client was configured to ignore duplicates and returned nothing.
        // For robustness, try fetching it to confirm.
        const { data: confirmSub, error: confirmError } = await supabase
            .from('subscriptions')
            .select('*')
            .eq('story_id', storyId)
            .eq('user_id', user.id)
            .single();

        if (confirmError || !confirmSub) {
             console.error('Subscription failed or not found after attempt:', confirmError);
             return new Response(JSON.stringify({ error: 'Failed to create or confirm subscription.' }), {
                headers: { ...corsHeaders, 'Content-Type': 'application/json' },
                status: 500,
             });
        }
         return new Response(JSON.stringify(confirmSub), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200, // OK, confirmed it exists
        });
    }


    return new Response(JSON.stringify(data), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 201, // Created
    });

  } catch (err) {
    console.error('Error processing subscribe-story request:', err);
    const statusCode = typeof err.status === 'number' ? err.status : 500;
    return new Response(JSON.stringify({ error: err.message || 'An unexpected error occurred.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: statusCode,
    });
  }
}); 