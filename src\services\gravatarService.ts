import CryptoJS from 'crypto-js';

export interface GravatarOptions {
  size?: number; // Size in pixels (1-2048)
  default?: 'mp' | '404' | 'identicon' | 'monsterid' | 'wavatar' | 'retro' | 'robohash' | 'blank';
  rating?: 'g' | 'pg' | 'r' | 'x'; // Content rating
  forceDefault?: boolean; // Force default image even if Gravatar exists
}

export interface AvatarPreference {
  source: 'gravatar' | 'custom' | 'default';
  customUrl?: string;
  useGravatar: boolean;
}

/**
 * Service for handling Gravatar integration and avatar management
 */
export class GravatarService {
  private static readonly GRAVATAR_BASE_URL = 'https://www.gravatar.com/avatar/';
  private static readonly DEFAULT_SIZE = 80;
  private static readonly DEFAULT_RATING = 'g';
  private static readonly DEFAULT_FALLBACK = 'mp'; // Mystery person

  /**
   * Generate proper MD5 hash for Gravatar (required by Gravatar API)
   */
  private static generateEmailHash(email: string): string {
    if (!email || typeof email !== 'string') {
      return '';
    }
    
    // Gravatar requires: lowercase, trimmed email with MD5 hash
    const normalizedEmail = email.trim().toLowerCase();
    
    // Generate MD5 hash using crypto-js
    return CryptoJS.MD5(normalizedEmail).toString();
  }

  /**
   * Generate Gravatar URL for an email address
   */
  static generateGravatarUrl(email: string, options: GravatarOptions = {}): string {
    const {
      size = this.DEFAULT_SIZE,
      default: defaultImage = this.DEFAULT_FALLBACK,
      rating = this.DEFAULT_RATING,
      forceDefault = false,
    } = options;

    const hash = this.generateEmailHash(email);
    
    if (!hash) {
      return this.getDefaultAvatarUrl();
    }

    const params = new URLSearchParams({
      s: size.toString(),
      d: defaultImage,
      r: rating,
    });

    if (forceDefault) {
      params.set('f', 'y');
    }

    return `${this.GRAVATAR_BASE_URL}${hash}?${params.toString()}`;
  }

  /**
   * Check if a Gravatar exists for an email address
   */
  static async checkGravatarExists(email: string): Promise<boolean> {
    try {
      const url = this.generateGravatarUrl(email, { default: '404' });
      const response = await fetch(url, { method: 'HEAD' });
      return response.status === 200;
    } catch (error) {
      console.warn('Error checking Gravatar existence:', error);
      return false;
    }
  }

  /**
   * Get the best avatar URL based on user preferences
   */
  static async getBestAvatarUrl(
    email: string,
    preferences: AvatarPreference,
    options: GravatarOptions = {}
  ): Promise<string> {
    // If user doesn't want Gravatar, use their preference
    if (!preferences.useGravatar) {
      return preferences.source === 'custom' && preferences.customUrl 
        ? preferences.customUrl 
        : this.getDefaultAvatarUrl();
    }

    // Check user's preferred source
    switch (preferences.source) {
      case 'custom':
        return preferences.customUrl || this.getDefaultAvatarUrl();
      
      case 'default':
        return this.getDefaultAvatarUrl();
      
      case 'gravatar':
      default:
        // For demo purposes, always try Gravatar first
        return this.generateGravatarUrl(email, options);
    }
  }

  /**
   * Get default avatar URL (can be a placeholder or initial-based)
   */
  static getDefaultAvatarUrl(): string {
    // Return a basic initial avatar instead of a potentially missing file
    return this.generateInitialsAvatar('User', 80);
  }

  /**
   * Generate initials-based avatar URL as fallback
   */
  static generateInitialsAvatar(name: string, size: number = 80): string {
    const initials = this.extractInitials(name);
    const backgroundColor = this.generateColorFromString(name);
    
    // Use UI Avatars service for generated initial avatars
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(initials)}&size=${size}&background=${backgroundColor}&color=fff&bold=true`;
  }

  /**
   * Extract initials from a name
   */
  private static extractInitials(name: string): string {
    if (!name || typeof name !== 'string') {
      return 'U'; // Default for 'User'
    }

    const words = name.trim().split(/\s+/);
    if (words.length === 1) {
      return words[0].substring(0, 2).toUpperCase();
    }
    
    return words
      .slice(0, 2) // Take first two words
      .map(word => word.charAt(0).toUpperCase())
      .join('');
  }

  /**
   * Generate a consistent color from a string
   */
  private static generateColorFromString(str: string): string {
    if (!str) return '6B7280'; // Default gray

    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash);
    }

    // Generate a color that's not too light or too dark
    const colors = [
      '3B82F6', // Blue
      '10B981', // Green  
      'F59E0B', // Yellow
      'EF4444', // Red
      '8B5CF6', // Purple
      '06B6D4', // Cyan
      'F97316', // Orange
      'EC4899', // Pink
    ];

    return colors[Math.abs(hash) % colors.length];
  }

  /**
   * Validate custom avatar URL
   */
  static async validateCustomAvatarUrl(url: string): Promise<boolean> {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      const contentType = response.headers.get('content-type');
      
      return (
        response.status === 200 &&
        contentType?.startsWith('image/') === true
      );
    } catch (error) {
      console.warn('Error validating custom avatar URL:', error);
      return false;
    }
  }

  /**
   * Get multiple avatar options for user to choose from
   */
  static async getAvatarOptions(email: string, username?: string): Promise<{
    gravatar: { url: string; available: boolean };
    initials: string;
    default: string;
  }> {
    const gravatarUrl = this.generateGravatarUrl(email);
    const gravatarExists = await this.checkGravatarExists(email);
    
    return {
      gravatar: {
        url: gravatarUrl,
        available: gravatarExists,
      },
      initials: this.generateInitialsAvatar(username || email),
      default: this.getDefaultAvatarUrl(),
    };
  }
}

export const gravatarService = GravatarService; 