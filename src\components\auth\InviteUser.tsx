import React, { useState } from "react";
import { useForm } from "react-hook-form";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { UserRoundPlus, Mail, Phone } from "lucide-react";
import { useStory } from "@/contexts/StoryContext";

interface InviteUserProps {
  storyId?: string;
  onInviteSuccess?: () => void;
  trigger?: React.ReactNode;
}

const emailSchema = z.object({
  email: z.string().email({ message: "Must be a valid email address" }),
});

const phoneSchema = z.object({
  phone: z
    .string()
    .min(10, { message: "Phone number must have at least 10 digits" }),
});

const InviteUser: React.FC<InviteUserProps> = ({
  storyId,
  onInviteSuccess,
  trigger,
}) => {
  const [open, setOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("email");
  const { toast } = useToast();
  const { inviteParticipant } = useStory();
  const [isLoading, setIsLoading] = useState(false);

  const emailForm = useForm<z.infer<typeof emailSchema>>({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: "",
    },
  });

  const phoneForm = useForm<z.infer<typeof phoneSchema>>({
    resolver: zodResolver(phoneSchema),
    defaultValues: {
      phone: "",
    },
  });

  const onEmailSubmit = async (data: z.infer<typeof emailSchema>) => {
    setIsLoading(true);
    try {
      // If a storyId is provided, invite to a specific story
      if (storyId) {
        const success = await inviteParticipant(storyId, data.email);
        if (success) {
          toast({
            title: "Invitation sent",
            description: `Invitation has been sent to ${data.email}`,
          });
          setOpen(false);
          if (onInviteSuccess) onInviteSuccess();
        } else {
          toast({
            title: "Invitation failed",
            description: "Failed to send invitation. Please try again.",
            variant: "destructive",
          });
        }
      } else {
        // General invitation to join the platform
        setTimeout(() => {
          toast({
            title: "Invitation sent",
            description: `Invitation has been sent to ${data.email}`,
          });
          setOpen(false);
          if (onInviteSuccess) onInviteSuccess();
        }, 1000);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onPhoneSubmit = async (data: z.infer<typeof phoneSchema>) => {
    setIsLoading(true);
    try {
      // Format phone number
      const formattedPhone = data.phone.replace(/\D/g, "");

      // If a storyId is provided, invite to a specific story
      if (storyId) {
        const success = await inviteParticipant(storyId, formattedPhone);
        if (success) {
          toast({
            title: "Invitation sent",
            description: `SMS invitation has been sent to ${formattedPhone}`,
          });
          setOpen(false);
          if (onInviteSuccess) onInviteSuccess();
        } else {
          toast({
            title: "Invitation failed",
            description: "Failed to send invitation. Please try again.",
            variant: "destructive",
          });
        }
      } else {
        // General invitation to join the platform
        setTimeout(() => {
          toast({
            title: "Invitation sent",
            description: `SMS invitation has been sent to ${formattedPhone}`,
          });
          setOpen(false);
          if (onInviteSuccess) onInviteSuccess();
        }, 1000);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button className="flex gap-2 items-center">
            <UserRoundPlus size={16} />
            <span>Invite User</span>
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Invite to One Word Story</DialogTitle>
          <DialogDescription>
            Send an invitation to join{" "}
            {storyId ? "this story" : "One Word Story"}.
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="email" className="flex items-center gap-2">
              <Mail size={16} />
              <span>Email</span>
            </TabsTrigger>
            <TabsTrigger value="phone" className="flex items-center gap-2">
              <Phone size={16} />
              <span>Phone</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="email" className="pt-4">
            <Form {...emailForm}>
              <form
                onSubmit={emailForm.handleSubmit(onEmailSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={emailForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="<EMAIL>"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Sending..." : "Send Invitation"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </TabsContent>

          <TabsContent value="phone" className="pt-4">
            <Form {...phoneForm}>
              <form
                onSubmit={phoneForm.handleSubmit(onPhoneSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={phoneForm.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="(*************"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Sending..." : "Send SMS Invitation"}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};

export default InviteUser;
