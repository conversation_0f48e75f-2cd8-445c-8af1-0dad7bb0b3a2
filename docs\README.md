# Word by Word Story - Documentation

Welcome to the comprehensive documentation for the Word by Word Story application. This directory contains all the documentation resources you need to understand, develop, and maintain the application.

## 📚 Documentation Index

### Core Documentation

#### [API Documentation](./API_DOCUMENTATION.md)
Complete API reference covering all endpoints, authentication, request/response formats, and usage examples.

**Highlights:**
- RESTful API endpoints for stories, contributions, users, and subscriptions
- Supabase integration patterns
- Real-time subscription examples
- Rate limiting and security considerations
- Error handling and status codes

#### [Component Guide](./COMPONENT_GUIDE.md)
Comprehensive guide to all React components in the application.

**Highlights:**
- UI component library (buttons, cards, inputs, etc.)
- Feature components (story cards, contribution inputs)
- Layout components (header, sidebar, footer)
- Best practices and patterns
- TypeScript interfaces and props
- Accessibility guidelines

#### [Generated API Documentation](./generated/)
Auto-generated TypeScript documentation from code comments.

**Highlights:**
- Type definitions and interfaces
- Function signatures and parameters
- Code examples and usage
- Cross-references and links

### Interactive Documentation

#### [Storybook Components](http://localhost:6006) *(when running)*
Interactive component playground and documentation.

**Features:**
- Live component examples
- Props editing and testing
- Accessibility testing
- Responsive design testing
- Dark/light mode testing

## 🚀 Getting Started

### For Developers

1. **API Development**
   - Start with [API Documentation](./API_DOCUMENTATION.md)
   - Check the Supabase schema and RLS policies
   - Review error handling patterns

2. **Frontend Development**
   - Read the [Component Guide](./COMPONENT_GUIDE.md)
   - Explore components in Storybook: `npm run storybook`
   - Follow the established patterns and conventions

3. **Testing**
   - Unit tests with Vitest
   - E2E tests with Cypress
   - Component testing with Storybook
   - Performance testing with Lighthouse

### For Contributors

1. **Setup Documentation Environment**
   ```bash
   # Install dependencies
   npm install
   
   # Start Storybook
   npm run storybook
   
   # Generate API docs
   npm run docs:generate
   
   # Start all documentation
   npm run docs:dev
   ```

2. **Documentation Standards**
   - Add JSDoc comments to all functions and components
   - Create Storybook stories for new UI components
   - Update API documentation for new endpoints
   - Follow TypeScript best practices

## 📋 Documentation Standards

### Code Documentation

#### TypeScript/JSDoc Comments
```typescript
/**
 * Represents a story contribution in the collaborative writing system.
 * 
 * @example
 * ```typescript
 * const contribution = await addContribution(storyId, {
 *   text: "adventure",
 *   userId: currentUser.id
 * });
 * ```
 * 
 * @param storyId - The unique identifier of the story
 * @param data - The contribution data
 * @returns Promise resolving to the created contribution
 * @throws {Error} When story is not found or user lacks permissions
 */
export async function addContribution(
  storyId: string, 
  data: ContributionData
): Promise<Contribution> {
  // Implementation...
}
```

#### Component Documentation
```typescript
/**
 * A reusable button component with multiple variants and states.
 * 
 * @component
 * @example
 * ```tsx
 * <Button variant="primary" size="lg" onClick={handleClick}>
 *   Click me
 * </Button>
 * ```
 */
interface ButtonProps {
  /** The visual style variant */
  variant?: 'primary' | 'secondary' | 'destructive'
  /** The size of the button */
  size?: 'sm' | 'md' | 'lg' | 'icon'
  /** Whether the button is disabled */
  disabled?: boolean
  /** Click handler function */
  onClick?: (event: MouseEvent) => void
  /** Button content */
  children: React.ReactNode
}
```

#### API Endpoint Documentation
```typescript
/**
 * @route POST /api/stories/:id/contributions
 * @description Add a new contribution to a story
 * @param {string} id - Story ID
 * @body {ContributionData} data - Contribution details
 * @returns {Contribution} The created contribution
 * @throws {401} Unauthorized - User not authenticated
 * @throws {403} Forbidden - User cannot contribute to this story
 * @throws {404} Not Found - Story does not exist
 * @throws {422} Validation Error - Invalid contribution data
 * 
 * @example
 * ```typescript
 * const response = await fetch('/api/stories/123/contributions', {
 *   method: 'POST',
 *   headers: {
 *     'Content-Type': 'application/json',
 *     'Authorization': `Bearer ${token}`
 *   },
 *   body: JSON.stringify({
 *     text: 'adventure',
 *     special_type: null
 *   })
 * });
 * ```
 */
```

### Storybook Stories

#### Story Structure
```typescript
import type { Meta, StoryObj } from '@storybook/react';
import { Component } from './Component';

const meta: Meta<typeof Component> = {
  title: 'Category/Component',
  component: Component,
  parameters: {
    docs: {
      description: {
        component: 'Brief description of the component and its purpose.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    prop: {
      control: 'select',
      options: ['option1', 'option2'],
      description: 'Description of the prop',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic examples
export const Default: Story = {
  args: {
    prop: 'value',
  },
};

// Interactive examples
export const Interactive: Story = {
  render: () => {
    // Custom render function for complex examples
    return <Component />;
  },
  parameters: {
    docs: {
      description: {
        story: 'Description of this specific story variant.',
      },
    },
  },
};
```

## 🔧 Maintenance

### Updating Documentation

#### When to Update API Documentation
- New endpoints added
- Request/response formats change
- Authentication requirements change
- Error codes or messages change
- Rate limiting rules change

#### When to Update Component Guide
- New components created
- Component APIs change
- New patterns established
- Best practices updated

#### When to Update Generated Docs
- TypeScript interfaces change
- Function signatures change
- New modules added
- Documentation comments updated

### Automated Updates

#### TypeDoc Generation
```bash
# Regenerate after code changes
npm run docs:generate
```

#### Storybook Updates
```bash
# Stories auto-update when files change
npm run storybook
```

### Documentation Reviews

#### Before Merging PRs
- [ ] API changes documented
- [ ] New components have stories
- [ ] TypeScript comments added
- [ ] Examples are working
- [ ] Breaking changes noted

#### Quarterly Reviews
- [ ] Remove outdated examples
- [ ] Update external links
- [ ] Review and update best practices
- [ ] Check for missing documentation
- [ ] Validate all examples still work

## 🎯 Documentation Goals

### Primary Goals
1. **Developer Onboarding** - New developers can start contributing quickly
2. **API Clarity** - Clear, unambiguous API documentation
3. **Component Reusability** - Well-documented, reusable components
4. **Maintenance Efficiency** - Easy to maintain and update

### Quality Metrics
- **Coverage**: All public APIs documented
- **Accuracy**: Examples work and are up-to-date
- **Usability**: Easy to find and understand information
- **Consistency**: Uniform style and structure

## 📞 Support

### For Documentation Issues
- Create an issue in the GitHub repository
- Label with "documentation" tag
- Include specific pages or sections affected

### For API Questions
- Check the [API Documentation](./API_DOCUMENTATION.md) first
- Review example code in the repository
- Check Supabase documentation for database queries

### For Component Usage
- Start with [Storybook](http://localhost:6006) for interactive examples
- Reference the [Component Guide](./COMPONENT_GUIDE.md)
- Look at existing usage in the codebase

---

**Last Updated:** $(date)  
**Documentation Version:** 1.0  
**Application Version:** 0.0.0