import React from "react";
import { <PERSON> } from "react-router-dom";
import { Book } from "lucide-react";

const Logo = () => {
  return (
    <div className="flex items-center gap-2 group">
      <Link
        to="/"
        className="flex items-center gap-2 hover:opacity-90 transition-all"
      >
        <Book
          size={24}
          className="text-literary-gold group-hover:rotate-6 transition-transform"
        />
        <h1 className="text-xl md:text-2xl font-serif font-bold text-literary-gold/90 group-hover:text-literary-gold transition-colors">
          Word by Word Story
        </h1>
      </Link>
    </div>
  );
};

export default Logo;
