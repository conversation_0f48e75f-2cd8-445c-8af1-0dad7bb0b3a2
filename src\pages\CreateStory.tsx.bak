import React, { useState, useEffect } from "react";
import { useN<PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/auth";
import { useStory } from "@/contexts/StoryContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Loader2,
  AlertCircle,
  BookText,
  PenLine,
  Book,
  UserPlus,
  Sparkles,
} from "lucide-react";
import {
  useSubscription,
  CREDIT_COSTS,
  SUBSCRIPTION_TIERS,
} from "@/hooks/use-subscription";
import {
  generateAiTitles,
  generateAiDescriptions,
} from "@/services/aiServices";
import { useMutation } from "@tanstack/react-query";
import type { Story, StoryContributionMode } from "@/contexts/StoryContext";
import { useCreditOperation } from "@/hooks/use-credit-operation";

const getParticipantLimit = (): number => {
  const user = JSON.parse(localStorage.getItem("oneWordStoryUser") || "{}");

  switch (user?.tier) {
    case "authors-guild":
      return Infinity; // Unlimited
    case "storyteller":
      return 10;
    case "wordsmith":
      return 5;
    default:
      return 3; // Free tier
  }
};

const CreateStoryPage: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { isAuthenticated, user } = useAuth();
  const { credits, userTier } = useSubscription();
  const { handleCreditOperation, isUsingCredits } = useCreditOperation();
  const { createStory, addWord } = useStory();
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [contributionMode, setContributionMode] =
    useState<StoryContributionMode>("word");
  const [wordCount, setWordCount] = useState(3);
  const [firstContribution, setFirstContribution] = useState("");
  const [error, setError] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const [inviteStep, setInviteStep] = useState(false);
  const [inviteEmails, setInviteEmails] = useState("");
  const [gotchaWord, setGotchaWord] = useState("");
  const [gotchaDifficulty, setGotchaDifficulty] = useState("easy");
  const [isGeneratingGotcha, setIsGeneratingGotcha] = useState(false);

  const { getMaxActiveStories, getMaxCollaborators } = useSubscription();

  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/login");
    }
  }, [isAuthenticated, navigate]);

  const getStoryLimit = () => {
    return getMaxActiveStories();
  };

  const validateContribution = (contribution: string): boolean => {
    let result = false;

    switch (contributionMode) {
      case "word": {
        result = /^[a-zA-Z]+[',.!?;:]*$/.test(contribution);
        break;
      }
      case "words": {
        const words = contribution.trim().split(/\s+/);
        result = words.length === wordCount && !/^\s*$/.test(contribution);
        break;
      }
      case "sentence": {
        result =
          contribution.trim().length > 0 && /[.!?]$/.test(contribution.trim());
        break;
      }
      case "paragraph": {
        result = contribution.trim().split(/\s+/).length >= 3;
        break;
      }
    }
    return result;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (title.trim() === "") {
      setError("Please enter a story title");
      return;
    }

    if (firstContribution.trim() === "") {
      setError(
        `Please enter the first ${contributionMode === "word" ? "word" : contributionMode === "words" ? `${wordCount} words` : contributionMode} to start your story`,
      );
      return;
    }

    if (!validateContribution(firstContribution)) {
      setError(
        contributionMode === "word"
          ? "First word can only contain letters and basic punctuation (no spaces or numbers)"
          : contributionMode === "words"
            ? `Please enter exactly ${wordCount} words`
            : contributionMode === "sentence"
              ? "Please enter a valid sentence ending with proper punctuation"
              : "Please enter a paragraph of at least a few words",
      );
      return;
    }

    if (requiresRegistration(contributionMode) && !isAuthenticated) {
      setError(
        "You must be signed in to create a story with this contribution mode.",
      );
      return;
    }

    setIsCreating(true);

    try {
      const storyData: Partial<Story> = {
        title: title.trim(),
        description: description.trim(),
        contributionMode,
        wordsPerContribution: wordCount,
        status: "active",
        // Include gotcha word if provided
        gotchaWord: gotchaWord.trim() || undefined,
      };

      const newStory = await createStory(storyData);
      if (newStory?.id) {
        await addWord(newStory.id, firstContribution.trim());
      }
      setInviteStep(true);
    } catch (err) {
      console.error("Error creating story:", err);
      setError("Failed to create story. Please try again.");
    } finally {
      setIsCreating(false);
    }
  };

  const handleInviteAndFinish = async () => {
    if (!inviteStep) return;

    setIsCreating(true);

    try {
      navigate("/dashboard");
    } catch (err) {
      console.error("Error sending invitations:", err);
      setError("Created story but failed to send some invitations.");
    } finally {
      setIsCreating(false);
    }
  };

  // Get the credit cost based on the contribution mode
  const getModeCost = () => {
    switch (contributionMode) {
      case "word":
        return 1; // Single word costs 1 credit
      case "words":
        return wordCount <= 3 ? 1 : 3; // 1-3 words cost 1 credit, more words cost 3
      case "sentence":
        return 5; // Sentence costs 5 credits
      case "paragraph":
        return 10; // Paragraph costs 10 credits
      default:
        return 1;
    }
  };

  // Get the name of the content type based on the mode
  const getContentTypeName = () => {
    switch (contributionMode) {
      case "word":
        return "Word";
      case "words":
        return `${wordCount} Words`;
      case "sentence":
        return "Sentence";
      case "paragraph":
        return "Paragraph";
      default:
        return "Contribution";
    }
  };
  
  const getContributionLabel = () => {
    switch (contributionMode) {
      case "word":
        return "First Word";
      case "words":
        return `First ${wordCount} Words`;
      case "sentence":
        return "First Sentence";
      case "paragraph":
        return "First Paragraph";
      default:
        return "First Contribution";
    }
  };

  const getContributionPlaceholder = () => {
    switch (contributionMode) {
      case "word":
        return "Enter the first word to begin your story";
      case "words":
        return `Enter the first ${wordCount} words to begin your story`;
      case "sentence":
        return "Enter the first sentence to begin your story";
      case "paragraph":
        return "Enter the first paragraph to begin your story";
      default:
        return "Enter your contribution to begin the story";
    }
  };

  const requiresRegistration = (mode?: StoryContributionMode) => {
    return mode !== "word";
  };

  const { mutate: generateTitle, isPending: isGeneratingTitle } = useMutation({
    mutationFn: async () => {
      if (!user) {
        throw new Error("Must be logged in to generate title");
      }

      const success = await handleCreditOperation("titleGeneration");
      if (!success) {
        throw new Error("Failed to use credits for title generation");
      }

      // Use the current title input as seed for the AI title generation
      const response = await generateAiTitles(title);
      if (!response || !response.titles || response.titles.length === 0) {
        throw new Error("No titles generated. Please try again.");
      }

      return response.titles;
    },
    onSuccess: (titles) => {
      const randomIndex = Math.floor(Math.random() * titles.length);
      const selectedTitle = titles[randomIndex];
      const otherTitles = titles.filter((_, index) => index !== randomIndex);

      setTitle(selectedTitle);

      toast({
        title: "AI Title Generated",
        description: (
          <div className="space-y-1">
            <p>
              Used {CREDIT_COSTS.titleGeneration} credits. Other suggestions:
            </p>
            <ul className="text-xs space-y-1 mt-1 list-disc pl-4">
              {otherTitles.map((title, i) => (
                <li
                  key={i}
                  className="cursor-pointer hover:text-literary-burgundy"
                  onClick={() => setTitle(title)}
                >
                  {title}
                </li>
              ))}
            </ul>
          </div>
        ),
      });
    },
    onError: (error) => {
      console.error("Error generating AI title:", error);
      toast({
        title: "Generation Failed",
        description:
          error.message || "Failed to generate AI title. Please try again.",
        variant: "destructive",
      });
    },
  });

  const { mutate: generateDescription, isPending: isGeneratingDescription } =
    useMutation({
      mutationFn: async () => {
        if (!user || !title) {
          throw new Error(
            "Must be logged in and have a title to generate description",
          );
        }

        const success = await handleCreditOperation("descriptionGeneration");
        if (!success) {
          throw new Error("Failed to use credits for description generation");
        }

        // Use the current description if available as a seed for the AI
        const response = await generateAiDescriptions(title, description);
        return response.descriptions;
      },
      onSuccess: (descriptions) => {
        // Show all descriptions in a toast so user can see alternatives
        const randomIndex = Math.floor(Math.random() * descriptions.length);
        const selectedDescription = descriptions[randomIndex];
        const otherDescriptions = descriptions.filter(
          (_, index) => index !== randomIndex,
        );

        setDescription(selectedDescription);

        toast({
          title: "AI Description Generated",
          description: (
            <div className="space-y-1">
              <p>
                Used {CREDIT_COSTS.descriptionGeneration} credits. Other
                options:
              </p>
              <ul className="text-xs space-y-2 mt-1 list-disc pl-4">
                {otherDescriptions.map((desc, i) => (
                  <li
                    key={i}
                    className="cursor-pointer hover:text-literary-burgundy"
                    onClick={() => setDescription(desc)}
                  >
                    {desc.length > 100 ? `${desc.substring(0, 100)}...` : desc}
                  </li>
                ))}
              </ul>
            </div>
          ),
        });
      },
      onError: (error: Error) => {
        console.error("Error generating AI description:", error);
        toast({
          title: "Generation Failed",
          description:
            error.message ||
            "Failed to generate AI description. Please try again.",
          variant: "destructive",
        });
      },
    });

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-8">
        <h1 className="text-3xl font-serif">Create a New Story</h1>
        <p className="text-gray-600 mt-1">
          Start your collaborative writing journey
        </p>
      </div>

      <Card className="border-literary-gold/20 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookText className="text-literary-burgundy" size={20} />
            {inviteStep ? "Invite Collaborators" : "New Story Details"}
          </CardTitle>
          <CardDescription>
            {inviteStep
              ? "Invite people to contribute to your story (optional)"
              : "Set up your new collaborative story"}
          </CardDescription>
        </CardHeader>

        <CardContent>
          {error && (
            <div className="bg-red-50 text-red-800 p-3 rounded-md flex items-start mb-4 text-sm">
              <AlertCircle size={16} className="mr-2 mt-0.5 flex-shrink-0" />
              <span>{error}</span>
            </div>
          )}

          {!inviteStep ? (
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="title">Story Title</Label>
                  <div className="flex items-center">
                    {user && (
                      <Button
                        type="button"
                        size="sm"
                        className="h-8 text-sm flex items-center gap-1 bg-literary-gold hover:bg-literary-gold/90 text-white shadow-sm"
                        onClick={(e) => {
                          e.preventDefault();
                          generateTitle();
                        }}
                        disabled={
                          isGeneratingTitle ||
                          !user ||
                          credits < CREDIT_COSTS.titleGeneration ||
                          isUsingCredits
                        }
                        title={
                          credits < CREDIT_COSTS.titleGeneration
                            ? `Requires ${CREDIT_COSTS.titleGeneration} credits`
                            : title.trim()
                              ? "Generate a better title based on your input"
                              : "Generate a title using AI"
                        }
                      >
                        {isGeneratingTitle || isUsingCredits ? (
                          <Loader2 size={14} className="mr-1 animate-spin" />
                        ) : (
                          <Sparkles size={14} className="mr-1" />
                        )}
                        Magic Title
                      </Button>
                    )}
                  </div>
                </div>
                <Input
                  id="title"
                  placeholder="Enter a captivating title for your story"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className="input-primary"
                  maxLength={100}
                  required
                />
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="description">Description (optional)</Label>
                  <div className="flex items-center">
                    {user && (
                      <Button
                        type="button"
                        size="sm"
                        className="h-8 text-sm flex items-center gap-1 bg-literary-gold hover:bg-literary-gold/90 text-white shadow-sm"
                        onClick={(e) => {
                          e.preventDefault();
                          generateDescription();
                        }}
                        disabled={
                          isGeneratingDescription ||
                          !user ||
                          !title ||
                          credits < CREDIT_COSTS.descriptionGeneration ||
                          isUsingCredits
                        }
                        title={
                          !title
                            ? "Enter a title first"
                            : credits < CREDIT_COSTS.descriptionGeneration
                              ? `Requires ${CREDIT_COSTS.descriptionGeneration} credits`
                              : description.trim()
                                ? "Generate a better description based on your input"
                                : "Generate a description based on the title"
                        }
                      >
                        {isGeneratingDescription || isUsingCredits ? (
                          <Loader2 size={14} className="mr-1 animate-spin" />
                        ) : (
                          <Sparkles size={14} className="mr-1" />
                        )}
                        Magic Story
                      </Button>
                    )}
                  </div>
                </div>
                <Textarea
                  id="description"
                  placeholder="Provide a brief description of your story"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="input-primary min-h-[100px]"
                  maxLength={500}
                />
                <p className="text-xs text-gray-500">
                  Add context to your story to help guide contributors (max 500
                  characters)
                </p>
              </div>

              {user && (
                <div
                  className={`p-3 rounded-md flex items-center justify-between ${credits < Math.min(CREDIT_COSTS.titleGeneration, CREDIT_COSTS.descriptionGeneration) ? "bg-red-50" : "bg-amber-50"}`}
                >
                  <div>
                    <h4
                      className={`font-medium text-sm ${credits < Math.min(CREDIT_COSTS.titleGeneration, CREDIT_COSTS.descriptionGeneration) ? "text-red-800" : "text-amber-800"}`}
                    >
                      AI Credits
                    </h4>
                    <p
                      className={`text-xs mt-1 ${credits < Math.min(CREDIT_COSTS.titleGeneration, CREDIT_COSTS.descriptionGeneration) ? "text-red-700" : "text-amber-700"}`}
                    >
                      Available credits:{" "}
                      <span className="font-semibold">{credits}</span>
                      {credits <
                        Math.min(
                          CREDIT_COSTS.titleGeneration,
                          CREDIT_COSTS.descriptionGeneration,
                        ) && (
                        <span className="ml-1">
                          - Not enough for AI features!
                        </span>
                      )}
                    </p>
                  </div>
                  <Button
                    type="button"
                    size="sm"
                    onClick={() => navigate("/subscription")}
                    className={`text-white ${credits < Math.min(CREDIT_COSTS.titleGeneration, CREDIT_COSTS.descriptionGeneration) ? "bg-red-600 hover:bg-red-700" : "bg-literary-gold hover:bg-literary-gold/90"}`}
                  >
                    <Sparkles className="h-3 w-3 mr-1" />
                    Get More Credits
                  </Button>
                </div>
              )}
              {/* Add description of what the AI can do */}
              {user && (
                <div className="p-3 rounded-md bg-blue-50 text-blue-800 text-xs">
                  <h4 className="font-medium mb-1">AI Features</h4>
                  <ul className="space-y-1">
                    <li className="flex items-center">
                      <Sparkles className="h-3 w-3 mr-1 text-literary-gold" />
                      <span>
                        Generate creative titles for your story (
                        {CREDIT_COSTS.titleGeneration} credits)
                      </span>
                    </li>
                    <li className="flex items-center">
                      <Sparkles className="h-3 w-3 mr-1 text-literary-gold" />
                      <span>
                        Create descriptions based on your title (
                        {CREDIT_COSTS.descriptionGeneration} credits)
                      </span>
                    </li>
                  </ul>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="contribution-mode">Contribution Mode</Label>
                <Select
                  value={contributionMode}
                  onValueChange={(value) => {
                    setContributionMode(value as StoryContributionMode);
                    if (
                      requiresRegistration(value as StoryContributionMode) &&
                      !isAuthenticated
                    ) {
                      toast({
                        title: "Registration Required",
                        description:
                          "This contribution mode requires an account. Please sign up.",
                        variant: "destructive",
                      });
                    }
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select contribution mode" />
                  </SelectTrigger>
                  <SelectContent>
                  <Select
                    value={wordCount.toString()}
                    onValueChange={(value) => setWordCount(parseInt(value))}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select word count" />
                    </SelectTrigger>
                    <SelectContent>
                      {[2, 3, 5, 10].map((count) => (
                        <SelectItem key={count} value={count.toString()}>
                          {count} words
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="first-contribution">
                  {getContributionLabel()}
                </Label>
                <div className="relative">
                  {contributionMode === "word" ||
                  contributionMode === "words" ? (
                    <Input
                      id="first-contribution"
                      placeholder={getContributionPlaceholder()}
                      value={firstContribution}
                      onChange={(e) => setFirstContribution(e.target.value)}
                      className={`input-primary ${!validateContribution(firstContribution) && firstContribution ? "border-red-500" : ""}`}
                      maxLength={contributionMode === "word" ? 30 : 100}
                      required
                    />
                  ) : (
                    <Textarea
                      id="first-contribution"
                      placeholder={getContributionPlaceholder()}
                      value={firstContribution}
                      onChange={(e) => setFirstContribution(e.target.value)}
                      className={`input-primary min-h-[${contributionMode === "sentence" ? "80" : "120"}px] ${!validateContribution(firstContribution) && firstContribution ? "border-red-500" : ""}`}
                      maxLength={contributionMode === "sentence" ? 200 : 1000}
                      required
                    />
                  )}
                  <div className="absolute right-3 top-2">
                    <PenLine size={16} className="text-gray-400" />
                  </div>
                </div>
                <p className="text-xs text-gray-500">
                  {contributionMode === "word" &&
                    "The first word can only contain letters and basic punctuation. No spaces allowed."}
                  {contributionMode === "words" &&
                    `Please enter exactly ${wordCount} words to start the story.`}
                  {contributionMode === "sentence" &&
                    "Enter a complete sentence ending with proper punctuation."}
                  {contributionMode === "paragraph" &&
                    "Enter a paragraph of at least a few words to start the story."}
                </p>
              </div>

              <div className="bg-blue-50 p-3 rounded-md">
                <h4 className="font-medium text-blue-800 text-sm">
                  How it works
                </h4>
                <ul className="text-xs text-blue-700 mt-1 space-y-1">
                  <li>• You start by writing the first {contributionMode}</li>
                  <li>
                    • Invite others to join and add{" "}
                    {contributionMode === "word"
                      ? "one word"
                      : contributionMode === "words"
                        ? `${wordCount} words`
                        : "a " + contributionMode}{" "}
                    at a time
                  </li>
                  <li>• Each participant takes turns to build the story</li>
                  <li>• When ready, you can complete and publish your story</li>
                </ul>
              </div>
            </form>
          ) : (
            <div className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label htmlFor="inviteEmails">Invite Collaborators</Label>
                  <Textarea
                    id="inviteEmails"
                    placeholder="Enter email addresses separated by commas"
                    value={inviteEmails}
                    onChange={(e) => setInviteEmails(e.target.value)}
                    className="input-primary min-h-[100px] mt-2"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Enter email addresses of people you'd like to invite (comma
                    separated)
                  </p>
                </div>
                
                <div className="bg-blue-50 p-4 rounded-md">
                  <h3 className="text-blue-800 font-medium mb-2 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Story Info
                  </h3>
                  <div className="space-y-2 text-sm text-blue-700">
                    <div className="flex justify-between">
                      <span>Mode:</span>
                      <span className="font-medium">{contributionMode === "word" ? "Word" : contributionMode === "words" ? "Multi-Word" : contributionMode === "sentence" ? "Sentence" : "Paragraph"}</span>
                    </div>
                    {contributionMode === "words" && (
                      <div className="flex justify-between">
                        <span>Words per turn:</span>
                        <span className="font-medium">{wordCount}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span>Creator:</span>
                      <span className="font-medium">{user?.email?.split('@')[0] || 'You'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Participants:</span>
                      <span className="font-medium">1 (You) {inviteEmails ? `+ ${inviteEmails.split(',').filter(email => email.trim()).length} invited` : ''}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label htmlFor="gotchaWord">AI Gotcha Word (Optional)</Label>
                    <div className="flex items-center gap-2">
                      <Select
                        value={gotchaDifficulty}
                        onValueChange={setGotchaDifficulty}
                      >
                        <SelectTrigger className="w-[100px] h-8">
                          <SelectValue placeholder="Difficulty" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="easy">Easy</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="hard">Hard</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button
                        type="button"
                        size="sm"
                        className="h-8 text-sm flex items-center gap-1 bg-yellow-500 hover:bg-yellow-600 text-white shadow-sm"
                        onClick={(e) => {
                          e.preventDefault();
                          setIsGeneratingGotcha(true);
                          // Simulate generating a gotcha word
                          setTimeout(() => {
                            const words = {
                              easy: ['curious', 'whisper', 'gentle', 'sparkle', 'journey'],
                              medium: ['whimsical', 'eloquent', 'serendipity', 'mellifluous', 'ephemeral'],
                              hard: ['onomatopoeia', 'sesquipedalian', 'perspicacious', 'verisimilitude', 'supercilious']
                            };
                            const randomWord = words[gotchaDifficulty][Math.floor(Math.random() * 5)];
                            setGotchaWord(randomWord);
                            setIsGeneratingGotcha(false);
                          }, 1000);
                        }}
                        disabled={isGeneratingGotcha}
                      >
                        {isGeneratingGotcha ? (
                          <Loader2 size={14} className="mr-1 animate-spin" />
                        ) : (
                          <Sparkles size={14} className="mr-1" />
                        )}
                        Generate
                      </Button>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Input
                      id="gotchaWord"
                      placeholder="Enter a challenging word or generate one"
                      value={gotchaWord}
                      onChange={(e) => setGotchaWord(e.target.value)}
                      className="input-primary"
                    />
                    {gotchaWord && (
                      <Button
                        type="button"
                        size="icon"
                        variant="ghost"
                        onClick={() => setGotchaWord('')}
                        className="h-10 w-10"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </Button>
                    )}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Add a challenging word that must be used in the next contribution
                  </p>
                </div>
              </div>

              <div className="bg-amber-50 p-3 rounded-md">
                <h4 className="font-medium text-amber-800 text-sm">
                  Participant Limits
                </h4>
                <p className="text-xs text-amber-700 mt-1">
                  {userTier &&
                  SUBSCRIPTION_TIERS[
                    userTier as keyof typeof SUBSCRIPTION_TIERS
                  ]
                    ? `Your ${SUBSCRIPTION_TIERS[userTier as keyof typeof SUBSCRIPTION_TIERS].name} plan allows up to ${getMaxCollaborators()} participants per story.`
                    : "Your free tier allows up to 3 participants per story."}
                </p>
              </div>
            </div>
          )}
        </CardContent>

        <CardFooter className="flex justify-between">
          {!inviteStep ? (
            <>
              <Button
                variant="outline"
                onClick={() => navigate("/dashboard")}
                disabled={isCreating}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                onClick={handleSubmit}
                disabled={isCreating || error.includes("limit")}
                className="bg-literary-burgundy hover:bg-opacity-90"
              >
                {isCreating ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Book className="h-4 w-4 mr-2" />
                )}
                Create Story
              </Button>
            </>
          ) : (
            <>
              <Button
                variant="outline"
                onClick={() => handleInviteAndFinish()}
                disabled={isCreating}
              >
                Skip
              </Button>
              <Button
                onClick={() => handleInviteAndFinish()}
                disabled={isCreating}
                className="bg-literary-burgundy hover:bg-opacity-90"
              >
                {isCreating ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <UserPlus className="h-4 w-4 mr-2" />
                )}
                Send Invites & Continue
              </Button>
            </>
          )}
        </CardFooter>
      </Card>
    </div>
  );
};

export default CreateStoryPage;
