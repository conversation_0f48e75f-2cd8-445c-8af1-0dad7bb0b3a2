import React from 'react';
import { render, screen } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import GoogleAd from '@/components/ads/GoogleAd';
import { useAds } from '@/contexts/AdsContext';

// Mock the useAds hook
vi.mock('@/contexts/AdsContext', () => ({
  useAds: vi.fn(),
}));

// Mock environment variables
vi.mock('import.meta', () => ({
  env: {
    VITE_ADSENSE_CLIENT_ID: 'ca-pub-test-client-id',
  },
}));

// Mock window.adsbygoogle
const mockAdsByGoogle = {
  push: vi.fn(),
};

describe('GoogleAd Component', () => {
  const mockUseAds = useAds as ReturnType<typeof vi.fn>;

  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();
    
    // Mock window.adsbygoogle
    Object.defineProperty(window, 'adsbygoogle', {
      value: mockAdsByGoogle,
      writable: true,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Ad Rendering', () => {
    it('should render ad when shouldShowAd returns true', () => {
      mockUseAds.mockReturnValue({
        shouldShowAd: vi.fn().mockReturnValue(true),
      });

      render(
        <GoogleAd
          placement="sidebar"
          slot="1234567890"
          format="auto"
        />
      );

      const adElement = screen.getByTestId('google-ad');
      expect(adElement).toBeInTheDocument();
      expect(adElement).toHaveClass('adsbygoogle');
    });

    it('should not render ad when shouldShowAd returns false', () => {
      mockUseAds.mockReturnValue({
        shouldShowAd: vi.fn().mockReturnValue(false),
      });

      const { container } = render(
        <GoogleAd
          placement="sidebar"
          slot="1234567890"
          format="auto"
        />
      );

      expect(container.firstChild).toBeNull();
    });

    it('should render ad with correct data attributes', () => {
      mockUseAds.mockReturnValue({
        shouldShowAd: vi.fn().mockReturnValue(true),
      });

      render(
        <GoogleAd
          placement="header"
          slot="9876543210"
          format="rectangle"
        />
      );

      const adElement = screen.getByTestId('google-ad');
      expect(adElement).toHaveAttribute('data-ad-client', 'ca-pub-test-client-id');
      expect(adElement).toHaveAttribute('data-ad-slot', '9876543210');
      expect(adElement).toHaveAttribute('data-ad-format', 'rectangle');
      expect(adElement).toHaveAttribute('data-full-width-responsive', 'true');
    });

    it('should use default format when not specified', () => {
      mockUseAds.mockReturnValue({
        shouldShowAd: vi.fn().mockReturnValue(true),
      });

      render(
        <GoogleAd
          placement="content"
          slot="1111111111"
        />
      );

      const adElement = screen.getByTestId('google-ad');
      expect(adElement).toHaveAttribute('data-ad-format', 'auto');
    });

    it('should apply custom className', () => {
      mockUseAds.mockReturnValue({
        shouldShowAd: vi.fn().mockReturnValue(true),
      });

      render(
        <GoogleAd
          placement="footer"
          slot="2222222222"
          className="custom-ad-class"
        />
      );

      const adElement = screen.getByTestId('google-ad');
      expect(adElement).toHaveClass('adsbygoogle', 'custom-ad-class');
    });

    it('should apply custom styles', () => {
      mockUseAds.mockReturnValue({
        shouldShowAd: vi.fn().mockReturnValue(true),
      });

      const customStyle = {
        display: 'inline-block',
        width: '300px',
        height: '250px',
      };

      render(
        <GoogleAd
          placement="story-end"
          slot="3333333333"
          style={customStyle}
        />
      );

      const adElement = screen.getByTestId('google-ad');
      expect(adElement).toHaveStyle('display: inline-block');
      expect(adElement).toHaveStyle('width: 300px');
      expect(adElement).toHaveStyle('height: 250px');
    });
  });

  describe('Placement Logic', () => {
    it('should call shouldShowAd with correct placement for sidebar', () => {
      const mockShouldShowAd = vi.fn().mockReturnValue(true);
      mockUseAds.mockReturnValue({
        shouldShowAd: mockShouldShowAd,
      });

      render(
        <GoogleAd
          placement="sidebar"
          slot="4444444444"
        />
      );

      expect(mockShouldShowAd).toHaveBeenCalledWith('sidebar');
    });

    it('should call shouldShowAd with correct placement for header', () => {
      const mockShouldShowAd = vi.fn().mockReturnValue(true);
      mockUseAds.mockReturnValue({
        shouldShowAd: mockShouldShowAd,
      });

      render(
        <GoogleAd
          placement="header"
          slot="5555555555"
        />
      );

      expect(mockShouldShowAd).toHaveBeenCalledWith('header');
    });

    it('should call shouldShowAd with correct placement for footer', () => {
      const mockShouldShowAd = vi.fn().mockReturnValue(true);
      mockUseAds.mockReturnValue({
        shouldShowAd: mockShouldShowAd,
      });

      render(
        <GoogleAd
          placement="footer"
          slot="6666666666"
        />
      );

      expect(mockShouldShowAd).toHaveBeenCalledWith('footer');
    });

    it('should call shouldShowAd with correct placement for content', () => {
      const mockShouldShowAd = vi.fn().mockReturnValue(true);
      mockUseAds.mockReturnValue({
        shouldShowAd: mockShouldShowAd,
      });

      render(
        <GoogleAd
          placement="content"
          slot="7777777777"
        />
      );

      expect(mockShouldShowAd).toHaveBeenCalledWith('content');
    });

    it('should call shouldShowAd with correct placement for story-end', () => {
      const mockShouldShowAd = vi.fn().mockReturnValue(true);
      mockUseAds.mockReturnValue({
        shouldShowAd: mockShouldShowAd,
      });

      render(
        <GoogleAd
          placement="story-end"
          slot="8888888888"
        />
      );

      expect(mockShouldShowAd).toHaveBeenCalledWith('story-end');
    });
  });

  describe('AdSense Integration', () => {
    it('should push to adsbygoogle when ad should be shown', () => {
      mockUseAds.mockReturnValue({
        shouldShowAd: vi.fn().mockReturnValue(true),
      });

      render(
        <GoogleAd
          placement="sidebar"
          slot="9999999999"
        />
      );

      expect(mockAdsByGoogle.push).toHaveBeenCalledWith({});
    });

    it('should not push to adsbygoogle when ad should not be shown', () => {
      mockUseAds.mockReturnValue({
        shouldShowAd: vi.fn().mockReturnValue(false),
      });

      render(
        <GoogleAd
          placement="sidebar"
          slot="0000000000"
        />
      );

      expect(mockAdsByGoogle.push).not.toHaveBeenCalled();
    });

    it('should handle missing adsbygoogle gracefully', () => {
      mockUseAds.mockReturnValue({
        shouldShowAd: vi.fn().mockReturnValue(true),
      });

      // Remove adsbygoogle from window
      Object.defineProperty(window, 'adsbygoogle', {
        value: undefined,
        writable: true,
      });

      expect(() => {
        render(
          <GoogleAd
            placement="header"
            slot="1111111111"
          />
        );
      }).not.toThrow();
    });

    it('should re-initialize ad when placement changes', () => {
      const mockShouldShowAd = vi.fn().mockReturnValue(true);
      mockUseAds.mockReturnValue({
        shouldShowAd: mockShouldShowAd,
      });

      const { rerender } = render(
        <GoogleAd
          placement="sidebar"
          slot="2222222222"
        />
      );

      expect(mockAdsByGoogle.push).toHaveBeenCalledTimes(1);

      // Change placement
      rerender(
        <GoogleAd
          placement="header"
          slot="2222222222"
        />
      );

      expect(mockAdsByGoogle.push).toHaveBeenCalledTimes(2);
    });
  });

  describe('Component Props', () => {
    it('should handle all placement types', () => {
      const placements: Array<'sidebar' | 'header' | 'footer' | 'content' | 'story-end'> = [
        'sidebar',
        'header',
        'footer',
        'content',
        'story-end',
      ];

      mockUseAds.mockReturnValue({
        shouldShowAd: vi.fn().mockReturnValue(true),
      });

      placements.forEach((placement) => {
        const { unmount } = render(
          <GoogleAd
            placement={placement}
            slot="1234567890"
          />
        );

        const adElement = screen.getByTestId('google-ad');
        expect(adElement).toBeInTheDocument();
        
        unmount();
      });
    });

    it('should handle different ad formats', () => {
      const formats = ['auto', 'rectangle', 'horizontal', 'vertical'];

      mockUseAds.mockReturnValue({
        shouldShowAd: vi.fn().mockReturnValue(true),
      });

      formats.forEach((format) => {
        const { unmount } = render(
          <GoogleAd
            placement="content"
            slot="1234567890"
            format={format}
          />
        );

        const adElement = screen.getByTestId('google-ad');
        expect(adElement).toHaveAttribute('data-ad-format', format);
        
        unmount();
      });
    });

    it('should handle missing environment variable gracefully', () => {
      // Mock missing environment variable by re-mocking the module
      vi.doMock('import.meta', () => ({
        env: {
          VITE_ADSENSE_CLIENT_ID: undefined,
        },
      }));

      mockUseAds.mockReturnValue({
        shouldShowAd: vi.fn().mockReturnValue(true),
      });

      render(
        <GoogleAd
          placement="sidebar"
          slot="1234567890"
        />
      );

      const adElement = screen.getByTestId('google-ad');
      expect(adElement).toHaveAttribute('data-ad-client', 'undefined');
    });
  });

  describe('Edge Cases', () => {
    it('should handle rapid re-renders without errors', () => {
      mockUseAds.mockReturnValue({
        shouldShowAd: vi.fn().mockReturnValue(true),
      });

      const { rerender } = render(
        <GoogleAd
          placement="sidebar"
          slot="1234567890"
        />
      );

      // Rapidly re-render multiple times
      for (let i = 0; i < 10; i++) {
        rerender(
          <GoogleAd
            placement="sidebar"
            slot={`123456789${i}`}
          />
        );
      }

      expect(mockAdsByGoogle.push).toHaveBeenCalledTimes(10);
    });

    it('should handle shouldShowAd function changes', () => {
      let shouldShow = true;
      const mockShouldShowAd = vi.fn(() => shouldShow);
      
      mockUseAds.mockReturnValue({
        shouldShowAd: mockShouldShowAd,
      });

      const { rerender } = render(
        <GoogleAd
          placement="sidebar"
          slot="1234567890"
        />
      );

      expect(screen.getByTestId('google-ad')).toBeInTheDocument();

      // Change shouldShowAd to return false
      shouldShow = false;
      mockShouldShowAd.mockReturnValue(false);

      rerender(
        <GoogleAd
          placement="sidebar"
          slot="1234567890"
        />
      );

      expect(screen.queryByTestId('google-ad')).not.toBeInTheDocument();
    });

    it('should maintain correct ins element structure', () => {
      mockUseAds.mockReturnValue({
        shouldShowAd: vi.fn().mockReturnValue(true),
      });

      render(
        <GoogleAd
          placement="content"
          slot="1234567890"
          format="rectangle"
          className="test-class"
          style={{ margin: '10px' }}
        />
      );

      const adElement = screen.getByTestId('google-ad');
      
      // Check that it's an ins element
      expect(adElement.tagName).toBe('INS');
      
      // Check all required attributes are present
      expect(adElement).toHaveClass('adsbygoogle', 'test-class');
      expect(adElement).toHaveStyle('margin: 10px');
      expect(adElement).toHaveAttribute('data-ad-client');
      expect(adElement).toHaveAttribute('data-ad-slot', '1234567890');
      expect(adElement).toHaveAttribute('data-ad-format', 'rectangle');
      expect(adElement).toHaveAttribute('data-full-width-responsive', 'true');
    });
  });

  describe('Performance', () => {
    it('should not re-initialize ad unnecessarily', () => {
      const mockShouldShowAd = vi.fn().mockReturnValue(true);
      mockUseAds.mockReturnValue({
        shouldShowAd: mockShouldShowAd,
      });

      const { rerender } = render(
        <GoogleAd
          placement="sidebar"
          slot="1234567890"
        />
      );

      expect(mockAdsByGoogle.push).toHaveBeenCalledTimes(1);

      // Re-render with same props
      rerender(
        <GoogleAd
          placement="sidebar"
          slot="1234567890"
        />
      );

      // Should not push again if placement and shouldShowAd haven't changed
      expect(mockAdsByGoogle.push).toHaveBeenCalledTimes(1);
    });
  });
}); 
