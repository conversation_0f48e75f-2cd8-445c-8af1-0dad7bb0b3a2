/**
 * Ad Blocker Detection Utility
 * Detects if user has ad blockers enabled that prevent analytics and ads
 */

export interface AdBlockDetectionResult {
  isBlocked: boolean;
  blockedServices: string[];
  detectedAt: Date;
}

/**
 * Detect if PostHog analytics is being blocked
 */
const detectPostHogBlocked = async (): Promise<boolean> => {
  try {
    // Try to make a request to PostHog
    const response = await fetch('https://us.i.posthog.com/decide/?v=3', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ token: 'test' }),
    });
    return false; // If successful, not blocked
  } catch (error) {
    return true; // If failed, likely blocked
  }
};

/**
 * Detect ad blockers using multiple methods
 */
export const detectAdBlocker = async (): Promise<AdBlockDetectionResult> => {
  const blockedServices: string[] = [];
  
  // Method 1: Check if common ad blocker elements exist
  const adBlockerIndicators = [
    '.adblockinfo',
    '#adblock-detected',
    '.ad-blocked',
  ];
  
  const hasAdBlockerElements = adBlockerIndicators.some(selector => 
    document.querySelector(selector) !== null
  );
  
  if (hasAdBlockerElements) {
    blockedServices.push('Generic Ad Blocker');
  }

  // Method 2: Try to create a fake ad element
  const testAd = document.createElement('div');
  testAd.innerHTML = '&nbsp;';
  testAd.className = 'adsbox ad-banner advertisement ads ad-placement';
  testAd.style.cssText = 'position:absolute;left:-10000px;';
  document.body.appendChild(testAd);
  
  // Wait a bit for ad blockers to process
  await new Promise(resolve => setTimeout(resolve, 100));
  
  const isAdElementHidden = testAd.offsetHeight === 0 || 
                           testAd.style.display === 'none' ||
                           testAd.style.visibility === 'hidden';
  
  document.body.removeChild(testAd);
  
  if (isAdElementHidden) {
    blockedServices.push('Display Ads');
  }

  // Method 3: Check PostHog specifically
  const isPostHogBlocked = await detectPostHogBlocked();
  if (isPostHogBlocked) {
    blockedServices.push('Analytics (PostHog)');
  }

  // Method 4: Check for common ad blocker extensions
  if (typeof window !== 'undefined') {
    // Check for uBlock Origin
    if ((window as any).uBlock || (window as any).uBlockOrigin) {
      blockedServices.push('uBlock Origin');
    }
    
    // Check for AdBlock Plus
    if ((window as any).adblockplus || (window as any).AdBlock) {
      blockedServices.push('AdBlock Plus');
    }
  }

  return {
    isBlocked: blockedServices.length > 0,
    blockedServices,
    detectedAt: new Date(),
  };
};

/**
 * Store ad blocker detection result in localStorage
 */
export const storeAdBlockDetection = (result: AdBlockDetectionResult): void => {
  try {
    localStorage.setItem('adblock_detection', JSON.stringify({
      ...result,
      detectedAt: result.detectedAt.toISOString(),
    }));
  } catch (error) {
    console.warn('Could not store ad block detection result:', error);
  }
};

/**
 * Get stored ad blocker detection result
 */
export const getStoredAdBlockDetection = (): AdBlockDetectionResult | null => {
  try {
    const stored = localStorage.getItem('adblock_detection');
    if (!stored) return null;
    
    const parsed = JSON.parse(stored);
    return {
      ...parsed,
      detectedAt: new Date(parsed.detectedAt),
    };
  } catch (error) {
    return null;
  }
};

/**
 * Check if we should show the ad blocker message
 * (respects user's choice for a certain period)
 */
export const shouldShowAdBlockMessage = (): boolean => {
  const dismissedUntil = localStorage.getItem('adblock_message_dismissed_until');
  
  if (dismissedUntil) {
    const dismissTime = new Date(dismissedUntil);
    if (new Date() < dismissTime) {
      return false; // Still within dismissed period
    }
  }
  
  return true;
};

/**
 * Dismiss ad blocker message for a certain period
 */
export const dismissAdBlockMessage = (hours: number = 24): void => {
  const dismissUntil = new Date();
  dismissUntil.setHours(dismissUntil.getHours() + hours);
  localStorage.setItem('adblock_message_dismissed_until', dismissUntil.toISOString());
}; 