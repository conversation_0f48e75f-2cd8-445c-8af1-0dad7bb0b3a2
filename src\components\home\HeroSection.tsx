import React from "react";
import { <PERSON> } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import { But<PERSON> } from "@/components/ui/button";
import { Book, Plus } from "lucide-react";

const HeroSection: React.FC = () => {
  const { isAuthenticated } = useAuth();

  return (
    <div className="text-center py-16 md:py-24">
      <div className="flex justify-center mb-6">
        <div className="relative">
          <Book size={48} className="text-literary-burgundy" />
          <span className="absolute -top-3 -right-3 text-3xl font-serif text-literary-gold">
            ...
          </span>
        </div>
      </div>
      <h1 className="text-4xl md:text-5xl lg:text-6xl font-serif font-bold mb-6">
        Word by Word Story
      </h1>
      <p className="text-xl md:text-2xl text-gray-700 max-w-2xl mx-auto mb-8">
        Collaborate with friends to craft stories, one word at a time.
      </p>
      <div className="flex flex-col md:flex-row justify-center gap-4">
        {!isAuthenticated ? (
          <>
            <Link to="/register">
              <Button className="text-lg py-6 px-8 bg-literary-burgundy hover:bg-opacity-90">
                Start Writing
              </Button>
            </Link>
            <Link to="/login">
              <Button
                variant="outline"
                className="text-lg py-6 px-8 border-literary-burgundy text-literary-burgundy hover:bg-literary-burgundy hover:text-white"
              >
                Login
              </Button>
            </Link>
          </>
        ) : (
          <>
            <Link to="/dashboard">
              <Button className="text-lg py-6 px-8 bg-literary-burgundy hover:bg-opacity-90">
                View Your Stories
              </Button>
            </Link>
            <Link to="/create-story">
              <Button
                variant="outline"
                className="text-lg py-6 px-8 border-literary-burgundy text-literary-burgundy hover:bg-literary-burgundy hover:text-white"
              >
                <Plus size={16} className="mr-2" />
                Create New Story
              </Button>
            </Link>
          </>
        )}
      </div>
    </div>
  );
};

export default HeroSection;
