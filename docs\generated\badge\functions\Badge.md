[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [badge](../README.md) / Badge

# Function: Badge()

> **Badge**(`__namedParameters`): `Element`

Defined in: [src/components/ui/badge.tsx:30](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/badge.tsx#L30)

## Parameters

### \_\_namedParameters

[`BadgeProps`](../interfaces/BadgeProps.md)

## Returns

`Element`
