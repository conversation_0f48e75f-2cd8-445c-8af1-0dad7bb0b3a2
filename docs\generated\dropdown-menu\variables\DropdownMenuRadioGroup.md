[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [dropdown-menu](../README.md) / DropdownMenuRadioGroup

# Variable: DropdownMenuRadioGroup

> `const` **DropdownMenuRadioGroup**: `ForwardRefExoticComponent`\<`DropdownMenuRadioGroupProps` & `RefAttributes`\<`HTMLDivElement`\>\> = `DropdownMenuPrimitive.RadioGroup`

Defined in: [src/components/ui/dropdown-menu.tsx:17](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/dropdown-menu.tsx#L17)
