import { BROWSERS, VIEWPORTS, DEVICE_CATEGORIES, PERFORMANCE_THRESHOLDS } from './browser-config'

// Browser detection utilities
export const getBrowserInfo = () => {
  return Cypress.browser
}

export const isMobile = () => {
  const viewport = Cypress.config('viewportWidth')
  return viewport < 768
}

export const isTablet = () => {
  const viewport = Cypress.config('viewportWidth')
  return viewport >= 768 && viewport < 1024
}

export const isDesktop = () => {
  const viewport = Cypress.config('viewportWidth')
  return viewport >= 1024
}

// Cross-browser testing helpers
export const skipOnBrowser = (browserName: string, testFn: () => void) => {
  if (Cypress.browser.name !== browserName) {
    testFn()
  }
}

export const onlyOnBrowser = (browserName: string, testFn: () => void) => {
  if (Cypress.browser.name === browserName) {
    testFn()
  }
}

export const skipOnMobile = (testFn: () => void) => {
  if (!isMobile()) {
    testFn()
  }
}

export const onlyOnMobile = (testFn: () => void) => {
  if (isMobile()) {
    testFn()
  }
}

// Viewport testing utilities
export const testOnViewports = (viewportNames: string[], testFn: (viewport: any) => void) => {
  viewportNames.forEach(viewportName => {
    const viewport = VIEWPORTS[viewportName as keyof typeof VIEWPORTS]
    if (viewport) {
      context(`${viewport.deviceName} (${viewport.width}x${viewport.height})`, () => {
        beforeEach(() => {
          cy.viewport(viewport.width, viewport.height)
        })
        
        testFn(viewport)
      })
    }
  })
}

export const testOnAllMobileDevices = (testFn: (viewport: any) => void) => {
  testOnViewports(DEVICE_CATEGORIES.mobile, testFn)
}

export const testOnAllTabletDevices = (testFn: (viewport: any) => void) => {
  testOnViewports(DEVICE_CATEGORIES.tablet, testFn)
}

export const testOnAllDesktopSizes = (testFn: (viewport: any) => void) => {
  testOnViewports(DEVICE_CATEGORIES.desktop, testFn)
}

// Performance testing utilities
export const measurePageLoad = (url: string) => {
  cy.visit(url, {
    onBeforeLoad: (win) => {
      win.performance.mark('start')
    },
    onLoad: (win) => {
      win.performance.mark('end')
      win.performance.measure('pageLoad', 'start', 'end')
    }
  })
  
  cy.window().its('performance').then((performance) => {
    const measure = performance.getEntriesByName('pageLoad')[0]
    expect(measure.duration).to.be.lessThan(PERFORMANCE_THRESHOLDS.pageLoadTime)
  })
}

// Browser-specific workarounds
export const handleBrowserQuirks = () => {
  const browser = Cypress.browser
  
  if (browser.name === 'firefox') {
    // Firefox-specific adjustments
    cy.get('body').then(($body) => {
      // Handle Firefox scroll behavior
      $body.css('scroll-behavior', 'auto')
    })
  }
  
  if (browser.name === 'webkit') {
    // Safari/WebKit-specific adjustments
    cy.get('body').then(($body) => {
      // Handle WebKit sticky positioning
      $body.css('-webkit-overflow-scrolling', 'touch')
    })
  }
  
  if (browser.family === 'chromium') {
    // Chrome/Edge-specific adjustments
    cy.window().then((win) => {
      // Ensure proper rendering
      win.requestAnimationFrame(() => {
        // Force repaint
      })
    })
  }
}

// Touch event simulation for mobile testing
export const simulateTouch = (element: string, action: 'tap' | 'swipe' | 'pinch' = 'tap') => {
  if (isMobile()) {
    switch (action) {
      case 'tap':
        cy.get(element).trigger('touchstart').trigger('touchend')
        break
      case 'swipe':
        cy.get(element)
          .trigger('touchstart', { touches: [{ clientX: 100, clientY: 100 }] })
          .trigger('touchmove', { touches: [{ clientX: 200, clientY: 100 }] })
          .trigger('touchend')
        break
      case 'pinch':
        cy.get(element)
          .trigger('touchstart', { 
            touches: [
              { clientX: 100, clientY: 100 },
              { clientX: 200, clientY: 200 }
            ] 
          })
          .trigger('touchmove', { 
            touches: [
              { clientX: 120, clientY: 120 },
              { clientX: 180, clientY: 180 }
            ] 
          })
          .trigger('touchend')
        break
    }
  } else {
    cy.get(element).click()
  }
}

// Accessibility testing across browsers
export const checkAccessibility = () => {
  // Inject axe-core for accessibility testing
  cy.injectAxe()
  cy.checkA11y(null, {
    rules: {
      'color-contrast': { enabled: true },
      'keyboard-navigation': { enabled: true },
      'focus-management': { enabled: true }
    }
  })
}

// CSS compatibility testing
export const checkCssSupport = (property: string, value: string) => {
  cy.window().then((win) => {
    const testElement = win.document.createElement('div')
    testElement.style.setProperty(property, value)
    const computedValue = win.getComputedStyle(testElement).getPropertyValue(property)
    
    if (computedValue !== value) {
      cy.log(`CSS property ${property}: ${value} not supported in ${Cypress.browser.name}`)
    }
  })
}

// Feature detection
export const checkFeatureSupport = () => {
  cy.window().then((win) => {
    const features = {
      serviceWorker: 'serviceWorker' in win.navigator,
      localStorage: typeof win.localStorage !== 'undefined',
      sessionStorage: typeof win.sessionStorage !== 'undefined',
      indexedDB: 'indexedDB' in win,
      webGL: !!win.WebGLRenderingContext,
      webGL2: !!win.WebGL2RenderingContext,
      touchEvents: 'ontouchstart' in win,
      geolocation: 'geolocation' in win.navigator,
      notifications: 'Notification' in win,
      webRTC: 'RTCPeerConnection' in win,
      es6Modules: 'noModule' in win.document.createElement('script')
    }
    
    cy.log('Browser feature support:', features)
    return features
  })
}