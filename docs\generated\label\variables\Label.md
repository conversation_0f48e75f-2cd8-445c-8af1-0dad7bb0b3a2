[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [label](../README.md) / Label

# Variable: Label

> `const` **Label**: `ForwardRefExoticComponent`\<`Omit`\<`LabelProps` & `RefAttributes`\<`HTMLLabelElement`\>, `"ref"`\> & `VariantProps`\<(`props?`) => `string`\> & `RefAttributes`\<`HTMLLabelElement`\>\>

Defined in: [src/components/ui/label.tsx:11](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/label.tsx#L11)
