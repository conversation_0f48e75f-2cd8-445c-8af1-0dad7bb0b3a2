/**
 * Content Security Service
 * Enhanced content validation and security measures
 */

import { supabase } from '@/lib/supabase';
// Note: Toast functionality should be handled by the calling component

export interface ContentValidationResult {
  isValid: boolean;
  score: number; // 0-100, higher = more suspicious
  violations: ContentViolation[];
  sanitizedContent?: string;
  recommendation: 'allow' | 'warn' | 'block' | 'review';
}

export interface ContentViolation {
  type: 'profanity' | 'spam' | 'personal_info' | 'external_link' | 'excessive_caps' | 'repeated_content' | 'inappropriate_content';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  position?: { start: number; end: number };
}

export interface ContentReport {
  id: string;
  content_type: 'story' | 'contribution' | 'comment' | 'message';
  content_id: string;
  reported_by: string;
  report_reason: string;
  report_details?: string;
  status: 'pending' | 'reviewed' | 'resolved' | 'dismissed';
  created_at: string;
}

// Enhanced prohibited words and patterns
const PROHIBITED_WORDS = [
  // Basic profanity (would be much more comprehensive in production)
  'badword1', 'badword2', 'inappropriate',
  // Add more categories as needed
];

const SPAM_PATTERNS = [
  /(.)\1{4,}/g, // Repeated characters (aaaaa)
  /^[A-Z\s!]{20,}$/, // Excessive caps
  /\b(buy now|click here|free money|limited time)\b/gi, // Common spam phrases
];

const PERSONAL_INFO_PATTERNS = [
  /\b\d{3}-\d{2}-\d{4}\b/, // SSN pattern
  /\b\d{3}-\d{3}-\d{4}\b/, // Phone number pattern
  /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, // Email pattern
];

const EXTERNAL_LINK_PATTERNS = [
  /https?:\/\/(?!(?:localhost|127\.0\.0\.1|.*\.your-domain\.com))[^\s]+/gi,
];

// Content validation cache to prevent repeated processing
const validationCache = new Map<string, ContentValidationResult>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Validate content for security and appropriateness
 */
export const validateContent = (
  content: string,
  contentType: 'story' | 'contribution' | 'comment' | 'message' = 'contribution',
  userId?: string
): ContentValidationResult => {
  // Check cache first
  const cacheKey = `${content}-${contentType}`;
  const cached = validationCache.get(cacheKey);
  if (cached && Date.now() - cached.score < CACHE_DURATION) {
    return cached;
  }

  const violations: ContentViolation[] = [];
  let score = 0;

  // 1. Check for prohibited words
  const profanityViolations = checkProfanity(content);
  violations.push(...profanityViolations);
  score += profanityViolations.length * 25;

  // 2. Check for spam patterns
  const spamViolations = checkSpamPatterns(content);
  violations.push(...spamViolations);
  score += spamViolations.length * 15;

  // 3. Check for personal information
  const personalInfoViolations = checkPersonalInfo(content);
  violations.push(...personalInfoViolations);
  score += personalInfoViolations.length * 30;

  // 4. Check for external links
  const linkViolations = checkExternalLinks(content);
  violations.push(...linkViolations);
  score += linkViolations.length * 10;

  // 5. Check content length and quality
  const qualityViolations = checkContentQuality(content, contentType);
  violations.push(...qualityViolations);
  score += qualityViolations.length * 5;

  // 6. Check for HTML/script injection attempts
  const securityViolations = checkSecurityThreats(content);
  violations.push(...securityViolations);
  score += securityViolations.length * 50;

  // Determine recommendation based on score and violation severity
  const recommendation = determineRecommendation(score, violations);

  const result: ContentValidationResult = {
    isValid: recommendation === 'allow',
    score: Math.min(score, 100),
    violations,
    sanitizedContent: sanitizeContent(content),
    recommendation,
  };

  // Cache result
  validationCache.set(cacheKey, { ...result, score: Date.now() });

  return result;
};

/**
 * Check for prohibited words and profanity
 */
const checkProfanity = (content: string): ContentViolation[] => {
  const violations: ContentViolation[] = [];
  const lowerContent = content.toLowerCase();

  PROHIBITED_WORDS.forEach(word => {
    const index = lowerContent.indexOf(word.toLowerCase());
    if (index !== -1) {
      violations.push({
        type: 'profanity',
        severity: 'medium',
        description: `Contains prohibited language: "${word}"`,
        position: { start: index, end: index + word.length },
      });
    }
  });

  return violations;
};

/**
 * Check for spam patterns
 */
const checkSpamPatterns = (content: string): ContentViolation[] => {
  const violations: ContentViolation[] = [];

  SPAM_PATTERNS.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      violations.push({
        type: 'spam',
        severity: 'low',
        description: 'Content contains spam-like patterns',
      });
    }
  });

  // Check for excessive repetition
  if (content.length > 50) {
    const words = content.split(/\s+/);
    const uniqueWords = new Set(words.map(w => w.toLowerCase()));
    const repetitionRatio = uniqueWords.size / words.length;
    
    if (repetitionRatio < 0.3) {
      violations.push({
        type: 'spam',
        severity: 'medium',
        description: 'Content contains excessive repetition',
      });
    }
  }

  return violations;
};

/**
 * Check for personal information
 */
const checkPersonalInfo = (content: string): ContentViolation[] => {
  const violations: ContentViolation[] = [];

  PERSONAL_INFO_PATTERNS.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      violations.push({
        type: 'personal_info',
        severity: 'high',
        description: 'Content may contain personal information',
      });
    }
  });

  return violations;
};

/**
 * Check for external links
 */
const checkExternalLinks = (content: string): ContentViolation[] => {
  const violations: ContentViolation[] = [];

  EXTERNAL_LINK_PATTERNS.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      violations.push({
        type: 'external_link',
        severity: 'low',
        description: 'Content contains external links',
      });
    }
  });

  return violations;
};

/**
 * Check content quality and appropriateness
 */
const checkContentQuality = (content: string, contentType: string): ContentViolation[] => {
  const violations: ContentViolation[] = [];

  // Check minimum length for contributions
  if (contentType === 'contribution' && content.trim().length < 10) {
    violations.push({
      type: 'inappropriate_content',
      severity: 'low',
      description: 'Content is too short to be meaningful',
    });
  }

  // Check maximum length
  const maxLength = contentType === 'message' ? 500 : 2000;
  if (content.length > maxLength) {
    violations.push({
      type: 'inappropriate_content',
      severity: 'medium',
      description: `Content exceeds maximum length (${maxLength} characters)`,
    });
  }

  // Check for excessive caps
  const capsRatio = (content.match(/[A-Z]/g) || []).length / content.length;
  if (capsRatio > 0.7 && content.length > 20) {
    violations.push({
      type: 'excessive_caps',
      severity: 'low',
      description: 'Content uses excessive capital letters',
    });
  }

  return violations;
};

/**
 * Check for security threats (HTML, scripts, etc.)
 */
const checkSecurityThreats = (content: string): ContentViolation[] => {
  const violations: ContentViolation[] = [];

  // Check for HTML tags (already blocked by React, but extra validation)
  if (/<[^>]*>/g.test(content)) {
    violations.push({
      type: 'inappropriate_content',
      severity: 'critical',
      description: 'Content contains HTML tags',
    });
  }

  // Check for script-like content
  if (/javascript:|vbscript:|on\w+\s*=/gi.test(content)) {
    violations.push({
      type: 'inappropriate_content',
      severity: 'critical',
      description: 'Content contains script-like elements',
    });
  }

  // Check for SQL injection attempts
  if (/(union|select|insert|delete|drop|create|alter)\s+/gi.test(content)) {
    violations.push({
      type: 'inappropriate_content',
      severity: 'critical',
      description: 'Content contains SQL-like statements',
    });
  }

  return violations;
};

/**
 * Sanitize content by removing/replacing problematic elements
 */
const sanitizeContent = (content: string): string => {
  let sanitized = content;

  // Remove HTML tags
  sanitized = sanitized.replace(/<[^>]*>/g, '');

  // Remove potential script content
  sanitized = sanitized.replace(/javascript:|vbscript:|on\w+\s*=/gi, '');

  // Normalize whitespace
  sanitized = sanitized.replace(/\s+/g, ' ').trim();

  return sanitized;
};

/**
 * Determine recommendation based on violations and score
 */
const determineRecommendation = (
  score: number, 
  violations: ContentViolation[]
): 'allow' | 'warn' | 'block' | 'review' => {
  // Check for critical violations
  const hasCritical = violations.some(v => v.severity === 'critical');
  if (hasCritical) {
    return 'block';
  }

  // Check for high severity violations
  const hasHigh = violations.some(v => v.severity === 'high');
  if (hasHigh) {
    return 'review';
  }

  // Score-based recommendations
  if (score >= 75) {
    return 'block';
  } else if (score >= 50) {
    return 'review';
  } else if (score >= 25) {
    return 'warn';
  }

  return 'allow';
};

/**
 * Submit content report
 */
export const submitContentReport = async (
  contentType: 'story' | 'contribution' | 'comment' | 'message',
  contentId: string,
  reportReason: string,
  reportDetails?: string,
  reportedBy?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user && !reportedBy) {
      return { success: false, error: 'Must be authenticated to report content' };
    }

    const { error } = await supabase
      .from('content_reports')
      .insert({
        content_type: contentType,
        content_id: contentId,
        reported_by: reportedBy || user?.id,
        report_reason: reportReason,
        report_details: reportDetails,
        status: 'pending',
      });

    if (error) {
      console.error('Error submitting content report:', error);
      return { success: false, error: 'Failed to submit report' };
    }

    // Toast notification should be handled by the calling component
    console.log('Content reported successfully');

    return { success: true };
  } catch (error) {
    console.error('Error in submitContentReport:', error);
    return { success: false, error: 'Failed to submit report' };
  }
};

/**
 * Get content reports for moderation
 */
export const getContentReports = async (
  status?: 'pending' | 'reviewed' | 'resolved' | 'dismissed'
): Promise<ContentReport[]> => {
  try {
    let query = supabase
      .from('content_reports')
      .select('*')
      .order('created_at', { ascending: false });

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching content reports:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getContentReports:', error);
    return [];
  }
};

/**
 * Update content report status
 */
export const updateContentReportStatus = async (
  reportId: string,
  status: 'reviewed' | 'resolved' | 'dismissed',
  reviewNotes?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return { success: false, error: 'Must be authenticated to update reports' };
    }

    const { error } = await supabase
      .from('content_reports')
      .update({
        status,
        reviewed_by: user.id,
        review_notes: reviewNotes,
        updated_at: new Date().toISOString(),
      })
      .eq('id', reportId);

    if (error) {
      console.error('Error updating content report:', error);
      return { success: false, error: 'Failed to update report' };
    }

    return { success: true };
  } catch (error) {
    console.error('Error in updateContentReportStatus:', error);
    return { success: false, error: 'Failed to update report' };
  }
};

/**
 * Get user's report history
 */
export const getUserReportHistory = async (
  userId: string
): Promise<ContentReport[]> => {
  try {
    const { data, error } = await supabase
      .from('content_reports')
      .select('*')
      .eq('reported_by', userId)
      .order('created_at', { ascending: false })
      .limit(50);

    if (error) {
      console.error('Error fetching user report history:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getUserReportHistory:', error);
    return [];
  }
};

/**
 * Check if content has been reported
 */
export const getContentReportStatus = async (
  contentType: string,
  contentId: string
): Promise<{
  hasReports: boolean;
  reportCount: number;
  isUnderReview: boolean;
  reports: ContentReport[];
}> => {
  try {
    const { data, error } = await supabase
      .from('content_reports')
      .select('*')
      .eq('content_type', contentType)
      .eq('content_id', contentId);

    if (error) {
      console.error('Error checking content report status:', error);
      return { hasReports: false, reportCount: 0, isUnderReview: false, reports: [] };
    }

    const reports = data || [];
    const hasReports = reports.length > 0;
    const isUnderReview = reports.some(r => r.status === 'pending' || r.status === 'reviewed');

    return {
      hasReports,
      reportCount: reports.length,
      isUnderReview,
      reports,
    };
  } catch (error) {
    console.error('Error in getContentReportStatus:', error);
    return { hasReports: false, reportCount: 0, isUnderReview: false, reports: [] };
  }
};

/**
 * Clean up old validation cache entries
 */
export const cleanupValidationCache = (): void => {
  const now = Date.now();
  for (const [key, value] of validationCache.entries()) {
    if (now - value.score > CACHE_DURATION) {
      validationCache.delete(key);
    }
  }
};

// Auto-cleanup every 10 minutes
setInterval(cleanupValidationCache, 10 * 60 * 1000);