-- Migration: Add special token-based action fields to contributions table
-- File: supabase/migrations/0010_add_special_token_fields.sql

-- Add special token fields to contributions table
ALTER TABLE public.contributions 
ADD COLUMN IF NOT EXISTS "position" INTEGER,
ADD COLUMN IF NOT EXISTS special_type TEXT CHECK (special_type IN ('gotcha', 'reverse', 'golden')),
ADD COLUMN IF NOT EXISTS token_cost INTEGER;

-- Set position values for existing contributions based on their order
UPDATE public.contributions SET "position" = "order" WHERE "position" IS NULL;

-- Make position NOT NULL after updating existing records
ALTER TABLE public.contributions ALTER COLUMN "position" SET NOT NULL;

-- Add index for performance on position queries
CREATE INDEX IF NOT EXISTS idx_contributions_story_position ON public.contributions(story_id, "position");

-- Add index for special token queries
CREATE INDEX IF NOT EXISTS idx_contributions_special_type ON public.contributions(special_type) WHERE special_type IS NOT NULL;

-- Add constraint to ensure position is unique within a story (similar to order)
ALTER TABLE public.contributions ADD CONSTRAINT unique_story_position UNIQUE (story_id, "position");

-- Update the add_contribution_to_story function to handle new fields
CREATE OR REPLACE FUNCTION public.add_contribution_to_story(
    p_story_id UUID,
    p_user_id UUID,
    p_content TEXT,
    p_special_type TEXT DEFAULT NULL,
    p_token_cost INTEGER DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    story_id UUID,
    user_id UUID,
    content TEXT,
    word_count INT,
    "order" INT,
    "position" INT,
    special_type TEXT,
    token_cost INT,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ,
    error_type TEXT,
    error_message TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_story RECORD;
    v_last_contribution RECORD;
    v_new_word_count INT;
    v_next_order INT;
    v_next_position INT;
    v_new_contribution_id UUID;
    v_is_max_contributors_reached BOOLEAN := false;
    v_current_contributors_count INT;
BEGIN
    -- Lock the story row to prevent concurrent updates
    SELECT * INTO v_story FROM public.stories WHERE public.stories.id = p_story_id FOR UPDATE;

    -- Check if story exists
    IF NOT FOUND THEN
        RETURN QUERY SELECT NULL::UUID, NULL::UUID, NULL::UUID, NULL::TEXT, NULL::INT, NULL::INT, NULL::INT, NULL::TEXT, NULL::INT, NULL::TIMESTAMPTZ, NULL::TIMESTAMPTZ, 'NOT_FOUND'::TEXT, 'Story not found.'::TEXT;
        RETURN;
    END IF;

    -- Check if story is in progress
    IF v_story.status <> 'in_progress' THEN
        RETURN QUERY SELECT NULL::UUID, NULL::UUID, NULL::UUID, NULL::TEXT, NULL::INT, NULL::INT, NULL::INT, NULL::TEXT, NULL::INT, NULL::TIMESTAMPTZ, NULL::TIMESTAMPTZ, 'STORY_NOT_ACTIVE'::TEXT, 'Story is not in progress.'::TEXT;
        RETURN;
    END IF;

    -- Validate special_type if provided
    IF p_special_type IS NOT NULL AND p_special_type NOT IN ('gotcha', 'reverse', 'golden') THEN
        RETURN QUERY SELECT NULL::UUID, NULL::UUID, NULL::UUID, NULL::TEXT, NULL::INT, NULL::INT, NULL::INT, NULL::TEXT, NULL::INT, NULL::TIMESTAMPTZ, NULL::TIMESTAMPTZ, 'INVALID_SPECIAL_TYPE'::TEXT, 'Invalid special type provided.'::TEXT;
        RETURN;
    END IF;

    -- Get the last contribution to check order and last contributor
    SELECT * INTO v_last_contribution
    FROM public.contributions
    WHERE public.contributions.story_id = p_story_id
    ORDER BY "order" DESC
    LIMIT 1;

    IF FOUND THEN
        -- Check if the current user is the same as the last contributor
        IF v_last_contribution.user_id = p_user_id THEN
            RETURN QUERY SELECT NULL::UUID, NULL::UUID, NULL::UUID, NULL::TEXT, NULL::INT, NULL::INT, NULL::INT, NULL::TEXT, NULL::INT, NULL::TIMESTAMPTZ, NULL::TIMESTAMPTZ, 'CONSECUTIVE_CONTRIBUTION'::TEXT, 'You cannot make two consecutive contributions.'::TEXT;
            RETURN;
        END IF;
        v_next_order := v_last_contribution."order" + 1;
        v_next_position := COALESCE(v_last_contribution."position", v_last_contribution."order") + 1;
    ELSE
        v_next_order := 1;
        v_next_position := 1;
    END IF;

    -- Calculate word count
    v_new_word_count := array_length(string_to_array(trim(p_content), ' '), 1);
    IF p_content = '' OR p_content IS NULL THEN
        v_new_word_count := 0;
    END IF;
    
    IF v_new_word_count = 0 THEN
        RETURN QUERY SELECT NULL::UUID, NULL::UUID, NULL::UUID, NULL::TEXT, NULL::INT, NULL::INT, NULL::INT, NULL::TEXT, NULL::INT, NULL::TIMESTAMPTZ, NULL::TIMESTAMPTZ, 'EMPTY_CONTENT'::TEXT, 'Contribution content cannot be empty.'::TEXT;
        RETURN;
    END IF;

    -- Check word count limit
    IF v_new_word_count > v_story.max_words_per_contribution THEN
        RETURN QUERY SELECT NULL::UUID, NULL::UUID, NULL::UUID, NULL::TEXT, NULL::INT, NULL::INT, NULL::INT, NULL::TEXT, NULL::INT, NULL::TIMESTAMPTZ, NULL::TIMESTAMPTZ, 'WORD_LIMIT_EXCEEDED'::TEXT, 'Contribution exceeds word limit for this story.'::TEXT;
        RETURN;
    END IF;
    
    -- Check max contributors
    SELECT COUNT(DISTINCT user_id) INTO v_current_contributors_count 
    FROM public.contributions 
    WHERE public.contributions.story_id = p_story_id;

    -- If the current user is a new contributor
    IF NOT EXISTS (SELECT 1 FROM public.contributions WHERE public.contributions.story_id = p_story_id AND public.contributions.user_id = p_user_id) THEN
        IF v_current_contributors_count >= v_story.max_contributors THEN
            v_is_max_contributors_reached := true;
        END IF;
    END IF;
    
    -- If max contributors is already reached
    IF v_is_max_contributors_reached THEN
         RETURN QUERY SELECT NULL::UUID, NULL::UUID, NULL::UUID, NULL::TEXT, NULL::INT, NULL::INT, NULL::INT, NULL::TEXT, NULL::INT, NULL::TIMESTAMPTZ, NULL::TIMESTAMPTZ, 'MAX_CONTRIBUTORS_REACHED'::TEXT, 'Maximum number of contributors reached for this story.'::TEXT;
         RETURN;
    END IF;

    -- Insert the new contribution with special token fields
    INSERT INTO public.contributions (story_id, user_id, content, word_count, "order", "position", special_type, token_cost)
    VALUES (p_story_id, p_user_id, p_content, v_new_word_count, v_next_order, v_next_position, p_special_type, p_token_cost)
    RETURNING public.contributions.id INTO v_new_contribution_id;

    -- Update story's word count and last contribution time
    UPDATE public.stories
    SET 
        current_word_count = public.stories.current_word_count + v_new_word_count,
        last_contribution_at = now()
    WHERE public.stories.id = p_story_id;
    
    -- Check if the story should now be marked as completed
    SELECT COUNT(DISTINCT user_id) INTO v_current_contributors_count 
    FROM public.contributions 
    WHERE public.contributions.story_id = p_story_id;

    IF v_current_contributors_count >= v_story.max_contributors THEN
        UPDATE public.stories
        SET status = 'completed', completed_at = now()
        WHERE public.stories.id = p_story_id AND public.stories.status = 'in_progress';
    END IF;

    -- Return the newly created contribution
    RETURN QUERY 
    SELECT c.id, c.story_id, c.user_id, c.content, c.word_count, c."order", c."position", c.special_type, c.token_cost, c.created_at, c.updated_at, NULL::TEXT, NULL::TEXT 
    FROM public.contributions c WHERE c.id = v_new_contribution_id;

EXCEPTION 
    WHEN OTHERS THEN
        RAISE NOTICE 'Error in add_contribution_to_story: %', SQLERRM;
        RETURN QUERY SELECT NULL::UUID, NULL::UUID, NULL::UUID, NULL::TEXT, NULL::INT, NULL::INT, NULL::INT, NULL::TEXT, NULL::INT, NULL::TIMESTAMPTZ, NULL::TIMESTAMPTZ, 'DATABASE_ERROR'::TEXT, SQLERRM::TEXT;
END;
$$;

-- Grant execute permission to the authenticated role
GRANT EXECUTE ON FUNCTION public.add_contribution_to_story(UUID, UUID, TEXT, TEXT, INTEGER) TO authenticated;

-- Add comments for documentation
COMMENT ON COLUMN public.contributions."position" IS 'Position of the contribution in the story (similar to order but supports special actions)';
COMMENT ON COLUMN public.contributions.special_type IS 'Type of special token-based action: gotcha, reverse, or golden';
COMMENT ON COLUMN public.contributions.token_cost IS 'Number of tokens spent for special actions'; 