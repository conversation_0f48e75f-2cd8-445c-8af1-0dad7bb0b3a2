[**Word by Word Story - UI Components v0.0.0**](../README.md)

***

[Word by Word Story - UI Components](../modules.md) / select

# select

## Variables

- [Select](variables/Select.md)
- [SelectContent](variables/SelectContent.md)
- [SelectGroup](variables/SelectGroup.md)
- [SelectItem](variables/SelectItem.md)
- [SelectLabel](variables/SelectLabel.md)
- [SelectScrollDownButton](variables/SelectScrollDownButton.md)
- [SelectScrollUpButton](variables/SelectScrollUpButton.md)
- [SelectSeparator](variables/SelectSeparator.md)
- [SelectTrigger](variables/SelectTrigger.md)
- [SelectValue](variables/SelectValue.md)
