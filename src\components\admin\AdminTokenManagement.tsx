import React, { useState, useEffect } from 'react';
import { Search, Plus, Users, History, Download, Coins } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Ava<PERSON>, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { adminService, UserSearchResult, AdminTokenGrant } from '@/services/adminService';
import { formatDistanceToNow } from 'date-fns';

interface TokenGrantFormData {
  targetUserId: string;
  amount: number;
  reason: string;
  grantType: 'manual' | 'correction' | 'automated';
}

interface BulkGrantFormData {
  targetUserIds: string[];
  amount: number;
  reason: string;
}

export const AdminTokenManagement: React.FC = () => {
  const { user, isAdmin } = useAuth();
  const { toast } = useToast();

  // State
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<UserSearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [tokenHistory, setTokenHistory] = useState<AdminTokenGrant[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [currentAdminSession, setCurrentAdminSession] = useState<string | null>(null);

  // Form states
  const [singleGrantForm, setSingleGrantForm] = useState<TokenGrantFormData>({
    targetUserId: '',
    amount: 0,
    reason: '',
    grantType: 'manual'
  });
  const [bulkGrantForm, setBulkGrantForm] = useState<BulkGrantFormData>({
    targetUserIds: [],
    amount: 0,
    reason: ''
  });
  const [isGranting, setIsGranting] = useState(false);

  // Dialog states
  const [showSingleGrantDialog, setShowSingleGrantDialog] = useState(false);
  const [showBulkGrantDialog, setShowBulkGrantDialog] = useState(false);

  // Check admin access
  useEffect(() => {
    if (!user?.id || !isAdmin()) {
      toast({
        title: 'Access Denied',
        description: 'Admin privileges required to access token management.',
        variant: 'destructive',
      });
      return;
    }
    
    // Start admin session
    startAdminSession();
    loadTokenHistory();
  }, [user?.id, isAdmin]);

  // Start admin session
  const startAdminSession = async () => {
    if (!user?.id) return;

    try {
      const result = await adminService.startAdminSession(
        user.id,
        undefined,
        navigator.userAgent
      );
      
      if (result.success && result.sessionId) {
        setCurrentAdminSession(result.sessionId);
      }
    } catch (error) {
      console.error('Failed to start admin session:', error);
    }
  };

  // Search users
  const handleSearch = async () => {
    if (!user?.id || !searchTerm.trim()) return;

    setIsSearching(true);
    try {
      const results = await adminService.searchUsers(searchTerm, 20, true);
      setSearchResults(results);
      
      // Log search action
      await adminService.logAdminAction(
        user.id,
        'user_search',
        undefined,
        { search_term: searchTerm, results_count: results.length },
        currentAdminSession || undefined
      );
    } catch (error) {
      console.error('Search failed:', error);
      toast({
        title: 'Search Failed',
        description: 'Failed to search users. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSearching(false);
    }
  };

  // Load token grant history
  const loadTokenHistory = async () => {
    if (!user?.id) return;

    setIsLoadingHistory(true);
    try {
      const history = await adminService.getTokenGrantHistory(user.id, { limit: 50 });
      setTokenHistory(history);
    } catch (error) {
      console.error('Failed to load token history:', error);
      toast({
        title: 'Load Failed',
        description: 'Failed to load token grant history.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingHistory(false);
    }
  };

  // Grant tokens to single user
  const handleSingleGrant = async () => {
    if (!user?.id || !singleGrantForm.targetUserId || singleGrantForm.amount <= 0 || !singleGrantForm.reason.trim()) {
      toast({
        title: 'Invalid Input',
        description: 'Please fill in all required fields with valid values.',
        variant: 'destructive',
      });
      return;
    }

    if (singleGrantForm.reason.trim().length < 10) {
      toast({
        title: 'Invalid Reason',
        description: 'Reason must be at least 10 characters long.',
        variant: 'destructive',
      });
      return;
    }

    setIsGranting(true);
    try {
      const result = await adminService.grantTokens(
        user.id,
        singleGrantForm.targetUserId,
        singleGrantForm.amount,
        singleGrantForm.reason.trim(),
        singleGrantForm.grantType,
        undefined,
        currentAdminSession || undefined
      );

      if (result.success) {
        toast({
          title: 'Tokens Granted',
          description: `Successfully granted ${singleGrantForm.amount} tokens. New balance: ${result.newBalance}`,
        });
        
        // Reset form and refresh data
        setSingleGrantForm({ targetUserId: '', amount: 0, reason: '', grantType: 'manual' });
        setShowSingleGrantDialog(false);
        await loadTokenHistory();
      } else {
        toast({
          title: 'Grant Failed',
          description: result.errorMessage || 'Failed to grant tokens',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Grant failed:', error);
      toast({
        title: 'Grant Failed',
        description: 'Failed to grant tokens. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsGranting(false);
    }
  };

  // Bulk grant tokens
  const handleBulkGrant = async () => {
    if (!user?.id || bulkGrantForm.targetUserIds.length === 0 || bulkGrantForm.amount <= 0 || !bulkGrantForm.reason.trim()) {
      toast({
        title: 'Invalid Input',
        description: 'Please select users and fill in all required fields.',
        variant: 'destructive',
      });
      return;
    }

    if (bulkGrantForm.reason.trim().length < 10) {
      toast({
        title: 'Invalid Reason',
        description: 'Reason must be at least 10 characters long.',
        variant: 'destructive',
      });
      return;
    }

    setIsGranting(true);
    try {
      const result = await adminService.bulkGrantTokens(
        user.id,
        bulkGrantForm.targetUserIds,
        bulkGrantForm.amount,
        bulkGrantForm.reason.trim(),
        currentAdminSession || undefined
      );

      toast({
        title: 'Bulk Grant Complete',
        description: `${result.successfulGrants}/${result.totalGranted} grants successful${result.failedGrants > 0 ? `, ${result.failedGrants} failed` : ''}`,
        variant: result.failedGrants > 0 ? 'destructive' : 'default',
      });

      // Reset form and refresh data
      setBulkGrantForm({ targetUserIds: [], amount: 0, reason: '' });
      setSelectedUsers([]);
      setShowBulkGrantDialog(false);
      await loadTokenHistory();
    } catch (error) {
      console.error('Bulk grant failed:', error);
      toast({
        title: 'Bulk Grant Failed',
        description: 'Failed to perform bulk grant. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsGranting(false);
    }
  };

  // Export token history
  const handleExport = async () => {
    if (!user?.id) return;

    try {
      const data = await adminService.exportAdminData(user.id, 'token_grants');
      
      // Create and download CSV
      const csvContent = [
        'Grant ID,Admin Username,Target Username,Amount,Reason,Grant Type,Created At',
        ...data.map(grant => [
          grant.id,
          grant.admin_username || '',
          grant.target_username || '',
          grant.amount,
          `"${grant.reason.replace(/"/g, '""')}"`,
          grant.grant_type,
          new Date(grant.created_at).toISOString()
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `token-grants-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      URL.revokeObjectURL(url);

      toast({
        title: 'Export Complete',
        description: `Exported ${data.length} token grant records`,
      });
    } catch (error) {
      console.error('Export failed:', error);
      toast({
        title: 'Export Failed',
        description: 'Failed to export token grant data.',
        variant: 'destructive',
      });
    }
  };

  // Toggle user selection for bulk operations
  const toggleUserSelection = (userId: string) => {
    setSelectedUsers(prev => 
      prev.includes(userId) 
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  // Set up single grant form for specific user
  const setupSingleGrant = (user: UserSearchResult) => {
    setSingleGrantForm(prev => ({ ...prev, targetUserId: user.id }));
    setShowSingleGrantDialog(true);
  };

  if (!user?.id || !isAdmin()) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-muted-foreground">Access denied. Admin privileges required.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Token Management</h2>
          <p className="text-muted-foreground">
            Grant tokens to users and manage token distribution
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExport} disabled={isLoadingHistory}>
            <Download className="w-4 h-4 mr-2" />
            Export History
          </Button>
        </div>
      </div>

      <Tabs defaultValue="search" className="space-y-4">
        <TabsList>
          <TabsTrigger value="search" className="flex items-center gap-2">
            <Search className="w-4 h-4" />
            User Search
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <History className="w-4 h-4" />
            Grant History
          </TabsTrigger>
        </TabsList>

        <TabsContent value="search" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Find Users
              </CardTitle>
              <CardDescription>
                Search for users by username to grant tokens
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="Search by username..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  className="flex-1"
                />
                <Button onClick={handleSearch} disabled={isSearching || !searchTerm.trim()}>
                  {isSearching ? 'Searching...' : 'Search'}
                </Button>
              </div>

              {searchResults.length > 0 && (
                <>
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-muted-foreground">
                      Found {searchResults.length} users
                    </p>
                    <div className="flex gap-2">
                      {selectedUsers.length > 0 && (
                        <Dialog open={showBulkGrantDialog} onOpenChange={setShowBulkGrantDialog}>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Plus className="w-4 h-4 mr-2" />
                              Bulk Grant ({selectedUsers.length})
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Bulk Grant Tokens</DialogTitle>
                              <DialogDescription>
                                Grant tokens to {selectedUsers.length} selected users
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div>
                                <Label htmlFor="bulk-amount">Token Amount</Label>
                                <Input
                                  id="bulk-amount"
                                  type="number"
                                  min="1"
                                  value={bulkGrantForm.amount || ''}
                                  onChange={(e) => setBulkGrantForm(prev => ({ ...prev, amount: parseInt(e.target.value) || 0 }))}
                                  placeholder="Enter amount..."
                                />
                              </div>
                              <div>
                                <Label htmlFor="bulk-reason">Reason (minimum 10 characters)</Label>
                                <Textarea
                                  id="bulk-reason"
                                  value={bulkGrantForm.reason}
                                  onChange={(e) => setBulkGrantForm(prev => ({ ...prev, reason: e.target.value }))}
                                  placeholder="Enter reason for bulk token grant..."
                                  rows={3}
                                />
                              </div>
                              <div className="flex justify-end gap-2">
                                <Button variant="outline" onClick={() => setShowBulkGrantDialog(false)}>
                                  Cancel
                                </Button>
                                <Button 
                                  onClick={() => {
                                    setBulkGrantForm(prev => ({ ...prev, targetUserIds: selectedUsers }));
                                    handleBulkGrant();
                                  }}
                                  disabled={isGranting}
                                >
                                  {isGranting ? 'Granting...' : 'Grant Tokens'}
                                </Button>
                              </div>
                            </div>
                          </DialogContent>
                        </Dialog>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    {searchResults.map((user) => (
                      <div
                        key={user.id}
                        className={`flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors ${
                          selectedUsers.includes(user.id) ? 'bg-accent border-primary' : ''
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={selectedUsers.includes(user.id)}
                            onChange={() => toggleUserSelection(user.id)}
                            className="rounded"
                          />
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={user.avatarUrl} />
                            <AvatarFallback>
                              {user.username?.substring(0, 2).toUpperCase() || 'U'}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{user.username || 'Unknown'}</p>
                            <p className="text-sm text-muted-foreground">
                              ID: {user.id.substring(0, 8)}...
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <Badge variant="outline" className="flex items-center gap-1">
                            <Coins className="w-3 h-3" />
                            {user.tokenBalance || 0} tokens
                          </Badge>
                          <Dialog open={showSingleGrantDialog} onOpenChange={setShowSingleGrantDialog}>
                            <DialogTrigger asChild>
                              <Button size="sm" onClick={() => setupSingleGrant(user)}>
                                <Plus className="w-4 h-4 mr-1" />
                                Grant
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Grant Tokens</DialogTitle>
                                <DialogDescription>
                                  Grant tokens to {user.username}
                                </DialogDescription>
                              </DialogHeader>
                              <div className="space-y-4">
                                <div>
                                  <Label htmlFor="amount">Token Amount</Label>
                                  <Input
                                    id="amount"
                                    type="number"
                                    min="1"
                                    value={singleGrantForm.amount || ''}
                                    onChange={(e) => setSingleGrantForm(prev => ({ ...prev, amount: parseInt(e.target.value) || 0 }))}
                                    placeholder="Enter amount..."
                                  />
                                </div>
                                <div>
                                  <Label htmlFor="grant-type">Grant Type</Label>
                                  <Select
                                    value={singleGrantForm.grantType}
                                    onValueChange={(value: 'manual' | 'correction' | 'automated') => 
                                      setSingleGrantForm(prev => ({ ...prev, grantType: value }))
                                    }
                                  >
                                    <SelectTrigger>
                                      <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="manual">Manual Grant</SelectItem>
                                      <SelectItem value="correction">Correction</SelectItem>
                                      <SelectItem value="automated">Automated</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                                <div>
                                  <Label htmlFor="reason">Reason (minimum 10 characters)</Label>
                                  <Textarea
                                    id="reason"
                                    value={singleGrantForm.reason}
                                    onChange={(e) => setSingleGrantForm(prev => ({ ...prev, reason: e.target.value }))}
                                    placeholder="Enter reason for token grant..."
                                    rows={3}
                                  />
                                </div>
                                <div className="flex justify-end gap-2">
                                  <Button variant="outline" onClick={() => setShowSingleGrantDialog(false)}>
                                    Cancel
                                  </Button>
                                  <Button onClick={handleSingleGrant} disabled={isGranting}>
                                    {isGranting ? 'Granting...' : 'Grant Tokens'}
                                  </Button>
                                </div>
                              </div>
                            </DialogContent>
                          </Dialog>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              )}

              {searchResults.length === 0 && searchTerm && !isSearching && (
                <p className="text-center text-muted-foreground py-8">
                  No users found matching "{searchTerm}"
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="w-5 h-5" />
                Token Grant History
              </CardTitle>
              <CardDescription>
                Recent token grants performed by administrators
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingHistory ? (
                <p className="text-center py-8">Loading history...</p>
              ) : tokenHistory.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Admin</TableHead>
                      <TableHead>Target User</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Reason</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tokenHistory.map((grant) => (
                      <TableRow key={grant.id}>
                        <TableCell className="text-sm">
                          {formatDistanceToNow(new Date(grant.createdAt), { addSuffix: true })}
                        </TableCell>
                        <TableCell className="font-medium">
                          {grant.adminUsername || 'Unknown'}
                        </TableCell>
                        <TableCell>
                          {grant.targetUsername || 'Unknown'}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="flex items-center gap-1 w-fit">
                            <Coins className="w-3 h-3" />
                            {grant.amount}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={grant.grantType === 'manual' ? 'default' : 'secondary'}>
                            {grant.grantType}
                          </Badge>
                        </TableCell>
                        <TableCell className="max-w-xs truncate" title={grant.reason}>
                          {grant.reason}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <p className="text-center text-muted-foreground py-8">
                  No token grants found
                </p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}; 