import React, { useState, useEffect, useRef } from 'react';
import { ContributionMode } from '@/services/realTimeService';
import { useStory } from '@/contexts/StoryContext';
import { config } from '@/config/environment';
import { TskTskTskButton } from '@/components/story/TskTskTskButton';
import { QuickUndoTimer } from '@/components/story/QuickUndoTimer';
import { undoContribution } from '@/services/undoService';
import { useToast } from '@/hooks/use-toast';

interface ContributionFormProps {
  storyId: string;
  userId: string;
  username: string;
  mode: ContributionMode;
  wordsPerContribution?: number;
  onSubmit: (contribution: any) => void;
  disabled?: boolean;
  gotchaWord?: string;
}

const ContributionForm: React.FC<ContributionFormProps> = ({
  storyId,
  userId,
  username,
  mode,
  wordsPerContribution = 5,
  onSubmit,
  disabled,
  gotchaWord,
}) => {
  const [content, setContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [wordCount, setWordCount] = useState(0);
  const [recentContribution, setRecentContribution] = useState<{
    id: string;
    content: string;
    timestamp: number;
  } | null>(null);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);
  const { handleUserTyping, currentStory } = useStory();
  const { toast } = useToast();

  // Focus input when it becomes user's turn
  useEffect(() => {
    if (!disabled && inputRef.current) {
      inputRef.current.focus();
    }
  }, [disabled]);

  // Update word count when content changes
  useEffect(() => {
    if (content) {
      const words = content.trim().split(/\s+/);
      setWordCount(words.length);
    } else {
      setWordCount(0);
    }
  }, [content]);

  // Broadcast typing status when user is typing
  useEffect(() => {
    // Only broadcast typing status on significant changes to prevent excessive updates
    if (content && !disabled) {
      // Use a debounce approach to limit typing status updates
      const timeout = setTimeout(() => {
        handleUserTyping(true);
        
        // Clear typing status after 2 seconds of inactivity
        const inactivityTimeout = setTimeout(() => {
          handleUserTyping(false);
        }, 2000);
        
        return () => clearTimeout(inactivityTimeout);
      }, 500); // Wait 500ms before sending typing status
      
      return () => clearTimeout(timeout);
    } else if (!content && !disabled) {
      // If content is cleared, immediately send not typing
      handleUserTyping(false);
    }
  }, [content, disabled, handleUserTyping]);

  // Validate the contribution based on the mode and gotcha word if provided
  const validateContribution = (content: string): { isValid: boolean; error?: string } => {
    if (!content.trim()) {
      return { isValid: false, error: 'Contribution cannot be empty' };
    }

    // PRIORITY CHECK: If a gotcha word is provided, check if it's included in the contribution
    // This check takes precedence over other validations
    if (gotchaWord && gotchaWord.trim() !== '') {
      const normalizedContent = content.toLowerCase();
      const normalizedGotchaWord = gotchaWord.toLowerCase();
      
      if (!normalizedContent.includes(normalizedGotchaWord)) {
        return {
          isValid: false,
          error: `⚠️ GOTCHA WORD REQUIRED: Your contribution must include the word "${gotchaWord}"`,
        };
      }
    }

    // Then validate based on the contribution mode
    switch (mode) {
      case ContributionMode.WORD:
        // Check if the contribution is a single word
        if (content.trim().split(/\s+/).length !== 1) {
          return { isValid: false, error: 'Please enter exactly one word' };
        }
        break;
      case ContributionMode.MULTI_WORD: {
        // Check if the contribution has the correct number of words
        const wordCount = content.trim().split(/\s+/).length;
        if (wordCount !== wordsPerContribution) {
          return {
            isValid: false,
            error: `Please enter exactly ${wordsPerContribution} words`,
          };
        }
        break;
      }
      case ContributionMode.SENTENCE:
        // Check if the contribution is a valid sentence
        if (!content.trim().match(/[.!?]$/)) {
          return {
            isValid: false,
            error: 'Please enter a complete sentence ending with a period, exclamation mark, or question mark',
          };
        }
        break;
      case ContributionMode.PARAGRAPH: {
        // Check if the contribution is a valid paragraph (at least 3 sentences)
        const sentenceCount = content.trim().split(/[.!?]\s+/).length;
        if (sentenceCount < 3) {
          return {
            isValid: false,
            error: 'Please enter at least 3 sentences',
          };
        }
        break;
      }
    }

    return { isValid: true };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validateContribution(content);
    if (!validation.isValid) {
      setError(validation.error);
      return;
    }
    
    try {
      setIsSubmitting(true);
      setError(null);
      
      let result;
      
      // Use real API in production, mock data in demo/development
      if (config.useRealApi) {
        // Call the API to submit the contribution
        const apiUrl = `${config.api.baseUrl}${config.api.endpoints.contribute(storyId)}`;
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            content: content.trim(),
            userId,
            username,
            mode,
          }),
        });
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to submit contribution');
        }
        
        result = await response.json();
      } else {
        // For the demo, create a mock result
        result = {
          id: `contribution-${Date.now()}`,
          content: content.trim(),
          userId,
          username,
          type: mode,
          position: 0, // This will be handled by the parent component
          createdAt: new Date().toISOString(),
        };
      }
      
      // Clear the form
      setContent('');
      setWordCount(0);
      
      // Set recent contribution for quick undo
      setRecentContribution({
        id: result.id,
        content: result.content,
        timestamp: Date.now(),
      });
      
      // Notify parent component
      onSubmit(result);
      
      // Clear typing status
      handleUserTyping(false);
    } catch (err) {
      setError((err as Error).message);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get placeholder text based on mode
  const getPlaceholder = () => {
    switch (mode) {
      case ContributionMode.WORD:
        return 'Enter a single word...';
      case ContributionMode.MULTI_WORD:
        return `Enter up to ${wordsPerContribution} words...`;
      case ContributionMode.SENTENCE:
        return 'Enter a complete sentence...';
      case ContributionMode.PARAGRAPH:
        return 'Enter a paragraph...';
      default:
        return 'Enter your contribution...';
    }
  };

  // Get input component based on mode
  const getInputComponent = () => {
    // For word and multi-word modes, use a single-line input
    if (mode === ContributionMode.WORD || mode === ContributionMode.MULTI_WORD) {
      return (
        <div className="w-full">
          {gotchaWord && (
            <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <div className="flex items-center">
                <span className="text-yellow-600 font-medium mr-2">⚠️</span>
                <span className="text-yellow-800">
                  You must include the word <span className="font-bold">"{gotchaWord}"</span> in your contribution
                </span>
              </div>
            </div>
          )}
          <input
            ref={inputRef as React.RefObject<HTMLInputElement>}
            type="text"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder={gotchaWord ? `Enter the word "${gotchaWord}"...` : "Enter a single word..."}
            className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${gotchaWord ? 'border-yellow-400' : ''}`}
            disabled={disabled || isSubmitting}
          />
          {/* Help text is already shown by the placeholder */}
        </div>
      );
    }
    
    // For sentence and paragraph modes, use a textarea
    return (
      <div className="w-full">
        {gotchaWord && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="flex items-center">
              <span className="text-yellow-600 font-medium mr-2">⚠️</span>
              <span className="text-yellow-800">
                You must include the word <span className="font-bold">"{gotchaWord}"</span> in your contribution
              </span>
            </div>
          </div>
        )}
        <textarea
          ref={inputRef as React.RefObject<HTMLTextAreaElement>}
          value={content}
          onChange={(e) => setContent(e.target.value)}
          placeholder={getPlaceholder()}
          disabled={disabled || isSubmitting}
          className="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows={mode === ContributionMode.SENTENCE ? 2 : 4}
          maxLength={mode === ContributionMode.SENTENCE ? 200 : 1000}
        />
        <div className="text-sm text-gray-500 mt-1">
          {mode === ContributionMode.SENTENCE ? 'Enter a complete sentence ending with punctuation' : 'Enter a paragraph with complete sentences'}
        </div>
      </div>
    );
  };

  // Get helper text based on mode
  const getHelperText = () => {
    switch (mode) {
      case ContributionMode.WORD:
        return 'Enter a single word';
      case ContributionMode.MULTI_WORD:
        return `${wordCount}/${wordsPerContribution} words`;
      case ContributionMode.SENTENCE:
        return 'Enter a complete sentence ending with punctuation';
      case ContributionMode.PARAGRAPH:
        return 'Enter a paragraph with complete sentences';
      default:
        return '';
    }
  };

  // Handle undo functionality
  const handleUndo = async (contributionId: string) => {
    try {
      const result = await undoContribution(contributionId, userId);
      
      if (result.success) {
        toast({
          title: 'Contribution Undone',
          description: result.message,
        });
        
        // Refresh the page to show updated contributions
        window.location.reload();
      } else {
        toast({
          title: 'Undo Failed',
          description: result.message,
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Undo Error',
        description: 'Failed to undo contribution. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Get the last contribution for undo functionality
  const lastContribution = currentStory?.contributions && currentStory.contributions.length > 0 
    ? currentStory.contributions[currentStory.contributions.length - 1]
    : undefined;
  
  // Convert to the format expected by TskTskTskButton
  const lastContributionForUndo = lastContribution ? {
    id: lastContribution.id,
    content: lastContribution.content,
    author: {
      id: lastContribution.author.id,
      username: lastContribution.author.username || 'Unknown User'
    },
    created_at: lastContribution.created_at
  } : undefined;
  
  // Check if user can undo (last contribution exists and is not from current user)
  const canUndo = lastContribution && lastContribution.author.id !== userId;

  // Handle quick undo for own contributions
  const handleQuickUndo = async (contributionId: string) => {
    try {
      // For quick undo, we can directly remove from the parent component
      // or call a special quick undo API that doesn't cost credits
      await handleUndo(contributionId);
      setRecentContribution(null);
      
      toast({
        title: 'Contribution Undone',
        description: 'Your contribution has been removed.',
      });
    } catch (error) {
      toast({
        title: 'Quick Undo Failed',
        description: 'Failed to undo your contribution. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleQuickUndoExpire = () => {
    setRecentContribution(null);
  };

  return (
    <div className="w-full max-w-2xl mx-auto space-y-4">
      {/* Quick Undo Timer - appears immediately after user submits their own contribution */}
      {recentContribution && (
        <div className="flex justify-center">
          <QuickUndoTimer
            contributionId={recentContribution.id}
            contributionContent={recentContribution.content}
            onUndo={handleQuickUndo}
            onExpire={handleQuickUndoExpire}
            duration={3}
          />
        </div>
      )}
      
      {/* Tsk-Tsk-Tsk Undo Button */}
      {canUndo && !recentContribution && (
        <div className="flex justify-center">
          <TskTskTskButton
            storyId={storyId}
            canUndo={canUndo}
            lastContribution={lastContributionForUndo}
            onUndo={handleUndo}
            disabled={isSubmitting}
          />
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            {!disabled ? 'Your turn to contribute' : 'Waiting for your turn...'}
          </label>
          {getInputComponent()}
          <p className="mt-1 text-sm text-gray-500">{getHelperText()}</p>
        </div>
        
        {error && (
          <div className="p-2 text-sm text-red-700 bg-red-100 rounded-md">
            {error}
          </div>
        )}
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={disabled || isSubmitting || !content.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isSubmitting ? 'Submitting...' : 'Submit'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ContributionForm;
