import React from 'react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Zap, RotateCcw, Crown } from 'lucide-react';

export interface SpecialActionIndicatorProps {
  specialType: 'gotcha' | 'reverse' | 'golden';
  tokenCost?: number;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  showCost?: boolean;
  className?: string;
}

const specialActionConfig = {
  gotcha: {
    icon: Zap,
    label: 'Gotcha Word',
    color: 'text-yellow-600 dark:text-yellow-400',
    bgColor: 'bg-yellow-50 dark:bg-yellow-950',
    borderColor: 'border-yellow-200 dark:border-yellow-800',
    gradient: 'from-yellow-400 to-yellow-600',
    description: 'Added a surprising twist!',
  },
  reverse: {
    icon: RotateCcw,
    label: 'Reverse Move',
    color: 'text-purple-600 dark:text-purple-400',
    bgColor: 'bg-purple-50 dark:bg-purple-950',
    borderColor: 'border-purple-200 dark:border-purple-800',
    gradient: 'from-purple-400 to-purple-600',
    description: 'Reversed the story direction!',
  },
  golden: {
    icon: Crown,
    label: 'Golden Contribution',
    color: 'text-amber-600 dark:text-amber-400',
    bgColor: 'bg-amber-50 dark:bg-amber-950',
    borderColor: 'border-amber-200 dark:border-amber-800',
    gradient: 'from-amber-400 to-amber-600',
    description: 'A premium contribution!',
  },
};

export const SpecialActionIndicator: React.FC<SpecialActionIndicatorProps> = ({
  specialType,
  tokenCost,
  size = 'md',
  showLabel = true,
  showCost = true,
  className,
}) => {
  const config = specialActionConfig[specialType];
  const IconComponent = config.icon;

  const sizeClasses = {
    sm: {
      container: 'px-2 py-1 text-xs',
      icon: 'w-3 h-3',
      badge: 'text-xs',
    },
    md: {
      container: 'px-3 py-1.5 text-sm',
      icon: 'w-4 h-4',
      badge: 'text-xs',
    },
    lg: {
      container: 'px-4 py-2 text-base',
      icon: 'w-5 h-5',
      badge: 'text-sm',
    },
  };

  const sizes = sizeClasses[size];

  return (
    <div
      className={cn(
        'inline-flex items-center gap-2 rounded-full border-2 font-medium',
        'shadow-sm hover:shadow-md transition-all duration-200',
        'animate-in fade-in-0 zoom-in-95 duration-300',
        config.bgColor,
        config.borderColor,
        config.color,
        sizes.container,
        className
      )}
      title={config.description}
    >
      {/* Gradient background for extra visual appeal */}
      <div 
        className={cn(
          'absolute inset-0 rounded-full bg-gradient-to-r opacity-10',
          config.gradient
        )}
      />
      
      {/* Icon */}
      <IconComponent className={cn(sizes.icon, 'relative z-10')} />
      
      {/* Label */}
      {showLabel && (
        <span className="font-semibold relative z-10">
          {config.label}
        </span>
      )}
      
      {/* Token cost badge */}
      {showCost && tokenCost && tokenCost > 0 && (
        <Badge 
          variant="secondary" 
          className={cn(
            'relative z-10 font-bold',
            sizes.badge
          )}
        >
          {tokenCost} {tokenCost === 1 ? 'token' : 'tokens'}
        </Badge>
      )}
    </div>
  );
};

export default SpecialActionIndicator; 