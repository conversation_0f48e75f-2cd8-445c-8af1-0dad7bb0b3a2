import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { formatDistanceToNow, format } from 'date-fns';
import { Zap, RotateCcw, Crown, Heart, Clock, User, Crown as CrownIcon } from 'lucide-react';
import SpecialActionIndicator from './SpecialActionIndicator';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export interface MessageBubbleProps {
  id: string;
  content: string;
  author: {
    id: string;
    username: string;
    avatar_url?: string;
    role?: 'creator' | 'contributor' | 'viewer';
    isOnline?: boolean;
  };
  timestamp: Date;
  isCurrentUser: boolean;
  position: number;
  specialType?: 'gotcha' | 'reverse' | 'golden' | null;
  tokenCost?: number;
  reactions?: Array<{
    type: 'heart' | 'star' | 'laugh';
    count: number;
    users: string[];
  }>;
  showFullTimestamp?: boolean;
  onAuthorClick?: (authorId: string) => void;
  className?: string;
}

const specialTypeConfig = {
  gotcha: {
    icon: Zap,
    label: 'Gotcha Word',
    bgColor: 'bg-yellow-50 dark:bg-yellow-950',
    borderColor: 'border-yellow-200 dark:border-yellow-800',
    iconColor: 'text-yellow-600 dark:text-yellow-400',
  },
  reverse: {
    icon: RotateCcw,
    label: 'Reverse Move',
    bgColor: 'bg-purple-50 dark:bg-purple-950',
    borderColor: 'border-purple-200 dark:border-purple-800',
    iconColor: 'text-purple-600 dark:text-purple-400',
  },
  golden: {
    icon: Crown,
    label: 'Golden Contribution',
    bgColor: 'bg-amber-50 dark:bg-amber-950',
    borderColor: 'border-amber-200 dark:border-amber-800',
    iconColor: 'text-amber-600 dark:text-amber-400',
  },
};

const getRoleIcon = (role?: string) => {
  switch (role) {
    case 'creator':
      return CrownIcon;
    case 'contributor':
      return User;
    default:
      return null;
  }
};

const getRoleColor = (role?: string) => {
  switch (role) {
    case 'creator':
      return 'text-amber-600 dark:text-amber-400';
    case 'contributor':
      return 'text-blue-600 dark:text-blue-400';
    default:
      return 'text-muted-foreground';
  }
};

export const MessageBubble: React.FC<MessageBubbleProps> = ({
  id,
  content,
  author,
  timestamp,
  isCurrentUser,
  position,
  specialType,
  tokenCost,
  reactions = [],
  showFullTimestamp = false,
  onAuthorClick,
  className,
}) => {
  const [showFullTime, setShowFullTime] = useState(showFullTimestamp);
  const specialConfig = specialType ? specialTypeConfig[specialType] : null;
  const RoleIcon = getRoleIcon(author.role);

  // Enhanced timestamp formatting
  const getTimestampDisplay = () => {
    if (showFullTime) {
      return format(timestamp, 'MMM d, yyyy \'at\' h:mm a');
    }
    
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else {
      return formatDistanceToNow(timestamp, { addSuffix: true });
    }
  };

  const handleAuthorClick = () => {
    if (onAuthorClick && !isCurrentUser) {
      onAuthorClick(author.id);
    }
  };

  return (
    <TooltipProvider>
      <div
        className={cn(
          'flex gap-3 group transition-all duration-200 hover:bg-muted/30 -mx-4 px-4 py-2 rounded-lg',
          isCurrentUser ? 'flex-row-reverse' : 'flex-row',
          className
        )}
      >
        {/* Enhanced Avatar with Status Indicator */}
        {!isCurrentUser && (
          <Tooltip>
            <TooltipTrigger asChild>
              <div 
                className="relative flex-shrink-0 cursor-pointer transition-transform hover:scale-105"
                onClick={handleAuthorClick}
              >
                <Avatar className="w-10 h-10 ring-2 ring-background shadow-sm">
                  <AvatarImage 
                    src={author.avatar_url} 
                    alt={author.username}
                    className="object-cover"
                  />
                  <AvatarFallback className="text-sm font-semibold bg-gradient-to-br from-primary/20 to-primary/40">
                    {author.username.slice(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                
                {/* Online Status Indicator */}
                {author.isOnline !== undefined && (
                  <div
                    className={cn(
                      'absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 rounded-full border-2 border-background',
                      author.isOnline 
                        ? 'bg-green-500 shadow-lg shadow-green-500/30' 
                        : 'bg-gray-400 dark:bg-gray-600'
                    )}
                  />
                )}
                
                {/* Role Indicator */}
                {RoleIcon && (
                  <div
                    className={cn(
                      'absolute -top-1 -left-1 w-5 h-5 rounded-full bg-background border-2 border-background flex items-center justify-center',
                      getRoleColor(author.role)
                    )}
                  >
                    <RoleIcon className="w-2.5 h-2.5" />
                  </div>
                )}
              </div>
            </TooltipTrigger>
            <TooltipContent side="left" align="start">
              <div className="text-center">
                <p className="font-semibold">{author.username}</p>
                {author.role && (
                  <p className="text-xs text-muted-foreground capitalize">
                    {author.role}
                  </p>
                )}
                <p className="text-xs text-muted-foreground">
                  {author.isOnline ? 'Online' : 'Offline'}
                </p>
              </div>
            </TooltipContent>
          </Tooltip>
        )}

        {/* Message Content */}
        <div className={cn(
          'flex flex-col max-w-[75%] sm:max-w-[60%]',
          isCurrentUser ? 'items-end' : 'items-start'
        )}>
          {/* Enhanced Author and Position Info */}
          {!isCurrentUser && (
            <div className="flex items-center gap-2 mb-1">
              <button
                onClick={handleAuthorClick}
                className={cn(
                  'text-sm font-semibold hover:underline transition-colors',
                  getRoleColor(author.role),
                  'hover:text-primary'
                )}
              >
                {author.username}
              </button>
              
              {author.role && (
                <Badge 
                  variant="outline" 
                  className={cn(
                    'text-xs px-1.5 py-0.5',
                    getRoleColor(author.role)
                  )}
                >
                  {author.role}
                </Badge>
              )}
              
              <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                #{position}
              </Badge>
            </div>
          )}

          {/* Message Bubble */}
          <div
            className={cn(
              'relative px-4 py-2 rounded-2xl max-w-full break-words',
              'shadow-sm border transition-all duration-200',
              isCurrentUser
                ? 'bg-primary text-primary-foreground border-primary/20'
                : 'bg-background border-border hover:shadow-md',
              specialConfig && [
                specialConfig.bgColor,
                specialConfig.borderColor,
                'border-2'
              ]
            )}
          >
            {/* Special Action Indicator */}
            {specialType && (
              <div className="mb-2">
                <SpecialActionIndicator
                  specialType={specialType}
                  tokenCost={tokenCost}
                  size="sm"
                  showLabel={true}
                  showCost={true}
                />
              </div>
            )}

            {/* Message Content */}
            <p className={cn(
              'text-sm leading-relaxed',
              specialConfig ? 'text-foreground' : ''
            )}>
              {content}
            </p>

            {/* Message Tail */}
            <div
              className={cn(
                'absolute w-3 h-3 transform rotate-45',
                'border-l border-b',
                isCurrentUser
                  ? 'right-[-6px] bottom-3 bg-primary border-primary/20'
                  : 'left-[-6px] bottom-3 bg-background border-border'
              )}
            />
          </div>

          {/* Reactions */}
          {reactions.length > 0 && (
            <div className="flex items-center gap-1 mt-1">
              {reactions.map((reaction, index) => (
                <Tooltip key={index}>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 px-2 py-1 text-xs hover:bg-muted/80 rounded-full transition-colors"
                    >
                      {reaction.type === 'heart' && <Heart className="w-3 h-3 text-red-500 mr-1" />}
                      <span>{reaction.count}</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{reaction.users.slice(0, 3).join(', ')}{reaction.users.length > 3 ? `... and ${reaction.users.length - 3} more` : ''}</p>
                  </TooltipContent>
                </Tooltip>
              ))}
            </div>
          )}

          {/* Enhanced Timestamp and Position for Current User */}
          <div className={cn(
            'flex items-center gap-2 mt-1 text-xs',
            isCurrentUser ? 'flex-row-reverse text-right' : 'flex-row text-left'
          )}>
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  onClick={() => setShowFullTime(!showFullTime)}
                  className={cn(
                    'text-muted-foreground hover:text-foreground transition-colors',
                    'flex items-center gap-1'
                  )}
                >
                  <Clock className="w-3 h-3" />
                  <span>{getTimestampDisplay()}</span>
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{format(timestamp, 'EEEE, MMMM d, yyyy \'at\' h:mm:ss a')}</p>
              </TooltipContent>
            </Tooltip>
            
            {isCurrentUser && (
              <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                #{position}
              </Badge>
            )}
          </div>
        </div>

        {/* Enhanced Current User Avatar */}
        {isCurrentUser && (
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="relative flex-shrink-0">
                <Avatar className="w-10 h-10 ring-2 ring-primary/20 shadow-sm">
                  <AvatarImage 
                    src={author.avatar_url} 
                    alt={author.username}
                    className="object-cover"
                  />
                  <AvatarFallback className="text-sm font-semibold bg-gradient-to-br from-primary/20 to-primary/40">
                    {author.username.slice(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                
                {/* Online Status Indicator */}
                <div className="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 rounded-full border-2 border-background bg-green-500 shadow-lg shadow-green-500/30" />
                
                {/* Role Indicator */}
                {RoleIcon && (
                  <div
                    className={cn(
                      'absolute -top-1 -left-1 w-5 h-5 rounded-full bg-background border-2 border-background flex items-center justify-center',
                      getRoleColor(author.role)
                    )}
                  >
                    <RoleIcon className="w-2.5 h-2.5" />
                  </div>
                )}
              </div>
            </TooltipTrigger>
            <TooltipContent side="right" align="start">
              <div className="text-center">
                <p className="font-semibold">{author.username} (You)</p>
                {author.role && (
                  <p className="text-xs text-muted-foreground capitalize">
                    {author.role}
                  </p>
                )}
                <p className="text-xs text-muted-foreground">Online</p>
              </div>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </TooltipProvider>
  );
};

export default MessageBubble;