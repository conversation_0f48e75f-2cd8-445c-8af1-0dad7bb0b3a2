import React, { createContext, useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { AuthContextType, User, SystemMaintenanceState } from "./types";
import { useSystemMaintenance } from "@/hooks/use-system-maintenance";
import { useUserProfile } from "@/hooks/use-user-profile";
import {
  signInWithGoogle,
  signInWithEmail,
  signUpWithEmail,
  signOut as firebaseSignOut,
  resetPassword as firebaseResetPassword,
  onAuthStateChange
} from "@/services/firebaseAuth";
import { usePostHogAuth } from "@/hooks/usePostHog";
import { trackUserEvent } from "@/lib/posthog";

// Create the context with a default value of undefined
export const FirebaseAuthContext = createContext<AuthContextType | undefined>(
  undefined,
);

// Create the AuthProvider component
interface FirebaseAuthProviderProps {
  children: React.ReactNode;
}

export const FirebaseAuthProvider: React.FC<FirebaseAuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const navigate = useNavigate();

  // Use our custom hooks
  const { systemMaintenance, setSystemMaintenance, loadMaintenanceSettings } =
    useSystemMaintenance();
  const {
    user: profileUser,
    setUser: setProfileUser,
    loadUserProfile,
    updateProfile,
    updateAchievements,
    addBadge,
    isAdmin,
  } = useUserProfile();

  // PostHog integration for user tracking
  usePostHogAuth(profileUser);

  // Listen for Firebase auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChange((firebaseUser) => {
      if (firebaseUser) {
        // Set initial user data from Firebase, will be updated by loadUserProfile
        setUser(firebaseUser);
        setProfileUser(firebaseUser);
        setIsAuthenticated(true);
        loadUserProfile(firebaseUser.id);

        // Track login event
        trackUserEvent.userLoggedIn(firebaseUser.id, 'firebase_google', {
          user_email: firebaseUser.email,
          login_timestamp: new Date().toISOString(),
        });
      } else {
        setUser(null);
        setProfileUser(null);
        setIsAuthenticated(false);
      }
      setIsLoading(false);
    });

    return unsubscribe;
  }, [loadUserProfile, setProfileUser]);

  useEffect(() => {
    loadMaintenanceSettings();
  }, [loadMaintenanceSettings]);

  // Auth actions
  const handleSignIn = useCallback(async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const user = await signInWithEmail(email, password);
      setUser(user);
      setProfileUser(user);
      setIsAuthenticated(true);
      loadUserProfile(user.id);
    } catch (err: any) {
      setError(err);
      setIsAuthenticated(false);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [loadUserProfile, setProfileUser]);

  const handleSignUp = useCallback(async (email: string, password: string, username: string) => {
    setIsLoading(true);
    try {
      const user = await signUpWithEmail(email, password, username);
      setUser(user);
      setProfileUser(user);
      setIsAuthenticated(true);
      await updateProfile({ id: user.id, username });
      loadUserProfile(user.id);

      // Track registration event
      trackUserEvent.userRegistered(user.id, 'firebase_email', {
        user_email: user.email,
        username: username,
        registration_timestamp: new Date().toISOString(),
      });
    } catch (err: any) {
      setError(err);
      setIsAuthenticated(false);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [updateProfile, loadUserProfile, setProfileUser]);

  const handleRegister = useCallback(async (username: string, email: string, password: string): Promise<boolean> => {
    try {
      await handleSignUp(email, password, username);
      return true;
    } catch (err: any) {
      setError(err);
      return false;
    }
  }, [handleSignUp]);

  const handleSignOut = useCallback(async () => {
    setIsLoading(true);
    try {
      // Track logout before signing out
      if (profileUser) {
        trackUserEvent.userLoggedOut({
          logout_timestamp: new Date().toISOString(),
          session_duration: Date.now() - (new Date(profileUser.createdAt).getTime()),
        });
      }
      await firebaseSignOut();
      setUser(null);
      setProfileUser(null);
      setIsAuthenticated(false);
    } catch (err: any) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }, [profileUser, setProfileUser]);

  const handleResetPassword = useCallback(async (email: string) => {
    setIsLoading(true);
    try {
      await firebaseResetPassword(email);
    } catch (err: any) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleGoogleLogin = useCallback(async () => {
    setIsLoading(true);
    try {
      const user = await signInWithGoogle();
      setUser(user);
      setProfileUser(user);
      setIsAuthenticated(true);
      loadUserProfile(user.id);
      return { user };
    } catch (err: any) {
      setError(err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [loadUserProfile, setProfileUser]);

  const value: AuthContextType = {
    user: profileUser, // Use the profile user that includes terms acceptance
    session: null, // Firebase doesn't use sessions like Supabase
    isAuthenticated,
    loading: isLoading,
    error,
    signIn: handleSignIn,
    signUp: handleSignUp,
    signOut: handleSignOut,
    resetPassword: handleResetPassword,
    updateProfile,
    handleAuthStateChange: async () => {}, // Not needed for Firebase
    handleError: setError,
    systemMaintenance,
    setSystemMaintenance,
    isAdmin,
    login: handleSignIn,
    loginWithGoogle: handleGoogleLogin,
    logout: handleSignOut,
    register: handleRegister,
    sendOTP: async () => false,
    verifyOTP: async () => false,
    resendOTP: async () => false,
    updateAchievements,
    addBadge,
    mfaResolver: null,
    setMFAResolver: () => {},
    isMFAEnabled: () => false,
    completeMFASetup: async () => false,
  };

  return <FirebaseAuthContext.Provider value={value}>{children}</FirebaseAuthContext.Provider>;
};