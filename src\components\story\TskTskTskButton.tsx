import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Undo2, Co<PERSON>, AlertTriangle } from "lucide-react";
import { useAuth } from "@/contexts/auth";
import { useSubscription } from "@/hooks/use-subscription";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";

interface TskTskTskButtonProps {
  storyId: string;
  canUndo: boolean; // Whether there's a word to undo
  lastContribution?: {
    id: string;
    content: string;
    author: {
      id: string;
      username: string;
    };
    created_at: string;
  };
  onUndo: (contributionId: string) => Promise<void>;
  disabled?: boolean;
}

const UNDO_COST = 5; // Credits required to use undo

export const TskTskTskButton: React.FC<TskTskTskButtonProps> = ({
  storyId,
  canUndo,
  lastContribution,
  onUndo,
  disabled = false,
}) => {
  const { user } = useAuth();
  const { credits } = useSubscription();
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [isUndoing, setIsUndoing] = useState(false);

  const hasEnoughCredits = credits >= UNDO_COST;
  const canUseUndo = canUndo && hasEnoughCredits && !disabled && lastContribution;

  const handleUndoClick = () => {
    if (!canUseUndo) return;
    setShowConfirmDialog(true);
  };

  const handleConfirmUndo = async () => {
    if (!lastContribution) return;
    
    try {
      setIsUndoing(true);
      await onUndo(lastContribution.id);
      setShowConfirmDialog(false);
    } catch (error) {
      console.error("Failed to undo contribution:", error);
      // Error handling could be improved with toast notifications
    } finally {
      setIsUndoing(false);
    }
  };

  const getButtonText = () => {
    if (!canUndo) return "Nothing to undo";
    if (!hasEnoughCredits) return `Need ${UNDO_COST} credits`;
    return "Tsk-Tsk-Tsk!";
  };

  const getTooltipText = () => {
    if (!user) return "Sign in to use undo";
    if (!canUndo) return "No recent contributions to undo";
    if (!hasEnoughCredits) return `You need ${UNDO_COST} credits to undo the last word`;
    return `Spend ${UNDO_COST} credits to undo "${lastContribution?.content}"`;
  };

  return (
    <>
      <div className="relative group">
        <Button
          onClick={handleUndoClick}
          disabled={!canUseUndo}
          variant={canUseUndo ? "destructive" : "outline"}
          size="sm"
          className={`
            transition-all duration-200 
            ${canUseUndo 
              ? "bg-red-600 hover:bg-red-700 text-white shadow-md hover:shadow-lg" 
              : "bg-gray-100 text-gray-400 cursor-not-allowed"
            }
          `}
        >
          <Undo2 className="w-4 h-4 mr-2" />
          {getButtonText()}
          {canUseUndo && (
            <Badge variant="secondary" className="ml-2 bg-yellow-100 text-yellow-800">
              <Coins className="w-3 h-3 mr-1" />
              {UNDO_COST}
            </Badge>
          )}
        </Button>
        
        {/* Tooltip */}
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
          {getTooltipText()}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-red-500" />
              <span>Tsk-Tsk-Tsk Undo</span>
            </DialogTitle>
            <DialogDescription className="space-y-3">
              <p>
                Are you sure you want to undo the last contribution? This action will:
              </p>
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-800">
                  <strong>Remove:</strong> "{lastContribution?.content}" by {lastContribution?.author.username}
                </p>
                <p className="text-sm text-red-700 mt-1">
                  <strong>Cost:</strong> {UNDO_COST} credits (you have {credits})
                </p>
              </div>
              <p className="text-sm text-gray-600">
                This action cannot be undone and will permanently remove the contribution from the story.
              </p>
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex space-x-2">
            <Button
              variant="outline"
              onClick={() => setShowConfirmDialog(false)}
              disabled={isUndoing}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirmUndo}
              disabled={isUndoing}
              className="bg-red-600 hover:bg-red-700"
            >
              {isUndoing ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Undoing...
                </>
              ) : (
                <>
                  <Coins className="w-4 h-4 mr-2" />
                  Spend {UNDO_COST} Credits & Undo
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}; 