import React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";

const TermsAndConditions: React.FC = () => {
  return (
    <ScrollArea className="h-[300px] w-full rounded-md border p-4 bg-white">
      <div className="pr-4 text-sm">
        <h2 className="text-lg font-semibold mb-4">Terms and Conditions</h2>

        <h3 className="font-semibold mt-4 mb-2">1. Acceptance of Terms</h3>
        <p className="mb-2">
          By accessing or using this application, you agree to be bound by these
          Terms and Conditions. If you do not agree to all of these terms, you
          may not access or use this application.
        </p>

        <h3 className="font-semibold mt-4 mb-2">2. User Content</h3>
        <p className="mb-2">
          We do not claim ownership of any content, data, or materials
          ("Content") that you submit, post, or display on the application. You
          are solely responsible for all Content that you upload, post, email,
          transmit, or otherwise make available via the application.
        </p>
        <p className="mb-2">
          You represent and warrant that you own or have the necessary licenses,
          rights, consents, and permissions to use and authorize us to use all
          intellectual property rights in and to any Content that you submit.
        </p>

        <h3 className="font-semibold mt-4 mb-2">
          3. No Responsibility for Content
        </h3>
        <p className="mb-2">
          We do not control and are not responsible for the Content posted via
          the application, and therefore we do not guarantee the accuracy,
          integrity, or quality of such Content. You understand that by using
          the application, you may be exposed to Content that is offensive,
          harmful, inaccurate, or otherwise inappropriate.
        </p>

        <h3 className="font-semibold mt-4 mb-2">
          4. Copyright and Intellectual Property
        </h3>
        <p className="mb-2">
          Users are solely responsible for ensuring that their use of the
          application does not infringe upon the intellectual property rights of
          others. We do not monitor or control the Content uploaded or shared
          through the application and take no responsibility for any
          infringement of copyright or other intellectual property rights that
          may occur.
        </p>

        <h3 className="font-semibold mt-4 mb-2">5. No Warranty</h3>
        <p className="mb-2">
          The application is provided on an "AS IS" and "AS AVAILABLE" basis. We
          make no warranty that the application will meet your requirements or
          be available on an uninterrupted, secure, or error-free basis.
        </p>

        <h3 className="font-semibold mt-4 mb-2">6. Service Availability</h3>
        <p className="mb-2">
          While we strive to keep the application operational, we offer no
          guarantees of service availability. The application may be subject to
          limitations, delays, and other problems inherent in the use of the
          internet and electronic communications. We are not responsible for any
          delays, delivery failures, or other damages resulting from such
          problems.
        </p>

        <h3 className="font-semibold mt-4 mb-2">7. Limitation of Liability</h3>
        <p className="mb-2">
          In no event shall we be liable for any indirect, incidental, special,
          consequential, or punitive damages, including without limitation, loss
          of profits, data, use, goodwill, or other intangible losses, resulting
          from your access to or use of or inability to access or use the
          application.
        </p>

        <h3 className="font-semibold mt-4 mb-2">8. Changes to Terms</h3>
        <p className="mb-2">
          We reserve the right to modify or replace these Terms at any time. It
          is your responsibility to check these Terms periodically for changes.
        </p>
      </div>
    </ScrollArea>
  );
};

export default TermsAndConditions;
