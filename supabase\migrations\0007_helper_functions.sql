-- supabase/migrations/0004_helper_functions.sql

-- Helper functions for Word By Word Story

CREATE OR REPLACE FUNCTION public.get_story_vote_counts(p_story_id UUID)
RETURNS TABLE(upvote_count BIGINT, downvote_count BIGINT) AS $$
BEGIN
  RETURN QUERY
  SELECT
    COUNT(*) FILTER (WHERE vote_type = 'upvote') AS upvote_count,
    COUNT(*) FILTER (WHERE vote_type = 'downvote') AS downvote_count
  FROM
    public.votes
  WHERE
    story_id = p_story_id;
END;
$$ LANGUAGE plpgsql STABLE;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_story_vote_counts(UUID) TO authenticated;

-- Optionally, grant execute to anon if public vote counts are desired (ensure R<PERSON> is in place)
-- GRANT EXECUTE ON FUNCTION public.get_story_vote_counts(UUID) TO anon;

COMMENT ON FUNCTION public.get_story_vote_counts(UUID) IS 
'Returns the total upvote and downvote counts for a given story ID. Accessible by authenticated users and anonymous users.'; 