import React from 'react';
import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { formatDistanceToNow } from 'date-fns';

export interface TypingUser {
  id: string;
  username: string;
  avatar_url?: string;
  lastSeen?: Date;
  isOnline?: boolean;
}

export interface TypingIndicatorProps {
  users: TypingUser[];
  showPresence?: boolean;
  className?: string;
}

export const TypingIndicator: React.FC<TypingIndicatorProps> = ({
  users,
  showPresence = true,
  className,
}) => {
  if (!users || users.length === 0) {
    return null;
  }

  // Limit display to first 3 users
  const displayUsers = users.slice(0, 3);
  const remainingCount = users.length - 3;

  const getTypingText = () => {
    if (users.length === 1) {
      return `${users[0].username} is typing...`;
    } else if (users.length === 2) {
      return `${users[0].username} and ${users[1].username} are typing...`;
    } else if (users.length === 3) {
      return `${users[0].username}, ${users[1].username}, and ${users[2].username} are typing...`;
    } else {
      return `${users[0].username}, ${users[1].username}, and ${remainingCount + 1} others are typing...`;
    }
  };

  const getLastSeenText = (user: TypingUser) => {
    if (user.isOnline === false && user.lastSeen) {
      return `Last seen ${formatDistanceToNow(user.lastSeen, { addSuffix: true })}`;
    }
    return user.isOnline ? 'Online' : 'Offline';
  };

  return (
    <div 
      className={cn(
        'flex items-center gap-3 animate-in fade-in-0 slide-in-from-left-2 duration-300',
        className
      )}
    >
      {/* Enhanced User Avatars with Presence */}
      <div className="flex -space-x-2">
        {displayUsers.map((user, index) => (
          <div key={user.id} className="relative group">
            <Avatar 
              className={cn(
                'w-7 h-7 border-2 border-background shadow-sm transition-all duration-200',
                'animate-pulse hover:scale-110 cursor-pointer',
                user.isOnline !== false && 'ring-2 ring-green-500/30'
              )}
              style={{ 
                animationDelay: `${index * 100}ms`,
                zIndex: displayUsers.length - index 
              }}
            >
              <AvatarImage src={user.avatar_url} alt={user.username} />
              <AvatarFallback className="text-xs bg-muted">
                {user.username.slice(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            
            {/* Enhanced Online Status Indicator */}
            <div
              className={cn(
                'absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 rounded-full border border-background',
                user.isOnline !== false 
                  ? 'bg-green-500 animate-pulse' 
                  : 'bg-gray-400 dark:bg-gray-600'
              )}
            />
            
            {/* Tooltip with enhanced presence info */}
            {showPresence && (
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/80 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                <div className="text-center">
                  <p className="font-semibold">{user.username}</p>
                  <p className="text-xs opacity-75">{getLastSeenText(user)}</p>
                </div>
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-black/80" />
              </div>
            )}
          </div>
        ))}
        
        {remainingCount > 0 && (
          <div className="w-7 h-7 rounded-full border-2 border-background bg-muted flex items-center justify-center text-xs font-semibold text-muted-foreground shadow-sm animate-pulse">
            +{remainingCount}
          </div>
        )}
      </div>

      {/* Enhanced Typing Message Bubble */}
      <div className="relative">
        <div className="bg-muted/80 backdrop-blur-sm border rounded-2xl px-4 py-2 shadow-sm hover:shadow-md transition-shadow duration-200">
          <div className="flex items-center gap-2">
            <p className="text-sm text-muted-foreground">
              {getTypingText()}
            </p>
            
            {/* Enhanced Animated Dots with Gradient */}
            <div className="flex gap-1">
              <div 
                className="w-1.5 h-1.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-bounce opacity-70" 
                style={{ animationDelay: '0ms', animationDuration: '1s' }} 
              />
              <div 
                className="w-1.5 h-1.5 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-bounce opacity-70" 
                style={{ animationDelay: '200ms', animationDuration: '1s' }} 
              />
              <div 
                className="w-1.5 h-1.5 bg-gradient-to-r from-pink-500 to-red-500 rounded-full animate-bounce opacity-70" 
                style={{ animationDelay: '400ms', animationDuration: '1s' }} 
              />
            </div>
          </div>
        </div>
        
        {/* Enhanced Message Tail with Gradient */}
        <div className="absolute w-3 h-3 bg-muted/80 border-l border-b border-border left-[-6px] bottom-3 transform rotate-45 backdrop-blur-sm" />
      </div>
    </div>
  );
};

export default TypingIndicator;