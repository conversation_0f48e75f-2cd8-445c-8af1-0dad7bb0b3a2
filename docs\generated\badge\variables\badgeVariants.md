[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [badge](../README.md) / badgeVariants

# Variable: badgeVariants()

> `const` **badgeVariants**: (`props?`) => `string`

Defined in: [src/components/ui/badge.tsx:6](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/badge.tsx#L6)

## Parameters

### props?

`ConfigVariants`\<\{ `variant`: \{ `default`: `string`; `destructive`: `string`; `outline`: `string`; `secondary`: `string`; \}; \}\> & `ClassProp`

## Returns

`string`
