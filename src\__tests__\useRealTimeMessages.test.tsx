import { renderHook, act, waitFor } from '@testing-library/react';
import { useRealTimeMessages } from '@/hooks/useRealTimeMessages';
import React from 'react';
import { vi, beforeEach, describe, it, expect } from 'vitest';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { AuthProvider } from '@/contexts/auth/AuthContext';
import { BrowserRouter } from 'react-router-dom';

// Mock all dependencies first
vi.mock('@/lib/supabase', () => {
  return {
    supabase: {
      from: vi.fn(() => ({
        upsert: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn(() => Promise.resolve({ data: { id: 'session-id' }, error: null }))
          }))
        })),
        update: vi.fn(() => ({
          match: vi.fn(() => Promise.resolve({ error: null }))
        })),
      })),
      auth: {
        getSession: vi.fn().mockResolvedValue({
          data: { session: {
            user: {
              id: 'test-user-id',
              email: '<EMAIL>',
              username: 'testuser',
              profilePicture: 'https://example.com/avatar.jpg',
            }
          }},
          error: null,
        }),
        onAuthStateChange: vi.fn().mockImplementation((callback) => {
          if (callback) {
            setTimeout(() => callback('SIGNED_IN', { 
              user: {
                id: 'test-user-id',
                email: '<EMAIL>',
                username: 'testuser',
                profilePicture: 'https://example.com/avatar.jpg',
              }
            }), 0);
          }
          return { data: { subscription: { unsubscribe: vi.fn() } } };
        }),
      },
      realtime: {
        subscribeToStory: vi.fn()
      }
    },
  };
});

vi.mock('@/services/realTimeService', () => ({
  realTimeService: {
    subscribeToStory: vi.fn(),
    registerUserSession: vi.fn(),
    updateUserStatus: vi.fn(),
    broadcastContribution: vi.fn(),
  },
}));

vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn(),
  }),
}));

// Mock react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
  };
});

// Test wrapper with all required providers
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <BrowserRouter>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </QueryClientProvider>
    </BrowserRouter>
  );
};

// Simple test without complex providers
describe('useRealTimeMessages', () => {
  const storyId = 'test-story-id';

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with default values', () => {
    const { result } = renderHook(() => 
      useRealTimeMessages({ storyId, enabled: false }),
      { wrapper: TestWrapper }
    );

    expect(result.current.messages).toEqual([]);
    expect(result.current.typingUsers).toEqual([]);
    expect(result.current.isConnected).toBe(false);
    expect(result.current.connectionState).toBe('disconnected');
  });

  it('should provide required functions', () => {
    const { result } = renderHook(() => 
      useRealTimeMessages({ storyId, enabled: false }),
      { wrapper: TestWrapper }
    );

    expect(typeof result.current.sendMessage).toBe('function');
    expect(typeof result.current.startTyping).toBe('function');
    expect(typeof result.current.stopTyping).toBe('function');
    expect(typeof result.current.addOptimisticMessage).toBe('function');
    expect(typeof result.current.removeOptimisticMessage).toBe('function');
    expect(typeof result.current.reconnect).toBe('function');
  });

  it('should handle optimistic updates correctly', () => {
    const { result } = renderHook(() => 
      useRealTimeMessages({ storyId, enabled: false }),
      { wrapper: TestWrapper }
    );

    let optimisticId: string;

    // Add optimistic message
    act(() => {
      optimisticId = result.current.addOptimisticMessage({
        content: 'Optimistic message',
        author: {
          id: 'test-user-id',
          username: 'testuser',
          avatar_url: 'https://example.com/avatar.jpg',
        },
        position: 1,
      });
    });

    expect(result.current.messages).toHaveLength(1);
    expect(result.current.messages[0].content).toBe('Optimistic message');
    expect(result.current.messages[0].isOptimistic).toBe(true);

    // Remove optimistic message
    act(() => {
      result.current.removeOptimisticMessage(optimisticId);
    });

    expect(result.current.messages).toHaveLength(0);
  });
}); 