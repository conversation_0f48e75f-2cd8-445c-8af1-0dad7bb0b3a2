import React, { createContext, useContext, useEffect, useState } from "react";
import { useAuth } from "./auth";
import { OnboardingWizard } from "@/components/onboarding/OnboardingWizard";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { useNavigate, useLocation } from "react-router-dom";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";

interface OnboardingContextType {
  hasCompletedOnboarding: boolean;
  showOnboarding: () => void;
  markOnboardingComplete: () => void;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(
  undefined,
);

export const useOnboarding = () => {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error("useOnboarding must be used within an OnboardingProvider");
  }
  return context;
};

interface OnboardingProviderProps {
  children: React.ReactNode;
}

export const OnboardingProvider: React.FC<OnboardingProviderProps> = ({
  children,
}) => {
  const { user, isAuthenticated } = useAuth();
  const [hasCompletedOnboarding, setHasCompletedOnboarding] =
    useState<boolean>(true);
  const [showOnboardingDialog, setShowOnboardingDialog] =
    useState<boolean>(false);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (isAuthenticated && user) {
      // Check if the user has completed onboarding
      const hasCompleted = localStorage.getItem(
        `onboarding-complete-${user.id}`,
      );

      if (!hasCompleted) {
        setHasCompletedOnboarding(false);
        setShowOnboardingDialog(true);
      }
    }
  }, [isAuthenticated, user]);

  const markOnboardingComplete = () => {
    if (user) {
      localStorage.setItem(`onboarding-complete-${user.id}`, "true");
      setHasCompletedOnboarding(true);
      setShowOnboardingDialog(false);

      // Redirect to dashboard if not already there
      if (location.pathname !== "/dashboard") {
        navigate("/dashboard");
      }
    }
  };

  const showOnboarding = () => {
    setShowOnboardingDialog(true);
  };

  // Use this function to close the dialog without completing onboarding
  const handleOpenChange = (open: boolean) => {
    setShowOnboardingDialog(open);

    // If dialog is closed without completing onboarding, redirect to dashboard
    if (!open && !hasCompletedOnboarding && user) {
      // Use a small timeout to prevent navigation issues
      setTimeout(() => {
        navigate("/dashboard");
      }, 100);
    }
  };

  return (
    <OnboardingContext.Provider
      value={{
        hasCompletedOnboarding,
        showOnboarding,
        markOnboardingComplete,
      }}
    >
      {children}

      <Dialog open={showOnboardingDialog} onOpenChange={handleOpenChange}>
        <DialogContent className="sm:max-w-[600px] p-0">
          <DialogTitle>
            <VisuallyHidden>Onboarding</VisuallyHidden>
          </DialogTitle>
          <DialogDescription>
            <VisuallyHidden>
              Complete the onboarding process to get started
            </VisuallyHidden>
          </DialogDescription>
          <OnboardingWizard onComplete={markOnboardingComplete} />
        </DialogContent>
      </Dialog>
    </OnboardingContext.Provider>
  );
};
