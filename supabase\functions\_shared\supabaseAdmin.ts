import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.43.4';

// These environment variables should be set in your Supabase Edge Function settings
const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

if (!supabaseUrl) {
  throw new Error('SUPABASE_URL is not set in environment variables.');
}
if (!supabaseServiceRoleKey) {
  throw new Error('SUPABASE_SERVICE_ROLE_KEY is not set in environment variables.');
}

export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey, {
    auth: {
        // It's Supabase convention to auto-refresh the token when using the client library.
        // This is not strictly necessary for service_role key but doesn't hurt.
        autoRefreshToken: true,
        persistSession: false, // Service role keys don't rely on user sessions
        // detectSessionInUrl: false, // Not applicable for service role
    }
});
