describe('Story Creation and Contribution', () => {
  beforeEach(() => {
    // Mock authentication
    cy.mockSupabaseAuth()
    
    // Mock stories API
    cy.intercept('GET', '**/stories**', { fixture: 'stories.json' }).as('getStories')
    cy.intercept('POST', '**/stories', {
      statusCode: 201,
      body: {
        id: 'new-story-id',
        title: 'Test Story',
        description: 'A test story',
        status: 'ACTIVE'
      }
    }).as('createStory')
    
    cy.intercept('POST', '**/contributions', {
      statusCode: 201,
      body: {
        id: 'new-contribution-id',
        text: 'test',
        story_id: 'test-story-id'
      }
    }).as('createContribution')
  })

  describe('Story Creation', () => {
    it('should display create story form correctly', () => {
      cy.visitAndWait('/create-story')
      
      cy.get('[data-testid="story-title-input"]').should('be.visible')
      cy.get('[data-testid="story-description-input"]').should('be.visible')
      cy.get('[data-testid="contribution-mode-select"]').should('be.visible')
      cy.get('[data-testid="create-story-button"]').should('be.visible')
    })

    it('should validate required fields', () => {
      cy.visitAndWait('/create-story')
      cy.get('[data-testid="create-story-button"]').click()
      
      cy.contains('Title is required').should('be.visible')
      cy.contains('Description is required').should('be.visible')
    })

    it('should create a story successfully', () => {
      cy.visitAndWait('/create-story')
      
      cy.get('[data-testid="story-title-input"]').type('My Test Story')
      cy.get('[data-testid="story-description-input"]').type('This is a test story description')
      cy.get('[data-testid="contribution-mode-select"]').select('word')
      cy.get('[data-testid="create-story-button"]').click()
      
      cy.wait('@createStory')
      cy.url().should('include', '/story/')
    })

    it('should display different contribution modes', () => {
      cy.visitAndWait('/create-story')
      
      const modes = ['word', 'sentence', 'paragraph']
      modes.forEach(mode => {
        cy.get('[data-testid="contribution-mode-select"]').select(mode)
        cy.get('[data-testid="contribution-mode-select"]').should('have.value', mode)
      })
    })

    it('should show character count for title and description', () => {
      cy.visitAndWait('/create-story')
      
      const title = 'My Story Title'
      cy.get('[data-testid="story-title-input"]').type(title)
      cy.get('[data-testid="title-character-count"]').should('contain', title.length)
      
      const description = 'Story description here'
      cy.get('[data-testid="story-description-input"]').type(description)
      cy.get('[data-testid="description-character-count"]').should('contain', description.length)
    })
  })

  describe('Story Gallery', () => {
    it('should display stories in the gallery', () => {
      cy.visitAndWait('/gallery')
      cy.wait('@getStories')
      
      cy.get('[data-testid="story-card"]').should('have.length.greaterThan', 0)
      cy.get('[data-testid="story-title"]').should('be.visible')
      cy.get('[data-testid="story-description"]').should('be.visible')
    })

    it('should filter stories by status', () => {
      cy.visitAndWait('/gallery')
      cy.wait('@getStories')
      
      cy.get('[data-testid="status-filter"]').select('ACTIVE')
      cy.get('[data-testid="story-card"]').should('be.visible')
      
      cy.get('[data-testid="status-filter"]').select('COMPLETED')
      // Should filter the results
    })

    it('should search stories by title', () => {
      cy.visitAndWait('/gallery')
      cy.wait('@getStories')
      
      cy.get('[data-testid="search-input"]').type('Mysterious')
      cy.get('[data-testid="search-button"]').click()
      
      cy.get('[data-testid="story-card"]').should('contain', 'Mysterious')
    })

    it('should sort stories', () => {
      cy.visitAndWait('/gallery')
      cy.wait('@getStories')
      
      cy.get('[data-testid="sort-select"]').select('newest')
      cy.get('[data-testid="story-card"]').should('be.visible')
      
      cy.get('[data-testid="sort-select"]').select('popular')
      cy.get('[data-testid="story-card"]').should('be.visible')
    })

    it('should vote on stories', () => {
      cy.intercept('POST', '**/votes', { statusCode: 201 }).as('voteStory')
      
      cy.visitAndWait('/gallery')
      cy.wait('@getStories')
      
      cy.get('[data-testid="vote-button"]').first().click()
      cy.wait('@voteStory')
      
      // Vote count should update
      cy.get('[data-testid="vote-count"]').should('be.visible')
    })
  })

  describe('Story Contribution', () => {
    beforeEach(() => {
      // Mock story details
      cy.intercept('GET', '**/stories/*', {
        statusCode: 200,
        body: {
          id: 'test-story-id',
          title: 'Test Story',
          description: 'A test story',
          status: 'ACTIVE',
          contribution_mode: 'word',
          words_per_contribution: 1,
          contributions: [
            { id: 1, text: 'Once', user: { username: 'user1' } },
            { id: 2, text: 'upon', user: { username: 'user2' } }
          ]
        }
      }).as('getStoryDetails')
    })

    it('should display story details correctly', () => {
      cy.visitAndWait('/story/test-story-id')
      cy.wait('@getStoryDetails')
      
      cy.get('[data-testid="story-title"]').should('contain', 'Test Story')
      cy.get('[data-testid="story-description"]').should('contain', 'A test story')
      cy.get('[data-testid="contribution-list"]').should('be.visible')
    })

    it('should show existing contributions', () => {
      cy.visitAndWait('/story/test-story-id')
      cy.wait('@getStoryDetails')
      
      cy.get('[data-testid="contribution-item"]').should('have.length', 2)
      cy.get('[data-testid="contribution-text"]').first().should('contain', 'Once')
      cy.get('[data-testid="contribution-text"]').last().should('contain', 'upon')
    })

    it('should allow contributing to a story', () => {
      cy.visitAndWait('/story/test-story-id')
      cy.wait('@getStoryDetails')
      
      cy.get('[data-testid="contribution-input"]').type('a')
      cy.get('[data-testid="submit-contribution-button"]').click()
      
      cy.wait('@createContribution')
      
      // Should clear the input
      cy.get('[data-testid="contribution-input"]').should('have.value', '')
      
      // Should show success message
      cy.contains('Contribution added successfully').should('be.visible')
    })

    it('should validate contribution length for word mode', () => {
      cy.visitAndWait('/story/test-story-id')
      cy.wait('@getStoryDetails')
      
      // Try to contribute multiple words when only one is allowed
      cy.get('[data-testid="contribution-input"]').type('multiple words here')
      cy.get('[data-testid="submit-contribution-button"]').click()
      
      cy.contains('Only one word allowed').should('be.visible')
    })

    it('should show typing indicators', () => {
      cy.visitAndWait('/story/test-story-id')
      cy.wait('@getStoryDetails')
      
      cy.get('[data-testid="contribution-input"]').type('test')
      
      // Should show typing indicator (if real-time is working)
      cy.get('[data-testid="typing-indicator"]', { timeout: 1000 }).should('be.visible')
    })

    it('should handle story completion', () => {
      cy.intercept('POST', '**/stories/*/complete', { statusCode: 200 }).as('completeStory')
      
      cy.visitAndWait('/story/test-story-id')
      cy.wait('@getStoryDetails')
      
      cy.get('[data-testid="complete-story-button"]').click()
      cy.get('[data-testid="confirm-complete-button"]').click()
      
      cy.wait('@completeStory')
      cy.contains('Story completed successfully').should('be.visible')
    })
  })

  describe('Real-time Features', () => {
    it('should show user presence indicators', () => {
      cy.visitAndWait('/story/test-story-id')
      cy.wait('@getStoryDetails')
      
      cy.get('[data-testid="active-users"]').should('be.visible')
      cy.get('[data-testid="user-avatar"]').should('have.length.greaterThan', 0)
    })

    it('should update when new contributions arrive', () => {
      cy.visitAndWait('/story/test-story-id')
      cy.wait('@getStoryDetails')
      
      // Simulate real-time update
      cy.window().then((win) => {
        // Trigger a custom event to simulate real-time update
        win.dispatchEvent(new CustomEvent('supabase-realtime-contribution', {
          detail: { text: 'time', user: { username: 'user3' } }
        }))
      })
      
      cy.get('[data-testid="contribution-item"]').should('have.length', 3)
    })
  })
})