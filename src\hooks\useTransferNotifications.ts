import { useState, useEffect, useCallback } from 'react';
import { useNotifications } from '@/contexts/NotificationContext';
import { transferNotificationService } from '@/services/transferNotificationService';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

export interface TransferNotification {
  id: string;
  type: 'transfer_sent' | 'transfer_received' | 'transfer_failed' | 'transfer_limit' | 'transfer_summary';
  content: {
    message: string;
    amount?: number;
    recipient_username?: string;
    sender_username?: string;
    transfer_id?: string;
    new_balance?: number;
    transfer_message?: string;
    error?: string;
    limit_type?: 'daily' | 'single';
    remaining_amount?: number;
    period?: 'weekly' | 'monthly';
    total_sent?: number;
    total_received?: number;
    transfer_count?: number;
  };
  isRead: boolean;
  createdAt: string;
  readAt?: string;
}

export interface UseTransferNotificationsReturn {
  transferNotifications: TransferNotification[];
  unreadTransferCount: number;
  loading: boolean;
  fetchTransferNotifications: () => Promise<void>;
  markTransferAsRead: (notificationId: string) => Promise<void>;
  markAllTransferAsRead: () => Promise<void>;
  markTransferNotificationsAsRead: (transferId?: string) => Promise<void>;
  getNotificationsByType: (type: TransferNotification['type']) => TransferNotification[];
  getRecentTransferActivity: (days?: number) => TransferNotification[];
}

/**
 * Custom hook for managing transfer-specific notifications
 * Extends the general notification system with transfer-specific functionality
 */
export const useTransferNotifications = (): UseTransferNotificationsReturn => {
  const { user } = useAuth();
  const { toast } = useToast();
  const { notifications, markAsRead, markAllAsRead } = useNotifications();

  const [transferNotifications, setTransferNotifications] = useState<TransferNotification[]>([]);
  const [loading, setLoading] = useState(false);

  // Filter and format transfer notifications from the general notifications
  useEffect(() => {
    const transferTypes = ['transfer_sent', 'transfer_received', 'transfer_failed', 'transfer_limit', 'transfer_summary'];
    
    const filteredNotifications = notifications
      .filter(notification => transferTypes.includes(notification.type))
      .map(notification => ({
        id: notification.id,
        type: notification.type as TransferNotification['type'],
        content: notification.content as TransferNotification['content'],
        isRead: notification.is_read,
        createdAt: notification.created_at,
        readAt: notification.read_at || undefined,
      }))
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    setTransferNotifications(filteredNotifications);
  }, [notifications]);

  // Calculate unread transfer notifications count
  const unreadTransferCount = transferNotifications.filter(n => !n.isRead).length;

  // Fetch transfer notifications directly (useful for detailed transfer history)
  const fetchTransferNotifications = useCallback(async () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      const notifications = await transferNotificationService.getTransferNotifications(user.id, 50, 0);
      
      const formattedNotifications: TransferNotification[] = notifications.map(notification => ({
        id: notification.id,
        type: notification.type,
        content: typeof notification.content === 'string' 
          ? JSON.parse(notification.content) 
          : notification.content,
        isRead: notification.is_read,
        createdAt: notification.created_at,
        readAt: notification.read_at || undefined,
      }));

      setTransferNotifications(formattedNotifications);
    } catch (error) {
      console.error('Error fetching transfer notifications:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch transfer notifications.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  }, [user?.id, toast]);

  // Mark a specific transfer notification as read
  const markTransferAsRead = useCallback(async (notificationId: string) => {
    try {
      await markAsRead(notificationId);
    } catch (error) {
      console.error('Error marking transfer notification as read:', error);
      toast({
        title: 'Error',
        description: 'Failed to mark notification as read.',
        variant: 'destructive',
      });
    }
  }, [markAsRead, toast]);

  // Mark all transfer notifications as read
  const markAllTransferAsRead = useCallback(async () => {
    try {
      await markAllAsRead();
    } catch (error) {
      console.error('Error marking all transfer notifications as read:', error);
      toast({
        title: 'Error',
        description: 'Failed to mark all notifications as read.',
        variant: 'destructive',
      });
    }
  }, [markAllAsRead, toast]);

  // Mark transfer notifications for a specific transfer as read
  const markTransferNotificationsAsRead = useCallback(async (transferId?: string) => {
    if (!user?.id) return;

    try {
      await transferNotificationService.markTransferNotificationsAsRead(user.id, transferId);
      
      // Update local state
      if (transferId) {
        setTransferNotifications(prev => 
          prev.map(notification => 
            notification.content.transfer_id === transferId 
              ? { ...notification, isRead: true, readAt: new Date().toISOString() }
              : notification
          )
        );
      }
    } catch (error) {
      console.error('Error marking transfer notifications as read:', error);
      toast({
        title: 'Error',
        description: 'Failed to mark transfer notifications as read.',
        variant: 'destructive',
      });
    }
  }, [user?.id, toast]);

  // Get notifications by specific type
  const getNotificationsByType = useCallback((type: TransferNotification['type']) => {
    return transferNotifications.filter(notification => notification.type === type);
  }, [transferNotifications]);

  // Get recent transfer activity
  const getRecentTransferActivity = useCallback((days: number = 7) => {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    return transferNotifications.filter(notification => 
      new Date(notification.createdAt) >= cutoffDate &&
      (notification.type === 'transfer_sent' || notification.type === 'transfer_received')
    );
  }, [transferNotifications]);

  return {
    transferNotifications,
    unreadTransferCount,
    loading,
    fetchTransferNotifications,
    markTransferAsRead,
    markAllTransferAsRead,
    markTransferNotificationsAsRead,
    getNotificationsByType,
    getRecentTransferActivity,
  };
}; 