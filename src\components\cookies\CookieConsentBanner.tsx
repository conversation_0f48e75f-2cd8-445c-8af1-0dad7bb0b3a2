import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>ting<PERSON>, Shield, BarChart3, Target, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  acceptAllCookies,
  acceptNecessaryOnly,
  acceptCustomSettings,
  type CookieConsentSettings,
} from '@/utils/cookie-consent';

interface CookieConsentBannerProps {
  isVisible: boolean;
  onConsentGiven: () => void;
}

export const CookieConsentBanner: React.FC<CookieConsentBannerProps> = ({
  isVisible,
  onConsentGiven,
}) => {
  const [showDetails, setShowDetails] = useState(false);
  const [customSettings, setCustomSettings] = useState({
    necessary: true, // Always true and disabled
    analytics: false,
    marketing: false,
    functional: false,
  });

  const handleAcceptAll = () => {
    acceptAllCookies();
    onConsentGiven();
  };

  const handleAcceptNecessary = () => {
    acceptNecessaryOnly();
    onConsentGiven();
  };

  const handleAcceptCustom = () => {
    acceptCustomSettings(customSettings);
    onConsentGiven();
  };

  const handleSettingChange = (category: keyof typeof customSettings, checked: boolean) => {
    if (category === 'necessary') return; // Can't disable necessary cookies
    
    setCustomSettings(prev => ({
      ...prev,
      [category]: checked,
    }));
  };

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-white border-t shadow-lg animate-in slide-in-from-bottom-full">
      <div className="max-w-6xl mx-auto">
        <Card className="border-none shadow-none">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Cookie className="h-5 w-5 text-orange-500" />
              We Use Cookies
            </CardTitle>
            <CardDescription className="text-sm">
              We use cookies to enhance your experience, analyze site usage, and support our free service through ads. 
              <strong className="text-gray-900"> You must accept our cookie policy to continue using Word-by-Word Story.</strong>
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Quick Actions */}
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={handleAcceptAll} className="flex-1">
                Accept All & Continue
              </Button>
              <Button variant="outline" onClick={handleAcceptNecessary} className="flex-1">
                Essential Only
              </Button>
              <Button 
                variant="ghost" 
                onClick={() => setShowDetails(!showDetails)}
                className="flex items-center gap-2"
              >
                <Settings className="h-4 w-4" />
                Customize
              </Button>
            </div>

            {/* Detailed Settings */}
            <Collapsible open={showDetails} onOpenChange={setShowDetails}>
              <CollapsibleContent className="space-y-4">
                <div className="grid gap-4 pt-4 border-t">
                  <div className="text-sm font-medium text-gray-900">
                    Choose which cookies you accept:
                  </div>

                  {/* Necessary Cookies */}
                  <div className="flex items-start space-x-3">
                    <Checkbox 
                      checked={true} 
                      disabled={true}
                      className="mt-1"
                    />
                    <div className="space-y-1 flex-1">
                      <div className="flex items-center gap-2">
                        <Shield className="h-4 w-4 text-green-500" />
                        <span className="font-medium text-sm">Essential Cookies</span>
                        <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded">Required</span>
                      </div>
                      <p className="text-xs text-gray-600">
                        Necessary for basic website functionality, security, and remembering your preferences.
                      </p>
                    </div>
                  </div>

                  {/* Analytics Cookies */}
                  <div className="flex items-start space-x-3">
                    <Checkbox 
                      checked={customSettings.analytics}
                      onCheckedChange={(checked) => handleSettingChange('analytics', checked as boolean)}
                      className="mt-1"
                    />
                    <div className="space-y-1 flex-1">
                      <div className="flex items-center gap-2">
                        <BarChart3 className="h-4 w-4 text-blue-500" />
                        <span className="font-medium text-sm">Analytics Cookies</span>
                      </div>
                      <p className="text-xs text-gray-600">
                        Help us understand how you use our app to improve your experience (PostHog, Google Analytics).
                      </p>
                    </div>
                  </div>

                  {/* Marketing Cookies */}
                  <div className="flex items-start space-x-3">
                    <Checkbox 
                      checked={customSettings.marketing}
                      onCheckedChange={(checked) => handleSettingChange('marketing', checked as boolean)}
                      className="mt-1"
                    />
                    <div className="space-y-1 flex-1">
                      <div className="flex items-center gap-2">
                        <Target className="h-4 w-4 text-purple-500" />
                        <span className="font-medium text-sm">Marketing Cookies</span>
                      </div>
                      <p className="text-xs text-gray-600">
                        Enable personalized ads and help support our free service through ad revenue.
                      </p>
                    </div>
                  </div>

                  {/* Functional Cookies */}
                  <div className="flex items-start space-x-3">
                    <Checkbox 
                      checked={customSettings.functional}
                      onCheckedChange={(checked) => handleSettingChange('functional', checked as boolean)}
                      className="mt-1"
                    />
                    <div className="space-y-1 flex-1">
                      <div className="flex items-center gap-2">
                        <Settings className="h-4 w-4 text-orange-500" />
                        <span className="font-medium text-sm">Functional Cookies</span>
                      </div>
                      <p className="text-xs text-gray-600">
                        Remember your preferences and settings for a personalized experience.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Custom Accept Button */}
                <div className="flex justify-end pt-4 border-t">
                  <Button onClick={handleAcceptCustom}>
                    Save My Preferences
                  </Button>
                </div>
              </CollapsibleContent>
            </Collapsible>

            {/* Policy Links */}
            <div className="text-xs text-gray-500 border-t pt-3">
              By clicking "Accept", you agree to our use of cookies as described in our{' '}
              <a href="/terms" className="text-blue-600 hover:underline">Privacy Policy</a>.
              You can change your preferences anytime in your account settings.
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CookieConsentBanner; 