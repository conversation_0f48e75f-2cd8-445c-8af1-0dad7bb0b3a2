import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/auth';
import { gravatarService, GravatarOptions, AvatarPreference } from '@/services/gravatarService';
import { supabase } from '@/lib/supabase';

interface UseAvatarReturn {
  // Current avatar state
  avatarUrl: string;
  isLoading: boolean;
  
  // Avatar options
  avatarOptions: {
    gravatar: { url: string; available: boolean };
    initials: string;
    default: string;
  } | null;
  
  // User preferences
  preferences: AvatarPreference;
  
  // Actions
  updatePreferences: (newPreferences: Partial<AvatarPreference>) => Promise<boolean>;
  refreshAvatar: () => Promise<void>;
  loadAvatarOptions: () => Promise<void>;
}

export const useAvatar = (): UseAvatarReturn => {
  const { user } = useAuth();
  
  const [avatarUrl, setAvatarUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [avatarOptions, setAvatarOptions] = useState<{
    gravatar: { url: string; available: boolean };
    initials: string;
    default: string;
  } | null>(null);
  
  // Default preferences
  const [preferences, setPreferences] = useState<AvatarPreference>({
    source: 'gravatar',
    useGravatar: true,
  });

  /**
   * Load user's avatar preferences from database
   */
  const loadPreferences = useCallback(async () => {
    if (!user?.id) return;

    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('avatar_preferences')
        .eq('id', user.id)
        .single();

      if (error) {
        console.warn('Error loading avatar preferences:', error);
        return;
      }

      if (data?.avatar_preferences) {
        setPreferences({ ...preferences, ...data.avatar_preferences });
      }
    } catch (error) {
      console.error('Failed to load avatar preferences:', error);
    }
  }, [user?.id, preferences]);

  /**
   * Save avatar preferences to database
   */
  const updatePreferences = useCallback(async (newPreferences: Partial<AvatarPreference>): Promise<boolean> => {
    if (!user?.id) return false;

    const updatedPreferences = { ...preferences, ...newPreferences };

    try {
      const { error } = await supabase
        .from('user_profiles')
        .update({ avatar_preferences: updatedPreferences })
        .eq('id', user.id);

      if (error) {
        console.error('Error updating avatar preferences:', error);
        return false;
      }

      setPreferences(updatedPreferences);
      
      // Refresh avatar with new preferences
      await refreshAvatar();
      
      return true;
    } catch (error) {
      console.error('Failed to update avatar preferences:', error);
      return false;
    }
  }, [user?.id, preferences]);

  /**
   * Load available avatar options
   */
  const loadAvatarOptions = useCallback(async () => {
    if (!user?.email) return;

    setIsLoading(true);
    try {
      const options = await gravatarService.getAvatarOptions(user.email, user.username);
      setAvatarOptions(options);
    } catch (error) {
      console.error('Failed to load avatar options:', error);
    } finally {
      setIsLoading(false);
    }
  }, [user?.email, user.username]);

  /**
   * Refresh the current avatar URL based on preferences
   */
  const refreshAvatar = useCallback(async () => {
    if (!user?.email) {
      setAvatarUrl(gravatarService.getDefaultAvatarUrl());
      return;
    }

    setIsLoading(true);
    try {
      const gravatarOptions: GravatarOptions = {
        size: 80,
        default: 'mp',
        rating: 'g',
      };

      const bestAvatarUrl = await gravatarService.getBestAvatarUrl(
        user.email,
        preferences,
        gravatarOptions
      );

      setAvatarUrl(bestAvatarUrl);
    } catch (error) {
      console.error('Failed to refresh avatar:', error);
      // Fallback to initials avatar
      if (user.username || user.email) {
        setAvatarUrl(gravatarService.generateInitialsAvatar(user.username || user.email));
      } else {
        setAvatarUrl(gravatarService.getDefaultAvatarUrl());
      }
    } finally {
      setIsLoading(false);
    }
  }, [user?.email, user?.username, preferences]);

  // Load preferences when user changes
  useEffect(() => {
    if (user?.id) {
      loadPreferences();
    }
  }, [user?.id, loadPreferences]);

  // Refresh avatar when preferences change
  useEffect(() => {
    refreshAvatar();
  }, [refreshAvatar]);

  // Load avatar options when user changes
  useEffect(() => {
    if (user?.email) {
      loadAvatarOptions();
    }
  }, [user?.email, loadAvatarOptions]);

  return {
    avatarUrl,
    isLoading,
    avatarOptions,
    preferences,
    updatePreferences,
    refreshAvatar,
    loadAvatarOptions,
  };
}; 