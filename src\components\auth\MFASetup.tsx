import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Shield, Phone, CheckCircle } from "lucide-react";
import { useAuth } from "@/contexts/auth";
import { supabase } from "@/lib/supabase";

interface MFASetupProps {
  onSetupComplete?: () => void;
  onCancel?: () => void;
}

interface MFAFactor {
  id: string;
  friendly_name?: string;
  factor_type: 'phone' | 'totp' | (string & {});
  status: 'unverified' | 'verified';
  phone?: string;
}

export const MFASetup: React.FC<MFASetupProps> = ({
  onSetupComplete,
  onCancel,
}) => {
  const { user } = useAuth();
  const [phoneNumber, setPhoneNumber] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [factorId, setFactorId] = useState("");
  const [challengeId, setChallengeId] = useState("");
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'phone' | 'verify' | 'complete'>('phone');
  const [existingFactors, setExistingFactors] = useState<MFAFactor[]>([]);

  // Check for existing MFA factors on component mount
  useEffect(() => {
    const checkExistingFactors = async () => {
      try {
        const { data, error } = await supabase.auth.mfa.listFactors();
        if (error) throw error;
        
        const allFactors = [...(data.totp || []), ...(data.phone || [])];
        setExistingFactors(allFactors);
        
        // If user already has verified factors, show completion state
        const hasVerifiedFactor = allFactors.some(factor => factor.status === 'verified');
        if (hasVerifiedFactor) {
          setStep('complete');
        }
      } catch (err) {
        console.error('Error checking existing factors:', err);
      }
    };

    if (user) {
      checkExistingFactors();
    }
  }, [user]);

  const handleEnrollPhone = async () => {
    if (!phoneNumber.trim()) {
      setError("Please enter a valid phone number");
      return;
    }

    setLoading(true);
    setError("");

    try {
      // Enroll the phone factor
      const { data: factor, error: enrollError } = await supabase.auth.mfa.enroll({
        phone: phoneNumber,
        factorType: 'phone',
      });

      if (enrollError) throw enrollError;

      setFactorId(factor.id);
      
      // Immediately send the first challenge
      const { data: challenge, error: challengeError } = await supabase.auth.mfa.challenge({
        factorId: factor.id,
      });

      if (challengeError) throw challengeError;

      setChallengeId(challenge.id);
      setStep('verify');
    } catch (err: any) {
      setError(err.message || "Failed to set up phone verification");
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyCode = async () => {
    if (!verificationCode.trim()) {
      setError("Please enter the verification code");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const { error: verifyError } = await supabase.auth.mfa.verify({
        factorId,
        challengeId,
        code: verificationCode,
      });

      if (verifyError) throw verifyError;

      setStep('complete');
      onSetupComplete?.();
    } catch (err: any) {
      setError(err.message || "Invalid verification code");
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (!factorId) return;

    setLoading(true);
    setError("");

    try {
      const { data: challenge, error } = await supabase.auth.mfa.challenge({
        factorId,
      });

      if (error) throw error;

      setChallengeId(challenge.id);
      setError(""); // Clear any previous errors
    } catch (err: any) {
      setError(err.message || "Failed to resend verification code");
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveFactor = async (factorId: string) => {
    setLoading(true);
    setError("");

    try {
      const { error } = await supabase.auth.mfa.unenroll({ factorId });
      if (error) throw error;

      // Refresh the factors list
      const { data, error: listError } = await supabase.auth.mfa.listFactors();
      if (listError) throw listError;
      
      const allFactors = [...(data.totp || []), ...(data.phone || [])];
      setExistingFactors(allFactors);
      
      // If no verified factors remain, go back to setup
      const hasVerifiedFactor = allFactors.some(factor => factor.status === 'verified');
      if (!hasVerifiedFactor) {
        setStep('phone');
      }
    } catch (err: any) {
      setError(err.message || "Failed to remove MFA factor");
    } finally {
      setLoading(false);
    }
  };

  if (step === 'complete') {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
            <CheckCircle className="w-6 h-6 text-green-600" />
          </div>
          <CardTitle>Multi-Factor Authentication Enabled</CardTitle>
          <CardDescription>
            Your account is now protected with an additional layer of security
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {existingFactors.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Active MFA Methods:</h4>
              {existingFactors.map((factor) => (
                <div key={factor.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Phone className="w-4 h-4" />
                    <span className="text-sm">
                      {factor.factor_type === 'phone' ? factor.phone : 'Authenticator App'}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded ${
                      factor.status === 'verified' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {factor.status}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveFactor(factor.id)}
                    disabled={loading}
                  >
                    Remove
                  </Button>
                </div>
              ))}
            </div>
          )}
          
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="flex space-x-2">
            <Button 
              onClick={() => setStep('phone')} 
              variant="outline" 
              className="flex-1"
            >
              Add Another Method
            </Button>
            <Button onClick={onCancel} className="flex-1">
              Done
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (step === 'verify') {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
            <Phone className="w-6 h-6 text-blue-600" />
          </div>
          <CardTitle>Verify Your Phone</CardTitle>
          <CardDescription>
            Enter the verification code sent to {phoneNumber}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Input
              type="text"
              placeholder="Enter 6-digit code"
              value={verificationCode}
              onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
              maxLength={6}
              className="text-center text-lg tracking-widest"
            />
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Button
              onClick={handleVerifyCode}
              disabled={loading || verificationCode.length !== 6}
              className="w-full"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Verifying...
                </>
              ) : (
                "Verify Code"
              )}
            </Button>

            <Button
              variant="ghost"
              onClick={handleResendCode}
              disabled={loading}
              className="w-full"
            >
              Resend Code
            </Button>

            <Button
              variant="outline"
              onClick={() => setStep('phone')}
              className="w-full"
            >
              Back
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
          <Shield className="w-6 h-6 text-blue-600" />
        </div>
        <CardTitle>Set Up Multi-Factor Authentication</CardTitle>
        <CardDescription>
          Add an extra layer of security to your account with phone verification
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label htmlFor="phone" className="text-sm font-medium">
            Phone Number
          </label>
          <Input
            id="phone"
            type="tel"
            placeholder="+****************"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(e.target.value)}
          />
          <p className="text-xs text-gray-500">
            Include country code (e.g., +1 for US)
          </p>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <Button
            onClick={handleEnrollPhone}
            disabled={loading || !phoneNumber.trim()}
            className="w-full"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Setting up...
              </>
            ) : (
              "Send Verification Code"
            )}
          </Button>

          {onCancel && (
            <Button
              variant="outline"
              onClick={onCancel}
              className="w-full"
            >
              Cancel
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
