[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [drawer](../README.md) / DrawerContent

# Variable: DrawerContent

> `const` **DrawerContent**: `ForwardRefExoticComponent`\<`Omit`\<`Omit`\<`DialogContentProps` & `RefAttributes`\<`HTMLDivElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLDivElement`\>, `"ref"`\> & `RefAttributes`\<`HTMLDivElement`\>\>

Defined in: [src/components/ui/drawer.tsx:35](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/drawer.tsx#L35)
