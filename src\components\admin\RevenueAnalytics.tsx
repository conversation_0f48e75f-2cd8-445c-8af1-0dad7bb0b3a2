import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  <PERSON>Chart,
  Pie,
  Cell,
} from "recharts";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ArrowDown,
  ArrowUp,
  DollarSign,
  Users,
  CreditCard,
  Calendar,
} from "lucide-react";

// Mock data for charts
const revenueData = [
  { month: "Jan", revenue: 12500, users: 150 },
  { month: "Feb", revenue: 13700, users: 210 },
  { month: "Mar", revenue: 15900, users: 250 },
  { month: "Apr", revenue: 17800, users: 320 },
  { month: "May", revenue: 19200, users: 390 },
  { month: "Jun", revenue: 21000, users: 450 },
];

const tierDistribution = [
  { name: "Free", value: 65, color: "#94a3b8" },
  { name: "<PERSON><PERSON>", value: 20, color: "#60a5fa" },
  { name: "Storyteller", value: 10, color: "#a78bfa" },
  { name: "Authors Guild", value: 5, color: "#facc15" },
];

const revenueMetrics = {
  totalRevenue: {
    value: "$102,100",
    change: "+15.3%",
    trend: "up",
  },
  avgRevenuePerUser: {
    value: "$42.50",
    change: "****%",
    trend: "up",
  },
  subscriptionRenewalRate: {
    value: "87%",
    change: "-2.1%",
    trend: "down",
  },
  conversionRate: {
    value: "32%",
    change: "****%",
    trend: "up",
  },
};

export const RevenueAnalytics: React.FC = () => {
  const [timeFrame, setTimeFrame] = useState("6m");

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Total Revenue</CardDescription>
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl">
                {revenueMetrics.totalRevenue.value}
              </CardTitle>
              <div
                className={`flex items-center ${
                  revenueMetrics.totalRevenue.trend === "up"
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {revenueMetrics.totalRevenue.trend === "up" ? (
                  <ArrowUp className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowDown className="h-4 w-4 mr-1" />
                )}
                <span>{revenueMetrics.totalRevenue.change}</span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-xs text-muted-foreground">
              Compared to previous period
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Avg. Revenue Per User</CardDescription>
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl">
                {revenueMetrics.avgRevenuePerUser.value}
              </CardTitle>
              <div
                className={`flex items-center ${
                  revenueMetrics.avgRevenuePerUser.trend === "up"
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {revenueMetrics.avgRevenuePerUser.trend === "up" ? (
                  <ArrowUp className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowDown className="h-4 w-4 mr-1" />
                )}
                <span>{revenueMetrics.avgRevenuePerUser.change}</span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-xs text-muted-foreground">
              Compared to previous period
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Subscription Renewal Rate</CardDescription>
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl">
                {revenueMetrics.subscriptionRenewalRate.value}
              </CardTitle>
              <div
                className={`flex items-center ${
                  revenueMetrics.subscriptionRenewalRate.trend === "up"
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {revenueMetrics.subscriptionRenewalRate.trend === "up" ? (
                  <ArrowUp className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowDown className="h-4 w-4 mr-1" />
                )}
                <span>{revenueMetrics.subscriptionRenewalRate.change}</span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-xs text-muted-foreground">
              Compared to previous period
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardDescription>Free to Paid Conversion</CardDescription>
            <div className="flex items-center justify-between">
              <CardTitle className="text-2xl">
                {revenueMetrics.conversionRate.value}
              </CardTitle>
              <div
                className={`flex items-center ${
                  revenueMetrics.conversionRate.trend === "up"
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {revenueMetrics.conversionRate.trend === "up" ? (
                  <ArrowUp className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowDown className="h-4 w-4 mr-1" />
                )}
                <span>{revenueMetrics.conversionRate.change}</span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-xs text-muted-foreground">
              Compared to previous period
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="lg:col-span-5">
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Revenue Over Time</CardTitle>
              <CardDescription>Monthly revenue performance</CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex items-center text-sm text-muted-foreground">
                <Calendar className="mr-1 h-4 w-4" />
                <span>Period:</span>
              </div>
              <Select
                defaultValue={timeFrame}
                onValueChange={(value) => setTimeFrame(value)}
              >
                <SelectTrigger className="w-[100px]">
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="30d">30 days</SelectItem>
                  <SelectItem value="6m">6 months</SelectItem>
                  <SelectItem value="1y">1 year</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
          <CardContent className="pl-2">
            <ResponsiveContainer width="100%" height={350}>
              <LineChart
                data={revenueData}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="revenue"
                  stroke="#facc15"
                  strokeWidth={2}
                  name="Revenue ($)"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Subscription Tiers</CardTitle>
            <CardDescription>Distribution of users by tier</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center">
            <ResponsiveContainer width="100%" height={220}>
              <PieChart>
                <Pie
                  data={tierDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) =>
                    `${name}: ${(percent * 100).toFixed(0)}%`
                  }
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {tierDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Growth Metrics</CardTitle>
          <CardDescription>Revenue and user growth comparison</CardDescription>
        </CardHeader>
        <CardContent className="pl-2">
          <ResponsiveContainer width="100%" height={350}>
            <BarChart
              data={revenueData}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip />
              <Legend />
              <Bar
                yAxisId="left"
                dataKey="revenue"
                name="Revenue ($)"
                fill="#facc15"
              />
              <Bar
                yAxisId="right"
                dataKey="users"
                name="Active Users"
                fill="#60a5fa"
              />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </div>
  );
};
