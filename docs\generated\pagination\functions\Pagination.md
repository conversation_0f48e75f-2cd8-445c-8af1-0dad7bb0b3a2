[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [pagination](../README.md) / Pagination

# Function: Pagination()

> **Pagination**(`__namedParameters`): `Element`

Defined in: [src/components/ui/pagination.tsx:7](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/pagination.tsx#L7)

## Parameters

### \_\_namedParameters

`DetailedHTMLProps`\<`HTMLAttributes`\<`HTMLElement`\>\>

## Returns

`Element`
