import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '../test/utils';
import SubscriptionPlansCard from '../components/Subscription/SubscriptionPlansCard';
import type { PricingPlan } from '../components/Subscription/SubscriptionPage';

const mockPlans: PricingPlan[] = [
  {
    id: 'price_monthly',
    name: 'Ad-Free Monthly',
    description: 'Enjoy an ad-free experience across the platform.',
    price: '$5/month',
    features: ['No advertisements', 'Support the platform'],
  },
  {
    id: 'price_yearly',
    name: 'Ad-Free Yearly',
    description: 'Get the best value with an annual ad-free subscription.',
    price: '$50/year',
    features: ['No advertisements', 'Support the platform', 'Save 16%'],
  },
];

describe('SubscriptionPlansCard Component', () => {
  const mockOnSubscribe = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders empty state when no plans are provided', () => {
    render(
      <SubscriptionPlansCard 
        plans={[]} 
        onSubscribe={mockOnSubscribe} 
      />
    );
    
    expect(screen.getByText('No Plans Available')).toBeInTheDocument();
    expect(screen.getByText('Subscription plans are currently being set up. Please check back later.')).toBeInTheDocument();
  });

  it('renders all provided plans', () => {
    render(
      <SubscriptionPlansCard 
        plans={mockPlans} 
        onSubscribe={mockOnSubscribe} 
      />
    );
    
    expect(screen.getByText('Ad-Free Monthly')).toBeInTheDocument();
    expect(screen.getByText('Ad-Free Yearly')).toBeInTheDocument();
    expect(screen.getByText('$5/month')).toBeInTheDocument();
    expect(screen.getByText('$50/year')).toBeInTheDocument();
  });

  it('displays plan features correctly', () => {
    render(
      <SubscriptionPlansCard 
        plans={mockPlans} 
        onSubscribe={mockOnSubscribe} 
      />
    );
    
    const noAdsFeatures = screen.getAllByText('No advertisements');
    expect(noAdsFeatures).toHaveLength(2); // Both plans have this feature
    
    expect(screen.getByText('Save 16%')).toBeInTheDocument(); // Only yearly plan
  });

  it('identifies best value plan correctly', () => {
    render(
      <SubscriptionPlansCard 
        plans={mockPlans} 
        onSubscribe={mockOnSubscribe} 
      />
    );
    
    // Yearly plan should be marked as best value (contains "Save")
    expect(screen.getByText('Most popular choice')).toBeInTheDocument();
    expect(screen.getByText('Best Value')).toBeInTheDocument();
  });

  it('handles subscription button clicks', async () => {
    render(
      <SubscriptionPlansCard 
        plans={mockPlans} 
        onSubscribe={mockOnSubscribe} 
      />
    );
    
    const monthlyButton = screen.getByText('Subscribe to Ad-Free Monthly');
    fireEvent.click(monthlyButton);
    
    expect(mockOnSubscribe).toHaveBeenCalledWith('price_monthly');
  });

  it('shows loading state when subscription is in progress', async () => {
    render(
      <SubscriptionPlansCard 
        plans={mockPlans} 
        onSubscribe={mockOnSubscribe}
        isLoading={true}
      />
    );
    
    const buttons = screen.getAllByRole('button');
    buttons.forEach(button => {
      expect(button).toBeDisabled();
    });
  });

  it('shows loading state for specific plan when clicked', async () => {
    mockOnSubscribe.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));
    
    render(
      <SubscriptionPlansCard 
        plans={mockPlans} 
        onSubscribe={mockOnSubscribe} 
      />
    );
    
    const monthlyButton = screen.getByText('Subscribe to Ad-Free Monthly');
    fireEvent.click(monthlyButton);
    
    await waitFor(() => {
      expect(screen.getByText('Redirecting...')).toBeInTheDocument();
    });
    
    // Other button should still be enabled
    const yearlyButton = screen.getByText('Subscribe to Ad-Free Yearly');
    expect(yearlyButton).not.toBeDisabled();
  });

  it('handles subscription errors gracefully', async () => {
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    mockOnSubscribe.mockRejectedValue(new Error('Subscription failed'));
    
    render(
      <SubscriptionPlansCard 
        plans={mockPlans} 
        onSubscribe={mockOnSubscribe} 
      />
    );
    
    const monthlyButton = screen.getByText('Subscribe to Ad-Free Monthly');
    fireEvent.click(monthlyButton);
    
    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error subscribing to plan:', expect.any(Error));
    });
    
    consoleErrorSpy.mockRestore();
  });

  it('applies correct styling for best value plan', () => {
    render(
      <SubscriptionPlansCard 
        plans={mockPlans} 
        onSubscribe={mockOnSubscribe} 
      />
    );
    
    const bestValueButton = screen.getByText('Subscribe to Ad-Free Yearly');
    expect(bestValueButton).toHaveClass('bg-blue-600');
    
    const regularButton = screen.getByText('Subscribe to Ad-Free Monthly');
    expect(regularButton).toHaveClass('bg-green-600');
  });

  it('displays additional information section', () => {
    render(
      <SubscriptionPlansCard 
        plans={mockPlans} 
        onSubscribe={mockOnSubscribe} 
      />
    );
    
    expect(screen.getByText('🔒 Secure Payment:')).toBeInTheDocument();
    expect(screen.getByText('📧 Easy Cancellation:')).toBeInTheDocument();
    expect(screen.getByText('💝 Support the Platform:')).toBeInTheDocument();
  });

  it('renders with single plan correctly', () => {
    const singlePlan = [mockPlans[0]];
    
    render(
      <SubscriptionPlansCard 
        plans={singlePlan} 
        onSubscribe={mockOnSubscribe} 
      />
    );
    
    expect(screen.getByText('Ad-Free Monthly')).toBeInTheDocument();
    expect(screen.queryByText('Best Value')).not.toBeInTheDocument(); // No best value for single plan
    expect(screen.queryByText('Most popular choice')).not.toBeInTheDocument();
  });
});