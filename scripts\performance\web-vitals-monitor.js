#!/usr/bin/env node

// Core Web Vitals monitoring script for production
// This script can be integrated into the application to collect real user metrics

const fs = require('fs').promises;
const path = require('path');

// Core Web Vitals thresholds
const THRESHOLDS = {
  LCP: { good: 2500, needsImprovement: 4000 },     // Largest Contentful Paint (ms)
  FID: { good: 100, needsImprovement: 300 },       // First Input Delay (ms)  
  CLS: { good: 0.1, needsImprovement: 0.25 },      // Cumulative Layout Shift
  TTFB: { good: 800, needsImprovement: 1800 },     // Time to First Byte (ms)
  INP: { good: 200, needsImprovement: 500 }        // Interaction to Next Paint (ms)
};

// Client-side Web Vitals collection code
const WEB_VITALS_CLIENT_CODE = `
// Web Vitals measurement client code
(function() {
  // Store metrics
  const metrics = {};
  const debug = sessionStorage.getItem('webVitalsDebug') === 'true';
  
  function log(message) {
    if (debug) console.log('[Web Vitals]', message);
  }

  // Measure LCP (Largest Contentful Paint)
  function measureLCP() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        const lastEntry = entries[entries.length - 1];
        metrics.LCP = Math.round(lastEntry.startTime);
        log(\`LCP: \${metrics.LCP}ms\`);
      });
      
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    } else {
      // Fallback using load event
      window.addEventListener('load', () => {
        setTimeout(() => {
          metrics.LCP = performance.now();
          log(\`LCP (fallback): \${metrics.LCP}ms\`);
        }, 0);
      });
    }
  }

  // Measure FID (First Input Delay)
  function measureFID() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          metrics.FID = Math.round(entry.processingStart - entry.startTime);
          log(\`FID: \${metrics.FID}ms\`);
          break; // Only capture the first input delay
        }
      });
      
      observer.observe({ entryTypes: ['first-input'] });
    }
  }

  // Measure CLS (Cumulative Layout Shift)
  function measureCLS() {
    let clsValue = 0;
    let sessionValue = 0;
    let sessionEntries = [];

    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          if (!entry.hadRecentInput) {
            const firstSessionEntry = sessionEntries[0];
            const lastSessionEntry = sessionEntries[sessionEntries.length - 1];

            if (sessionValue && 
                entry.startTime - lastSessionEntry.startTime < 1000 &&
                entry.startTime - firstSessionEntry.startTime < 5000) {
              sessionValue += entry.value;
              sessionEntries.push(entry);
            } else {
              sessionValue = entry.value;
              sessionEntries = [entry];
            }

            if (sessionValue > clsValue) {
              clsValue = sessionValue;
              metrics.CLS = Math.round(clsValue * 10000) / 10000;
              log(\`CLS: \${metrics.CLS}\`);
            }
          }
        }
      });

      observer.observe({ entryTypes: ['layout-shift'] });
    }
  }

  // Measure TTFB (Time to First Byte)
  function measureTTFB() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          if (entry.name === window.location.href) {
            metrics.TTFB = Math.round(entry.responseStart - entry.requestStart);
            log(\`TTFB: \${metrics.TTFB}ms\`);
            break;
          }
        }
      });
      
      observer.observe({ entryTypes: ['navigation'] });
    } else {
      // Fallback using Performance Timing API
      window.addEventListener('load', () => {
        const timing = performance.timing;
        metrics.TTFB = timing.responseStart - timing.requestStart;
        log(\`TTFB (fallback): \${metrics.TTFB}ms\`);
      });
    }
  }

  // Measure INP (Interaction to Next Paint) - Experimental
  function measureINP() {
    if ('PerformanceObserver' in window) {
      let longestInteraction = 0;
      
      const observer = new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          const interactionTime = entry.processingEnd - entry.startTime;
          if (interactionTime > longestInteraction) {
            longestInteraction = interactionTime;
            metrics.INP = Math.round(longestInteraction);
            log(\`INP: \${metrics.INP}ms\`);
          }
        }
      });
      
      try {
        observer.observe({ entryTypes: ['event'] });
      } catch (e) {
        log('INP measurement not supported');
      }
    }
  }

  // Additional performance metrics
  function measureAdditionalMetrics() {
    window.addEventListener('load', () => {
      // First Contentful Paint
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((entryList) => {
          for (const entry of entryList.getEntries()) {
            if (entry.name === 'first-contentful-paint') {
              metrics.FCP = Math.round(entry.startTime);
              log(\`FCP: \${metrics.FCP}ms\`);
            }
          }
        });
        
        observer.observe({ entryTypes: ['paint'] });
      }

      // Time to Interactive (approximation)
      setTimeout(() => {
        const timing = performance.timing;
        metrics.TTI = timing.domInteractive - timing.navigationStart;
        log(\`TTI: \${metrics.TTI}ms\`);
      }, 100);

      // Speed Index (simplified calculation)
      const entries = performance.getEntriesByType('navigation');
      if (entries.length > 0) {
        const nav = entries[0];
        metrics.speedIndex = Math.round(nav.domContentLoadedEventEnd - nav.fetchStart);
        log(\`Speed Index: \${metrics.speedIndex}ms\`);
      }
    });
  }

  // Send metrics to analytics
  function sendMetrics() {
    const data = {
      ...metrics,
      url: window.location.href,
      userAgent: navigator.userAgent,
      connection: navigator.connection ? {
        effectiveType: navigator.connection.effectiveType,
        downlink: navigator.connection.downlink,
        rtt: navigator.connection.rtt
      } : null,
      timestamp: new Date().toISOString(),
      viewportSize: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      deviceMemory: navigator.deviceMemory || null,
      hardwareConcurrency: navigator.hardwareConcurrency || null
    };

    // Send to analytics endpoint
    if (window.gtag) {
      // Google Analytics 4
      Object.entries(metrics).forEach(([metric, value]) => {
        gtag('event', metric, {
          custom_parameter_1: value,
          custom_parameter_2: getMetricRating(metric, value)
        });
      });
    }

    // Send to PostHog if available
    if (window.posthog) {
      window.posthog.capture('web_vitals_measured', data);
    }

    // Send to custom endpoint
    if (typeof window.webVitalsEndpoint === 'string') {
      fetch(window.webVitalsEndpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      }).catch(err => log('Failed to send metrics:', err));
    }

    log('Metrics collected:', data);
  }

  // Get metric rating (good, needs-improvement, poor)
  function getMetricRating(metric, value) {
    const thresholds = {
      LCP: { good: 2500, needsImprovement: 4000 },
      FID: { good: 100, needsImprovement: 300 },
      CLS: { good: 0.1, needsImprovement: 0.25 },
      TTFB: { good: 800, needsImprovement: 1800 },
      INP: { good: 200, needsImprovement: 500 }
    };

    const threshold = thresholds[metric];
    if (!threshold) return 'unknown';

    if (value <= threshold.good) return 'good';
    if (value <= threshold.needsImprovement) return 'needs-improvement';
    return 'poor';
  }

  // Initialize measurements
  measureLCP();
  measureFID();
  measureCLS();
  measureTTFB();
  measureINP();
  measureAdditionalMetrics();

  // Send metrics when page is about to unload
  window.addEventListener('beforeunload', sendMetrics);
  
  // Also send metrics after 30 seconds for long-lived pages
  setTimeout(sendMetrics, 30000);

  // Expose metrics for debugging
  window.webVitalsMetrics = metrics;
  
  log('Web Vitals monitoring initialized');
})();
`;

// Server-side functions for analysis
class WebVitalsAnalyzer {
  constructor() {
    this.reportDir = path.join(__dirname, '../../reports/web-vitals');
  }

  async ensureReportDir() {
    try {
      await fs.mkdir(this.reportDir, { recursive: true });
    } catch (error) {
      // Directory already exists
    }
  }

  // Analyze a collection of metrics
  analyzeMetrics(metricsArray) {
    if (!metricsArray || metricsArray.length === 0) {
      return null;
    }

    const analysis = {
      totalSamples: metricsArray.length,
      metrics: {},
      summary: {
        good: 0,
        needsImprovement: 0,
        poor: 0
      },
      recommendations: []
    };

    // Analyze each metric
    Object.keys(THRESHOLDS).forEach(metric => {
      const values = metricsArray
        .map(m => m[metric])
        .filter(v => v !== undefined && v !== null);

      if (values.length > 0) {
        const sorted = values.sort((a, b) => a - b);
        const ratings = values.map(v => this.getMetricRating(metric, v));
        
        analysis.metrics[metric] = {
          count: values.length,
          min: Math.min(...values),
          max: Math.max(...values),
          average: Math.round(values.reduce((sum, v) => sum + v, 0) / values.length),
          median: sorted[Math.floor(sorted.length / 2)],
          p75: sorted[Math.floor(sorted.length * 0.75)],
          p90: sorted[Math.floor(sorted.length * 0.90)],
          p95: sorted[Math.floor(sorted.length * 0.95)],
          ratings: {
            good: ratings.filter(r => r === 'good').length,
            needsImprovement: ratings.filter(r => r === 'needs-improvement').length,
            poor: ratings.filter(r => r === 'poor').length
          }
        };

        // Update overall summary
        analysis.summary.good += analysis.metrics[metric].ratings.good;
        analysis.summary.needsImprovement += analysis.metrics[metric].ratings.needsImprovement;
        analysis.summary.poor += analysis.metrics[metric].ratings.poor;
      }
    });

    // Generate recommendations
    analysis.recommendations = this.generateRecommendations(analysis.metrics);

    return analysis;
  }

  getMetricRating(metric, value) {
    const threshold = THRESHOLDS[metric];
    if (!threshold) return 'unknown';

    if (value <= threshold.good) return 'good';
    if (value <= threshold.needsImprovement) return 'needs-improvement';
    return 'poor';
  }

  generateRecommendations(metrics) {
    const recommendations = [];

    // LCP recommendations
    if (metrics.LCP && metrics.LCP.p75 > THRESHOLDS.LCP.good) {
      recommendations.push({
        metric: 'LCP',
        priority: metrics.LCP.p75 > THRESHOLDS.LCP.needsImprovement ? 'high' : 'medium',
        issue: \`LCP is \${metrics.LCP.p75}ms (75th percentile)\`,
        suggestions: [
          'Optimize server response times',
          'Implement resource preloading for above-the-fold content',
          'Optimize and compress images',
          'Remove render-blocking resources',
          'Use a Content Delivery Network (CDN)'
        ]
      });
    }

    // FID recommendations
    if (metrics.FID && metrics.FID.p75 > THRESHOLDS.FID.good) {
      recommendations.push({
        metric: 'FID',
        priority: metrics.FID.p75 > THRESHOLDS.FID.needsImprovement ? 'high' : 'medium',
        issue: \`FID is \${metrics.FID.p75}ms (75th percentile)\`,
        suggestions: [
          'Break up long-running JavaScript tasks',
          'Optimize JavaScript execution and reduce main thread work',
          'Reduce JavaScript bundle size',
          'Implement code splitting',
          'Use web workers for heavy computations'
        ]
      });
    }

    // CLS recommendations
    if (metrics.CLS && metrics.CLS.p75 > THRESHOLDS.CLS.good) {
      recommendations.push({
        metric: 'CLS',
        priority: metrics.CLS.p75 > THRESHOLDS.CLS.needsImprovement ? 'high' : 'medium',
        issue: \`CLS is \${metrics.CLS.p75} (75th percentile)\`,
        suggestions: [
          'Add size attributes to images and video elements',
          'Reserve space for ads and dynamic content',
          'Avoid inserting content above existing content',
          'Use CSS transform animations instead of animating layout properties',
          'Ensure fonts are loaded before text is rendered'
        ]
      });
    }

    // TTFB recommendations
    if (metrics.TTFB && metrics.TTFB.p75 > THRESHOLDS.TTFB.good) {
      recommendations.push({
        metric: 'TTFB',
        priority: 'medium',
        issue: \`TTFB is \${metrics.TTFB.p75}ms (75th percentile)\`,
        suggestions: [
          'Optimize server configuration and response times',
          'Use a CDN for static assets',
          'Implement proper caching strategies',
          'Optimize database queries',
          'Consider server-side rendering (SSR) optimizations'
        ]
      });
    }

    return recommendations;
  }

  generateReport(analysis) {
    let report = '# Web Vitals Analysis Report\\n\\n';
    report += \`Generated on: \${new Date().toISOString()}\\n\`;
    report += \`Total Samples: \${analysis.totalSamples}\\n\\n\`;

    // Summary
    report += '## Overall Performance Summary\\n\\n';
    const total = analysis.summary.good + analysis.summary.needsImprovement + analysis.summary.poor;
    const goodPercent = Math.round((analysis.summary.good / total) * 100);
    const needsImprovementPercent = Math.round((analysis.summary.needsImprovement / total) * 100);
    const poorPercent = Math.round((analysis.summary.poor / total) * 100);

    report += \`- 🟢 Good: \${analysis.summary.good} (\${goodPercent}%)\\n\`;
    report += \`- 🟡 Needs Improvement: \${analysis.summary.needsImprovement} (\${needsImprovementPercent}%)\\n\`;
    report += \`- 🔴 Poor: \${analysis.summary.poor} (\${poorPercent}%)\\n\\n\`;

    // Metrics details
    report += '## Core Web Vitals Details\\n\\n';
    report += '| Metric | Count | Average | Median | P75 | P90 | P95 | Good | Needs Improvement | Poor |\\n';
    report += '|--------|-------|---------|--------|-----|-----|-----|------|------------------|------|\\n';

    Object.entries(analysis.metrics).forEach(([metric, data]) => {
      const unit = ['CLS'].includes(metric) ? '' : 'ms';
      report += \`| \${metric} | \${data.count} | \${data.average}\${unit} | \${data.median}\${unit} | \${data.p75}\${unit} | \${data.p90}\${unit} | \${data.p95}\${unit} | \${data.ratings.good} | \${data.ratings.needsImprovement} | \${data.ratings.poor} |\\n\`;
    });

    // Recommendations
    if (analysis.recommendations.length > 0) {
      report += '\\n## Optimization Recommendations\\n\\n';
      analysis.recommendations.forEach(rec => {
        const priorityIcon = rec.priority === 'high' ? '🔴' : rec.priority === 'medium' ? '🟡' : '🟢';
        report += \`### \${priorityIcon} \${rec.metric} - \${rec.priority.toUpperCase()} Priority\\n\\n\`;
        report += \`**Issue**: \${rec.issue}\\n\\n\`;
        report += '**Suggested Optimizations**:\\n';
        rec.suggestions.forEach(suggestion => {
          report += \`- \${suggestion}\\n\`;
        });
        report += '\\n';
      });
    } else {
      report += '\\n## Optimization Recommendations\\n\\n✅ All Core Web Vitals are performing well!\\n\\n';
    }

    return report;
  }

  async saveReport(analysis) {
    await this.ensureReportDir();
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Save JSON data
    const jsonFilename = \`web-vitals-analysis-\${timestamp}.json\`;
    await fs.writeFile(
      path.join(this.reportDir, jsonFilename),
      JSON.stringify(analysis, null, 2)
    );

    // Save markdown report
    const report = this.generateReport(analysis);
    const mdFilename = \`web-vitals-report-\${timestamp}.md\`;
    await fs.writeFile(path.join(this.reportDir, mdFilename), report);

    console.log(\`\\n📊 Web Vitals analysis saved to:\`);
    console.log(\`- reports/web-vitals/\${jsonFilename}\`);
    console.log(\`- reports/web-vitals/\${mdFilename}\`);

    return { jsonFilename, mdFilename };
  }
}

// Export for use in applications
module.exports = {
  WEB_VITALS_CLIENT_CODE,
  WebVitalsAnalyzer,
  THRESHOLDS
};

// CLI functionality
async function main() {
  console.log('📊 Web Vitals Analysis Tool\\n');
  
  const analyzer = new WebVitalsAnalyzer();
  
  // For demonstration, create some sample data
  const sampleMetrics = Array.from({ length: 100 }, () => ({
    LCP: 1000 + Math.random() * 3000,
    FID: 50 + Math.random() * 200,
    CLS: Math.random() * 0.3,
    TTFB: 300 + Math.random() * 1000,
    url: 'http://localhost:5173/',
    timestamp: new Date().toISOString()
  }));

  console.log(\`Analyzing \${sampleMetrics.length} sample metrics...\\n\`);
  
  const analysis = analyzer.analyzeMetrics(sampleMetrics);
  
  if (analysis) {
    console.log('📈 Analysis Summary:');
    console.log(\`- Total samples: \${analysis.totalSamples}\`);
    console.log(\`- Good performance: \${analysis.summary.good} metrics\`);
    console.log(\`- Needs improvement: \${analysis.summary.needsImprovement} metrics\`);
    console.log(\`- Poor performance: \${analysis.summary.poor} metrics\`);
    
    if (analysis.recommendations.length > 0) {
      console.log(\`\\n🔧 \${analysis.recommendations.length} optimization recommendations available\`);
    }

    await analyzer.saveReport(analysis);
  } else {
    console.log('❌ No metrics to analyze');
  }
}

if (require.main === module) {
  main().catch(console.error);
}