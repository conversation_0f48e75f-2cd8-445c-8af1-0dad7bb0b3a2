
-- Create a user_preferences table to store user-specific settings including ad-free status
CREATE TABLE IF NOT EXISTS public.user_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  ad_free BOOLEAN DEFAULT false,
  theme TEXT DEFAULT 'light',
  font_size TEXT DEFAULT 'medium',
  created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(user_id)
);

-- Enable Row Level Security
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;

-- Create policy for users to view their own preferences
CREATE POLICY "select_own_preferences" ON public.user_preferences
  FOR SELECT
  USING (user_id = auth.uid());

-- Create policy for users to update their own preferences
CREATE POLICY "update_own_preferences" ON public.user_preferences
  FOR UPDATE
  USING (user_id = auth.uid());

-- Create policy for edge functions to upsert preferences
CREATE POLICY "insert_preferences" ON public.user_preferences
  FOR INSERT
  WITH CHECK (true);
