import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_ANON_KEY');
}

interface ContributionInput {
  content: string;
}

async function getAuthenticatedUser(req: Request, supabase: SupabaseClient) {
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    console.error('Auth error or no user for create-contribution:', authError);
    throw { message: 'User not authenticated or auth error.', status: 401 };
  }
  return user;
}

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed. Use POST.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 405,
    });
  }

  try {
    const supabase = createClient(supabaseUrl!, supabaseAnonKey!, {
      global: { headers: { Authorization: req.headers.get('Authorization')! } },
      auth: { persistSession: false }
    });

    const user = await getAuthenticatedUser(req, supabase);

    const url = new URL(req.url);
    const storyId = url.searchParams.get('story_id');

    if (!storyId) {
      return new Response(JSON.stringify({ error: 'story_id query parameter is required.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    const body: ContributionInput = await req.json();

    if (!body.content || typeof body.content !== 'string' || body.content.trim() === '') {
      return new Response(JSON.stringify({ error: 'Content is required and must be a non-empty string.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }

    const { data: rpcResult, error: rpcError } = await supabase.rpc('add_contribution_to_story', {
      p_story_id: storyId,
      p_user_id: user.id,
      p_content: body.content.trim(),
    });

    if (rpcError) {
      console.error('RPC call to add_contribution_to_story failed:', rpcError);
      return new Response(JSON.stringify({ error: 'Failed to add contribution.', details: rpcError.message }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      });
    }
    
    // The RPC function returns an array of rows, even if it's just one.
    // And it includes error_type and error_message columns.
    if (!rpcResult || rpcResult.length === 0) {
         console.error('RPC call to add_contribution_to_story returned no result or empty array');
         return new Response(JSON.stringify({ error: 'Failed to process contribution (empty RPC result).' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
         });
    }

    const resultRow = rpcResult[0];

    if (resultRow.error_type) {
      let statusCode = 400; // Default for business logic errors from DB function
      if (resultRow.error_type === 'NOT_FOUND') statusCode = 404;
      if (resultRow.error_type === 'STORY_NOT_ACTIVE') statusCode = 403;
      if (resultRow.error_type === 'CONSECUTIVE_CONTRIBUTION') statusCode = 403;
      if (resultRow.error_type === 'WORD_LIMIT_EXCEEDED') statusCode = 400;
      if (resultRow.error_type === 'MAX_CONTRIBUTORS_REACHED') statusCode = 403;
      if (resultRow.error_type === 'EMPTY_CONTENT') statusCode = 400;
      if (resultRow.error_type === 'DATABASE_ERROR') statusCode = 500;


      return new Response(JSON.stringify({ error: resultRow.error_message, code: resultRow.error_type }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: statusCode,
      });
    }

    // Successfully created, return the contribution details (excluding error fields)
    const { error_type, error_message, ...contributionData } = resultRow;

    return new Response(JSON.stringify(contributionData), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 201, // Created
    });

  } catch (err) {
    console.error('Error processing create-contribution request:', err);
    const status = typeof err.status === 'number' ? err.status : 500;
    return new Response(JSON.stringify({ error: err.message || 'An unexpected error occurred.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status,
    });
  }
}); 