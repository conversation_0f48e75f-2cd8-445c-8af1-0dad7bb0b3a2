import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import Stripe from 'https://esm.sh/stripe@10.17.0?target=deno&deno-std=0.132.0';
import { corsHeaders } from '../_shared/cors.ts';
import { supabaseAdmin } from '../_shared/supabaseAdmin.ts';

const stripe = new Stripe(Deno.env.get('STRIPE_TEST_SECRET_KEY')!, {
  httpClient: Stripe.createFetchHttpClient(),
  apiVersion: '2024-04-10',
});

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { user_id, user_email, price_id, success_url, cancel_url } = await req.json();

    if (!user_id) {
      throw new Error('User ID is required.');
    }
    if (!user_email) {
      throw new Error('User email is required.');
    }
    if (!price_id) {
      throw new Error('Price ID is required.');
    }
    if (!success_url) {
        throw new Error('Success URL is required.');
    }
    if (!cancel_url) {
        throw new Error('Cancel URL is required.');
    }
    
    // Get or create Stripe customer
    let customerId: string;
    
    // First, check if we already have a customer ID stored for this user
    const { data: existingSubscription } = await supabaseAdmin
      .from('subscriptions')
      .select('stripe_customer_id')
      .eq('user_id', user_id)
      .single();
    
    if (existingSubscription?.stripe_customer_id) {
      // Use existing customer
      customerId = existingSubscription.stripe_customer_id;
    } else {
      // Create new customer in Stripe
      const customer = await stripe.customers.create({ 
        email: user_email, 
        metadata: { user_id } 
      });
      customerId = customer.id;
    }

    const session = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      mode: 'subscription',
      line_items: [
        {
          price: price_id,
          quantity: 1,
        },
      ],
      customer: customerId, // Associate with existing or new Stripe customer
      success_url: success_url,
      cancel_url: cancel_url,
      metadata: {
        user_id: user_id,
      },
    });

    return new Response(JSON.stringify({ sessionId: session.id }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return new Response(JSON.stringify({ error: errorMessage }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    });
  }
});
