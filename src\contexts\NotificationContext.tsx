import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from './auth'; // Assuming useAuth provides the current user
import { useToast } from '@/hooks/use-toast';
import { PostgrestError, RealtimeChannel } from '@supabase/supabase-js';

// Mirroring the DB structure, content will be parsed
export interface AppNotification {
  id: string;
  user_id: string;
  story_id?: string | null;
  contribution_id?: string | null;
  actor_id?: string | null;
  type: string;
  content: { // Parsed from JSONB
    message: string;
    actor_username?: string;
    story_title?: string;
  };
  is_read: boolean;
  created_at: string;
  read_at?: string | null;
}

interface NotificationContextType {
  notifications: AppNotification[];
  unreadCount: number;
  loading: boolean;
  fetchNotifications: () => Promise<void>;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider = ({ children }: { children: ReactNode }) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [notifications, setNotifications] = useState<AppNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [notificationsChannel, setNotificationsChannel] = useState<RealtimeChannel | null>(null);

  const parseDbNotification = (dbNotification: any): AppNotification => {
    let parsedContent = { message: 'Notification content missing.' };
    if (typeof dbNotification.content === 'string') {
      try {
        parsedContent = JSON.parse(dbNotification.content);
      } catch (e) { console.error('Failed to parse notification content string:', e); }
    } else if (typeof dbNotification.content === 'object' && dbNotification.content !== null) {
      parsedContent = dbNotification.content;
    }

    return {
      ...dbNotification,
      content: parsedContent,
    } as AppNotification;
  };

  const fetchNotifications = useCallback(async () => {
    if (!user) return;
    setLoading(true);
    try {
      const { data, error, count } = await supabase
        .from('notifications')
        .select('*', { count: 'exact' })
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(50); // Fetch latest 50

      if (error) throw error;
      if (data) {
        const parsedNotifications = data.map(parseDbNotification);
        setNotifications(parsedNotifications);
        const unread = parsedNotifications.filter(n => !n.is_read).length; // Recalculate based on fetched data
        // The count from query gives total rows, not unread count directly unless filtered.
        // For unread count, it might be better to do a separate count query for is_read = false.
        // Or, fetch all unread, then fetch some read ones if needed to fill up to 50.
        // For now, this count is based on the latest 50.
        // Let's get a more accurate unread count:
        const { count: unreadResultCount, error: unreadError } = await supabase
          .from('notifications')
          .select('id', { count: 'exact', head: true })
          .eq('user_id', user.id)
          .eq('is_read', false);
        if (unreadError) console.error('Error fetching unread count:', unreadError);
        setUnreadCount(unreadResultCount || 0);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
      toast({ title: 'Error', description: 'Could not fetch notifications.', variant: 'destructive' });
    }
    setLoading(false);
  }, [user, toast]);

  useEffect(() => {
    if (user) {
      fetchNotifications();
    } else {
      // Clear notifications if user logs out
      setNotifications([]);
      setUnreadCount(0);
    }
  }, [user, fetchNotifications]);

  // Real-time subscription for new notifications
  useEffect(() => {
    if (notificationsChannel) {
      supabase.removeChannel(notificationsChannel).then(() => setNotificationsChannel(null));
    }

    if (user) {
      const channel = supabase
        .channel(`notifications-${user.id}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'notifications',
            filter: `user_id=eq.${user.id}`,
          },
          (payload) => {
            console.log('New notification received:', payload);
            const newNotification = parseDbNotification(payload.new);
            setNotifications(prev => [newNotification, ...prev].slice(0, 50)); // Keep list to 50
            if (!newNotification.is_read) {
              setUnreadCount(prev => prev + 1);
            }
            toast({
              title: newNotification.content.story_title ? `Update: ${newNotification.content.story_title}` : 'New Notification',
              description: newNotification.content.message,
              duration: 5000,
            });
          }
        )
        .subscribe((status, err) => {
          if (status === 'SUBSCRIBED') {
            console.log(`Subscribed to new notifications for user ${user.id}`);
          } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
            console.error(`Notification subscription error for user ${user.id}:`, err);
          }
        });
      setNotificationsChannel(channel);
    }

    return () => {
      if (notificationsChannel) {
        supabase.removeChannel(notificationsChannel);
      }
    };
  }, [user, toast, notificationsChannel]); // Added notificationsChannel to dependencies

  const markAsRead = async (notificationId: string) => {
    if (!user) return;
    try {
      const { data, error } = await supabase
        .from('notifications')
        .update({ is_read: true, read_at: new Date().toISOString() })
        .eq('id', notificationId)
        .eq('user_id', user.id) // Ensure user can only update their own
        .select(); // Select to get the updated row back

      if (error) throw error;
      if (data && data.length > 0) {
        const updatedNotification = parseDbNotification(data[0]);
        setNotifications(prev => prev.map(n => n.id === notificationId ? updatedNotification : n));
        setUnreadCount(prev => Math.max(0, prev - 1)); // Decrement if it was unread
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
      toast({ title: 'Error', description: 'Could not mark notification as read.', variant: 'destructive' });
    }
  };

  const markAllAsRead = async () => {
    if (!user || unreadCount === 0) return;
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true, read_at: new Date().toISOString() })
        .eq('user_id', user.id)
        .eq('is_read', false);

      if (error) throw error;
      // Refetch or update local state optimistically/realistically
      setNotifications(prev => prev.map(n => ({ ...n, is_read: true, read_at: new Date().toISOString() })));
      setUnreadCount(0);
      toast({ title: 'Success', description: 'All notifications marked as read.' });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      toast({ title: 'Error', description: 'Could not mark all notifications as read.', variant: 'destructive' });
    }
  };

  return (
    <NotificationContext.Provider value={{ notifications, unreadCount, loading, fetchNotifications, markAsRead, markAllAsRead }}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
}; 