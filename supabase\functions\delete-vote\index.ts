import { serve } from 'https://deno.land/std@0.177.0/http/server.ts';
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { corsHeaders } from '../_shared/cors.ts';

const supabaseUrl = Deno.env.get('SUPABASE_URL');
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY');

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Missing SUPABASE_URL or SUPABASE_ANON_KEY for delete-vote function.');
}

async function getAuthenticatedUser(req: Request, supabase: SupabaseClient) {
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    console.error('Auth error or no user for delete-vote:', authError);
    throw { message: 'User not authenticated or auth error.', status: 401 };
  }
  return user;
}

serve(async (req: Request) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  if (req.method !== 'DELETE') {
    return new Response(JSON.stringify({ error: 'Method not allowed. Use DELETE.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 405,
    });
  }

  try {
    const supabase = createClient(supabaseUrl!, supabaseAnonKey!, {
      global: { headers: { Authorization: req.headers.get('Authorization')! } },
      auth: { persistSession: false }
    });

    const user = await getAuthenticatedUser(req, supabase);

    const url = new URL(req.url);
    const storyId = url.searchParams.get('story_id');

    if (!storyId) {
      return new Response(JSON.stringify({ error: 'story_id query parameter is required.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      });
    }
    
    // Optional: Check if story exists before attempting to delete a vote for it.
    // This provides a more specific "story not found" vs "vote not found".
    const { data: storyData, error: storyCheckError } = await supabase
      .from('stories')
      .select('id')
      .eq('id', storyId)
      .maybeSingle();

    if (storyCheckError) {
        console.error("Error checking story existence during vote deletion:", storyCheckError);
        // Decide if to proceed or return 500. For now, proceed, RLS on votes will be the main guard.
    }
    if (!storyCheckError && !storyData) {
        return new Response(JSON.stringify({ error: 'Story not found.' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 404,
        });
    }


    const { error, count } = await supabase
      .from('votes')
      .delete({ count: 'exact' }) // Request count of deleted rows
      .eq('story_id', storyId)
      .eq('user_id', user.id); // RLS also enforces this, but good to be explicit

    if (error) {
      console.error('Supabase delete vote error:', error);
      return new Response(JSON.stringify({ error: error.message, details: error.details, hint: error.hint }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: (error as any).status || 500,
      });
    }

    if (count === 0) {
      // Vote not found for this user and story, or RLS prevented delete
      return new Response(JSON.stringify({ error: 'Vote not found or delete not permitted.' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 404,
      });
    }

    return new Response(null, { // 204 No Content
      headers: { ...corsHeaders },
      status: 204,
    });

  } catch (err) {
    console.error('Error processing delete-vote request:', err);
    const statusCode = typeof err.status === 'number' ? err.status : 500;
    return new Response(JSON.stringify({ error: err.message || 'An unexpected error occurred.' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: statusCode,
    });
  }
}); 