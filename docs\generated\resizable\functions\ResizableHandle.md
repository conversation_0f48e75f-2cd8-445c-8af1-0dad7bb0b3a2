[**Word by Word Story - UI Components v0.0.0**](../../README.md)

***

[Word by Word Story - UI Components](../../modules.md) / [resizable](../README.md) / ResizableHandle

# Function: ResizableHandle()

> **ResizableHandle**(`__namedParameters`): `Element`

Defined in: [src/components/ui/resizable.tsx:21](https://github.com/PatSul/word-by-word-story/blob/ee5b50587741b912c423b26c27bbe22bb0a1e058/src/components/ui/resizable.tsx#L21)

## Parameters

### \_\_namedParameters

`Omit`\<`HTMLAttributes`\<keyof `HTMLElementTagNameMap`\>, `"id"` \| `"onFocus"` \| `"onBlur"` \| `"onClick"` \| `"onPointerDown"` \| `"onPointerUp"`\> & `object` & `object` & `object`

## Returns

`Element`
