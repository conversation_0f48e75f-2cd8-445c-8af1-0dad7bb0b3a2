import { useState, useEffect, useCallback } from "react";
import { SystemMaintenanceState } from "@/contexts/auth/types";
import { safeJsonParse } from "@/lib/utils";

// LocalStorage key for system maintenance
const MAINTENANCE_STORAGE_KEY = "system_maintenance_settings";

// Browser-compatible version that uses localStorage instead of Prisma
export const useSystemMaintenance = () => {
  const [systemMaintenance, setSystemMaintenanceState] =
    useState<SystemMaintenanceState>({
      enabled: false,
      message: "",
      allowAdmins: false,
      kickOutUsers: false,
    });

  // Convert to useCallback to stabilize the reference
  const loadMaintenanceSettings = useCallback(async () => {
    try {
      // Get settings from localStorage
      const storedSettings = localStorage.getItem(MAINTENANCE_STORAGE_KEY);

      if (storedSettings) {
        const defaultSettings: SystemMaintenanceState = {
          enabled: false,
          message: "",
          allowAdmins: false,
          kickOutUsers: false,
        };

        const parsedSettings = safeJsonParse(storedSettings, defaultSettings);
        setSystemMaintenanceState({
          enabled: Boolean(parsedSettings.enabled),
          message: String(parsedSettings.message || ""),
          allowAdmins: Boolean(parsedSettings.allowAdmins),
          kickOutUsers: Boolean(parsedSettings.kickOutUsers),
        });
      }
    } catch (error) {
      console.error("Failed to load maintenance settings:", error);
    }
  }, []);

  // Load settings from localStorage on initial mount
  useEffect(() => {
    loadMaintenanceSettings();
  }, [loadMaintenanceSettings]);

  const setSystemMaintenance = useCallback(
    async (settings: SystemMaintenanceState) => {
      try {
        // Update state
        setSystemMaintenanceState(settings);

        // Save to localStorage for persistence
        localStorage.setItem(MAINTENANCE_STORAGE_KEY, JSON.stringify(settings));

        return;
      } catch (error) {
        console.error("Failed to update maintenance settings:", error);
        throw error;
      }
    },
    [],
  );

  return {
    systemMaintenance,
    setSystemMaintenance,
    loadMaintenanceSettings,
  };
};
